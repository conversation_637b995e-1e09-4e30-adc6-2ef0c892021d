#!/bin/sh
# Before bootstraping the resources account, you need to set the value of  "@aws-cdk/core:bootstrapQualifier": "<PROJECT_NAME>" in the cdk.json
export AWS_RES_ACCOUNT=************
export AWS_RES_PROFILE=${AWS_RES_ACCOUNT}_PA_DEVELOPER
export AWS_STAGE_ACCOUNT=************
export AWS_STAGE_PROFILE=${AWS_STAGE_ACCOUNT}_PA_DEVELOPER
export PROJECT_NAME=cora-oc
aws iam create-policy \
    --policy-name $PROJECT_NAME-service-resourcedeploy \
    --policy-document file://bootstrap/stage_policy.json \
    --profile $AWS_STAGE_PROFILE > /dev/null && \
cdk bootstrap \
    --profile ${AWS_STAGE_PROFILE} \
    --qualifier ${PROJECT_NAME} \
    --toolkit-stack-name ${PROJECT_NAME}-cdk \
    --trust ${AWS_RES_ACCOUNT} \
    --cloudformation-execution-policies "arn:aws:iam::"${AWS_STAGE_ACCOUNT}":policy/"${PROJECT_NAME}-service-resourcedeploy \
    aws://${AWS_STAGE_ACCOUNT}/eu-west-1
