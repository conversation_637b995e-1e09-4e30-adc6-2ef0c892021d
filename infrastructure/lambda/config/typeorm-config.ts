import { Kas<PERSON><PERSON><PERSON><PERSON><PERSON>ogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { RdsSecret, secretCache } from '../utils/secret-cache';
import { DataSource, LoggerOptions } from 'typeorm';
import { Class } from 'type-fest';

export async function getDbCredentials(logger: Kas<PERSON><PERSON><PERSON><PERSON><PERSON>ogger, aurora_secret_arn: string): Promise<RdsSecret> {
  logger.log(LogLevel.DEBUG, 'Try to get Aurora password');
  const rdsSecret = await secretCache.getSecret<RdsSecret>(aurora_secret_arn).catch((error) => {
    logger.log(LogLevel.ERROR, 'Failed to get aurora password');
    throw error;
  });
  logger.log(LogLevel.INFO, 'Successfully got Aurora password');
  return rdsSecret;
}

export async function createTypeORMDataSource(
  logger: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ogger,
  aurora_secret_arn: string,
  stage: string,
  entities: Class[],
  typeormLogging?: LoggerOptions,
): Promise<DataSource> {
  const dbCredentials = await getDbCredentials(logger, aurora_secret_arn);

  return new DataSource({
    type: 'postgres',
    host: dbCredentials.host,
    port: 5432,
    username: dbCredentials.username,
    password: dbCredentials.password,
    database: dbCredentials.dbname,
    synchronize: false,
    ssl: {
      rejectUnauthorized: false,
    },
    logging: typeormLogging ?? ['error'],
    entities: entities,
  }).initialize();
}
