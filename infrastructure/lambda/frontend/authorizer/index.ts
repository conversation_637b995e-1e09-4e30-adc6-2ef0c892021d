import { APIGatewayRequestAuthorizerEvent, APIGatewayAuthorizerResult } from 'aws-lambda';
import * as jwt from 'jsonwebtoken';
import { JwksClient } from 'jwks-rsa';
import { KasAuthEndpointResponse } from '../../../lib/types/kas-auth-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from '../../utils/utils';

const publicKeyEndpoint = getEnvVarWithAssert('PUBLIC_KEY_ENDPOINT');
const issuer = getEnvVarWithAssert('ISSUER');
const clientId = getEnvVarWithAssert('CLIENT_ID');
const kasAuthEndpointUrl = getEnvVarWithAssert('KAS_AUTH_ENDPOINT_URL');
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const stage = getEnvVarWithAssert('STAGE');

let publicKeyCache: string | undefined = undefined;

const logger = new KasLambdaLogger('frontendAuthorizer', LogLevel.TRACE);

// Create a new JWKS (JSON Web Key Set) client instance with the public key endpoint
const client = new JwksClient({
  jwksUri: publicKeyEndpoint,
});

// Asynchronously fetch the public key for a given Key ID (kid)
const fetchPublicKey = async (kid: string): Promise<string> => {
  const key = await client.getSigningKey(kid);
  return key.getPublicKey();
};

// Asynchronously get the public key for a given Key ID (kid) and cache it for future use
const getPublicKey = async (kid: string): Promise<string> => {
  if (!publicKeyCache) {
    publicKeyCache = await fetchPublicKey(kid);
  }
  return publicKeyCache;
};

// Asynchronously validate a JWT token using the public key, issuer
const validateToken = async (token: string): Promise<boolean> => {
  const options = {
    issuer,
  };

  // Decode the JWT token to access the header
  const decodedToken = jwt.decode(token, { complete: true });

  // Check if the decodedToken is valid and has a header with a kid property
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (!decodedToken?.header?.kid) {
    logger.log(LogLevel.WARN, 'Invalid token or missing kid in header');
    return false;
  }
  const kid = decodedToken.header.kid;

  const payload = decodedToken.payload as jwt.JwtPayload;
  if (payload.client_id !== clientId) {
    logger.log(LogLevel.WARN, `Invalid client_id in payload: ${JSON.stringify(payload)}`);
    return false;
  }

  try {
    const publicKey = await getPublicKey(kid);
    logger.log(LogLevel.INFO, `Validating token from publicKey: ${publicKey}`);

    jwt.verify(token, publicKey, options);
    return true;
  } catch (error: unknown) {
    if (error instanceof Error) {
      logger.log(LogLevel.WARN, `Token validation failed: ${String(error.message)}`);
    } else {
      logger.log(LogLevel.FATAL, 'Token validation failed: Unknown error');
    }

    // Refresh the public key and retry validation
    publicKeyCache = await fetchPublicKey(kid);
    try {
      jwt.verify(token, publicKeyCache, options);
      return true;
    } catch (innerError: unknown) {
      if (innerError instanceof Error) {
        logger.log(LogLevel.WARN, `Token validation failed: ${String(innerError.message)}`);
      } else {
        logger.log(LogLevel.FATAL, 'Token validation failed: Unknown error');
      }
      return false;
    }
  }
};

// Generate an AWS IAM policy with the specified principalId, effect, and resource

const generateDenyPolicy = (
  principalId: string,
  resourcePathWithoutMethod: string,
  errorMessage?: string,
): APIGatewayAuthorizerResult => {
  logger.log(LogLevel.DEBUG, 'Denying access');
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Deny',
          Resource: `${resourcePathWithoutMethod}*`,
        },
      ],
    },
    context: {
      errorMessage: errorMessage,
    },
  };
};
const generateAllowPolicy = (principalId: string, resourcePathWithoutMethod: string): APIGatewayAuthorizerResult => {
  //read only access
  logger.log(LogLevel.DEBUG, 'user has read only access');
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Allow',
          Resource: `${resourcePathWithoutMethod}GET/*`,
        },
      ],
    },
  };
};
// Extract the 'Authorization' cookie value from the cookieHeader string
const getAuthorizationFromCookie = (cookieHeader: string | undefined): string | undefined => {
  if (!cookieHeader) {
    return undefined;
  }

  const authCookieName = 'KasAuthorization-' + stage;

  // Split the cookieHeader into separate cookies and search for 'Authorization' cookie
  const cookies = cookieHeader.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.split('=');
    if (name.trim() === authCookieName) {
      return value.trim();
    }
  }

  // Return undefined if the 'Authorization' cookie is not found
  return undefined;
};

function checkIfUserHasAccess(data: KasAuthEndpointResponse): boolean {
  if (!data.kasApplications[applicationNameToAuthorize]) {
    logger.log(LogLevel.INFO, 'user has no access', {
      data: { apps: data.kasApplications, needed_application: applicationNameToAuthorize },
    });
    return false;
  }
  if (data.kasApplications[applicationNameToAuthorize]) {
    logger.log(LogLevel.DEBUG, 'user has access');
    return true;
  }
  return false;
}

async function getKasAuthUserData(token: string): Promise<KasAuthEndpointResponse> {
  logger.log(LogLevel.TRACE, 'getKasAuthUserData input', { data: token });
  logger.log(LogLevel.DEBUG, 'starting call to auth service', { data: { url: kasAuthEndpointUrl } });
  let response: Response;
  try {
    logger.log(LogLevel.DEBUG, 'fetching data from kasAuthEndpoint, first try', { data: { url: kasAuthEndpointUrl } });
    response = await fetch(kasAuthEndpointUrl, {
      method: 'GET',
      headers: {
        Authorization: token,
      },
    });
  } catch (error) {
    logger.log(LogLevel.ERROR, 'error during first kasAuth fetch try', {
      data: { error: error },
    });
    logger.log(LogLevel.DEBUG, 'fetching data from kasAuthEndpoint, second try', { data: { url: kasAuthEndpointUrl } });
    response = await fetch(kasAuthEndpointUrl, {
      method: 'GET',
      headers: {
        Authorization: token,
      },
    });
  }

  if (!response.ok) {
    if (response.status === 406) {
      const data = await response.json();
      logger.log(LogLevel.WARN, 'Too many groups', { data: data });
      throw new Error('too many groups');
    }
    logger.log(LogLevel.ERROR, 'error response from kathAuthEndpoint', {
      data: { status: response.status, text: response.statusText, url: response.url },
    });
    throw new Error('Unauthorized, error from kas auth');
  }
  const data = (await response.json()) as KasAuthEndpointResponse | undefined;
  if (!data) {
    throw new Error('Got invalid/empty answer from KAS Auth Endpoint');
  }
  logger.log(LogLevel.TRACE, 'getKasAuthUserData output', { data: data });
  return data;
}

// Lambda function handler for AWS API Gateway custom authorizer
export const handler = async (event: APIGatewayRequestAuthorizerEvent): Promise<APIGatewayAuthorizerResult> => {
  logger.log(LogLevel.TRACE, `Event: ${JSON.stringify(event, null, 2)}`);

  const principalId = 'user';
  // Get the 'cookie' header from the event
  const cookie = event.headers?.cookie ?? event.headers?.Cookie;
  // Get the authorizationHeader based on the event type and the 'cookie' header
  const authorizationHeader = getAuthorizationFromCookie(cookie);

  // Parse the event to get the resource path for the whole API
  const httpMethod = event.requestContext.httpMethod;
  const resourcePathWithoutMethod = `${event.methodArn.split(httpMethod)[0]}`; //arn:aws:execute-api:eu-west-1:897501187430:83pfuyq8vc/prod/

  // If there is no authorizationHeader, generate a deny policy
  if (!authorizationHeader) {
    logger.log(LogLevel.WARN, 'Missing authorization header');
    throw new Error('Unauthorized');
  }

  // If the authorizationHeader is present, validate the token
  const isValid = await validateToken(authorizationHeader);

  //if token is not valid, generate a deny policy
  if (!isValid) {
    logger.log(LogLevel.WARN, 'Invalid token');
    throw new Error('Unauthorized');
  }

  try {
    // If the token is valid, check if the user is in the access group
    const userDataFromAuthEndpoint = await getKasAuthUserData(authorizationHeader);
    const userHasAccess = checkIfUserHasAccess(userDataFromAuthEndpoint);

    logger.log(
      LogLevel.INFO,
      `${userHasAccess ? 'Allow' : 'Deny'} because the user ${userHasAccess ? 'is' : 'is not'} in the access group`,
    );

    // If user is in access group, generate an allow policy; otherwise, generate a deny policy
    if (!userHasAccess) {
      return generateDenyPolicy(principalId, resourcePathWithoutMethod, 'User not in access group');
    } else {
      const policy = generateAllowPolicy(principalId, resourcePathWithoutMethod);
      logger.log(LogLevel.DEBUG, `Policy: ${JSON.stringify(policy, null, 2)}`);
      policy.context = {
        userAttributes: JSON.stringify(userDataFromAuthEndpoint),
      };
      return policy;
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'error', { data: error });
    if (error instanceof Error) {
      logger.log(LogLevel.ERROR, `Error: ${error.message}`);
      return generateDenyPolicy(principalId, resourcePathWithoutMethod, error.message);
    }
  }

  return generateDenyPolicy(principalId, resourcePathWithoutMethod);
};
