import { OAuthCredentials, secretCache } from './secret-cache';
import { JwtTokenCache } from './jwtTokenCache';
import { VehicleConfigurationPvmsNext } from '../../lib/types/pvms-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

interface OmConfigApiAdapterProps {
  omConfigApiSecretArn: string;
  logger: KasLambdaLogger;
}

export class OmConfigApiAdapter {
  private readonly jwtTokenCache: JwtTokenCache;
  private readonly secretArn: string;
  private readonly logger: Kas<PERSON>ambdaLogger;

  public constructor(props: OmConfigApiAdapterProps) {
    this.secretArn = props.omConfigApiSecretArn;
    this.logger = props.logger;
    this.jwtTokenCache = new JwtTokenCache({ oAuthSecretArn: props.omConfigApiSecretArn, logger: this.logger });
  }

  private static getApiQueryPath(vguid: string): string {
    return `/saml/vehicle/v1/configurations/VehicleSet('${vguid}')?$expand=VehicleOption/OptionExclusive,VehicleOption/OptionIndividual,VehicleOption/OptionCustomTailoring,VehicleOption/OptionZoffer,VehicleOption/OptionPackage/PackageDefaultContent,VehicleOption/OptionLocal,VehicleTag,VehicleWLTPHeader,VehicleWLTPBody/WLTPBodyWLTPBodyRecord/WLTPBodyRecordWLTPDataRecord/WLTPDataRecordWLTPValue`;
  }

  public async getConfig(vguid: string, region: string): Promise<VehicleConfigurationPvmsNext | null> {
    try {
      const cachedSecret = await secretCache.getSecret<OAuthCredentials>(this.secretArn);
      this.logger.log(LogLevel.INFO, 'Begin to fetch the jwt token');
      const token = await this.jwtTokenCache.getToken();
      this.logger.log(LogLevel.INFO, 'Successfully fetched the jwt token');

      const result = await fetch(cachedSecret.base_url + OmConfigApiAdapter.getApiQueryPath(vguid), {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          region: region,
          Authorization: `Bearer ${token.access_token}`,
          ...cachedSecret.headers,
        },
      });

      if (result.ok) {
        const config = (await result.json()) as { d?: VehicleConfigurationPvmsNext };
        if (!config.d) {
          this.logger.log(LogLevel.ERROR, 'Empty configuration response from API', { data: config });
        }
        return config.d ?? null;
      } else {
        // Token expiry:
        const expiresAt: number = // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
          JSON.parse(Buffer.from(token.access_token.split('.')[1], 'base64').toString()).exp as number;
        this.logger.log(
          LogLevel.ERROR,
          'Error getting configuration for purchase intention, API call result statuscode was ' + result.status,
          {
            data: `${result.statusText} - (IDP-Token Expires: ${expiresAt}. Remaining: ${
              expiresAt * 1000 - new Date().getTime()
            })`,
          },
        );
        return null;
      }
    } catch (err) {
      this.logger.log(LogLevel.ERROR, 'Error getting configuration for purchase intention, API call error', {
        data: err,
      });
      return null;
    }
  }
}
