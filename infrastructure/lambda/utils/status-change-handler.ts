import { Kas<PERSON><PERSON><PERSON><PERSON><PERSON>ogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource, Repository, EntityManager } from 'typeorm';
import { Context, SQSEvent, SQSHandler, SQSBatchResponse } from 'aws-lambda';
import { NewCarOrderAuditTrailModel } from '../../lib/entities/new-car-order-audit-trail-model';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../lib/entities/new-car-order-model';
import {
  OneVmsEventKey,
  SQSBatchResponseWithError,
  DefaultEventHandlerResult,
  NotificationStatus,
  OneVmsSourceSystemKey,
  NotificationKafkaEvent,
  UserOrderActionEventHandlerEvent,
} from '../../lib/types/process-steering-types';
import { ObjectValidator } from '../../lib/utils/object-validation';
import { EventHandlerContext } from '../backend/process-steering/event-handler/event-handler-context';
import { eventToNotification, unparseableEventToNotification } from './process-steering-helpers';
import { createSqsEventHandlerWithInitLogger } from './sqs-event-handler';
import {
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from './utils-typeorm';
import { NcoExportActionType } from '../backend/export-nco/types';
import { OneVmsEventHandlerKey } from '../../lib/utils/constants';
import { validateSqsEvent } from './validation-helpers';

export interface StatusChangeHandlerConfig<T extends UserOrderActionEventHandlerEvent> {
  sqsEventValidator: ObjectValidator<T>;
  handlerKey: OneVmsEventHandlerKey;
  exportAction: NcoExportActionType;
  eventKey: OneVmsEventKey;
  successLogMessage: (id: string) => string;
}

/**
 * Creates a standardized SQS status change handler.
 * Use this function to create a handler which solely focuses on changing status codes of a NewCarOrder.
 *
 * @template T The type of the SQS event that extends UserOrderActionEventHandlerEvent.
 * @param {StatusChangeHandlerConfig<T>} config Handler Configuration (see {@link StatusChangeHandlerConfig})
 * @returns {SQSHandler} AWS Lambda handler for processing `SQSEvent`.
 *
 * @example
 * const handler = createStatusChangeHandler({
 *   sqsEventValidator,
 *   handlerKey: OneVmsEventHandlerKey.MOVE_TO_INVENTORY,
 *   exportAction: NcoExportActionType.MOVE_TO_INVENTORY,
 *   eventKey: OneVmsEventKey.MOVE_TO_INVENTORY,
 *   successLogMessage: (id) => `NewCarOrder ${id} successfully moved to inventory.`,
 * });
 */

export function createStatusChangeHandler<T extends UserOrderActionEventHandlerEvent>(
  config: StatusChangeHandlerConfig<T>,
): SQSHandler {
  type HandleStatusChangeWithMessageId = T & { messageId: string };
  type HandleStatusChangeWithError = HandleStatusChangeWithMessageId & { errorMessage: string };
  // Initialize context once per Lambda
  EventHandlerContext.init(config.handlerKey, [
    NewCarOrderModel,
    NewCarOrderAuditTrailModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
  ]);

  // Core logic, parameterized by config
  const core = async (
    event: SQSEvent, // eslint-disable-next-line @typescript-eslint/no-unused-vars
    context: Context,
    logger: KasLambdaLogger,
  ): Promise<SQSBatchResponse | void> => {
    const unParseableEvents: Partial<HandleStatusChangeWithError>[] = [];
    const expectedFailedEvents: HandleStatusChangeWithError[] = [];
    const unexpectedFailedEvents: HandleStatusChangeWithError[] = [];
    const successfulEvents: HandleStatusChangeWithMessageId[] = [];
    const sqsBatchResponse: SQSBatchResponseWithError = {
      batchItemFailures: [],
    };

    // Parse and validate
    const inboundEvents = event.Records.map((record) => {
      try {
        const body = JSON.parse(record.body) as T;
        //Check if sqs content is valid
        const isValid = validateSqsEvent<T>(body, config.sqsEventValidator, logger);
        if (!isValid) {
          const msg = 'SQS record is not valid, skipping';
          logger.log(LogLevel.ERROR, msg, { data: body, correlationId: body.transaction_id });
          unParseableEvents.push({ ...body, messageId: record.messageId, errorMessage: msg });
          return undefined;
        }
        return { ...body, messageId: record.messageId };
      } catch (e) {
        const msg = 'Failed to parse sqs record body, skipping';
        logger.log(LogLevel.ERROR, msg, { data: { error: e, body: record.body } });
        const partial = JSON.parse(record.body) as T;
        unParseableEvents.push({ ...partial, messageId: record.messageId, errorMessage: msg });
        return undefined;
      }
    }).filter(Boolean) as HandleStatusChangeWithMessageId[];

    // Outbound mapping
    let outboundStatusUpdateStatement: StatusUpdateStatement = {};
    try {
      const mappings = await EventHandlerContext.getOutboundEventMappings();
      const mapping = mappings.find(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
        (m) => m.event_result === DefaultEventHandlerResult.SUCCESS,
      );
      if (mapping) {
        outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(mapping);
      }
    } catch (e) {
      const msg = 'Failed to get outbound event mapping config';
      logger.log(LogLevel.ERROR, msg, { data: e });
      await EventHandlerContext.pushNotificationsToKafka(
        inboundEvents.map((inboundEvent) =>
          eventToNotification(inboundEvent, NotificationStatus.EVENT_HANDLER_NIO, msg),
        ),
      );
      // Everyone goes into the DLQ because no outboundMapping, no party.
      return {
        batchItemFailures: inboundEvents.map((inboundEvent) => ({
          itemIdentifier: inboundEvent.messageId,
          errorMessage: msg,
        })),
      };
    }

    // DataSource
    let dataSource: DataSource;
    let repo: Repository<NewCarOrderModel>;
    try {
      dataSource = await EventHandlerContext.getDataSource();
      repo = dataSource.getRepository(NewCarOrderModel);
    } catch (e) {
      const msg = 'Unexpected error. Datasource could not be initialized';
      logger.log(LogLevel.ERROR, msg, { data: e });
      await EventHandlerContext.pushNotificationsToKafka(
        inboundEvents.map((inboundEvent) =>
          eventToNotification(inboundEvent, NotificationStatus.EVENT_HANDLER_NIO, msg),
        ),
      );
      // Everyone goes into the DLQ because no DataSource, no party.
      return {
        batchItemFailures: inboundEvents.map((inboundEvent) => ({
          itemIdentifier: inboundEvent.messageId,
          errorMessage: msg,
        })),
      };
    }

    // Process each record
    for (const inboundEvent of inboundEvents) {
      logger.setObjectId(inboundEvent.nco_id);
      logger.setCorrelationId(inboundEvent.transaction_id);
      const userAttributes = inboundEvent.user_auth_context;
      const visibilityLevel =
        userAttributes.kasApplications[EventHandlerContext.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
      const ppnId = userAttributes.organizationId;

      if (!visibilityLevel) {
        const msg = 'Failed to get the visibility level';
        logger.log(LogLevel.ERROR, msg, { data: event });
        expectedFailedEvents.push({ ...inboundEvent, errorMessage: msg });
        continue;
      }

      try {
        const sourceNco = await repo.findOneBy({ pk_new_car_order_id: inboundEvent.nco_id });
        if (!sourceNco) {
          const msg = `Failed to find order with id: ${inboundEvent.nco_id}`;
          logger.log(LogLevel.ERROR, msg, { data: inboundEvent });
          expectedFailedEvents.push({ ...inboundEvent, errorMessage: msg });
          continue;
        }

        const validation = await EventHandlerContext.commonBusinessLogicValidation(inboundEvent, sourceNco, ppnId);
        if (!validation.valid) {
          expectedFailedEvents.push({ ...inboundEvent, errorMessage: validation.message! });
          continue;
        }

        await saveNcosWithAuditTrail(
          dataSource,
          [sourceNco.pk_new_car_order_id],
          config.exportAction,
          async (transactionManager: EntityManager) => {
            await transactionManager.getRepository(NewCarOrderModel).update(
              { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
              {
                changed_by_system: OneVmsSourceSystemKey.CORA,
                order_status_onevms_timestamp_last_change: new Date().toISOString(),
                modified_by: userAttributes.username,
                ...outboundStatusUpdateStatement,
              },
            );
            // Reload and return updated orders to ensure all orders were changed correctly
            return transactionManager.getRepository(NewCarOrderModel).find({
              where: { pk_new_car_order_id: sourceNco.pk_new_car_order_id, modified_by: userAttributes.username },
            });
          },
          logger,
          false,
        );
        logger.log(LogLevel.INFO, config.successLogMessage(sourceNco.pk_new_car_order_id));
        successfulEvents.push(inboundEvent);
      } catch (error) {
        const err = error as { message: string; name: string };
        if (
          err.name === 'QueryFailedError' &&
          err.message.includes('could not serialize access due to concurrent update')
        ) {
          const msg = 'Nco was changed by someone else since the event was created';
          expectedFailedEvents.push({ ...inboundEvent, errorMessage: msg });
          continue;
        }
        const msg = 'Unexpected database error occurred during transaction';
        logger.log(LogLevel.ERROR, msg, { data: error });
        unexpectedFailedEvents.push({ ...inboundEvent, errorMessage: msg });

        continue;
      }
    }

    // Notifications
    const successfulNotifications = successfulEvents.map((inboundEvent) =>
      eventToNotification(inboundEvent, NotificationStatus.EVENT_HANDLER_IO),
    );
    const expectedFailedNotifications = expectedFailedEvents.map((inboundEvent) =>
      eventToNotification(inboundEvent, NotificationStatus.EVENT_HANDLER_NIO, inboundEvent.errorMessage),
    );
    const unparseableNotifications = unParseableEvents.map((inboundEvent) =>
      unparseableEventToNotification(
        inboundEvent,
        config.eventKey,
        NotificationStatus.EVENT_HANDLER_NIO,
        inboundEvent.errorMessage,
      ),
    );
    // Unexpected fail events are not consumed and can be retried
    const unexpectedNotifications = unexpectedFailedEvents.map((inboundEvent) => {
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: inboundEvent.messageId,
        errorMessage: inboundEvent.errorMessage,
      });
      return eventToNotification(inboundEvent, NotificationStatus.EVENT_HANDLER_NIO, inboundEvent.errorMessage);
    });
    const notificationEvents: NotificationKafkaEvent[] = [
      ...successfulNotifications,
      ...expectedFailedNotifications,
      ...unparseableNotifications,
      ...unexpectedNotifications,
    ];

    try {
      await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
    } catch (e) {
      logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
    }

    return sqsBatchResponse;
  };

  // Wrap with init-logger
  return async (event, context) =>
    createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, core);
}
