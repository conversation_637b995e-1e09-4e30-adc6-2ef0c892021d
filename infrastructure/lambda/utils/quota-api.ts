import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { CoraNCOQuotaConsumeObj } from '../../lib/types/new-car-order-types';
import { QuotaApiConsumeBody, QuotaApiConsumeResponse } from '../../lib/types/quota-api-types';
import { JwtTokenCache, JwtTokenObject } from './jwtTokenCache';
import { OAuthCredentials, secretCache } from './secret-cache';

interface QuotaApiAdapterProps {
  quotaApiSecretArn: string;
  logger: KasLambdaLogger;
}

export class QuotaApiAdapter {
  private readonly jwtTokenCache: JwtTokenCache;
  private readonly secretArn: string;
  private readonly logger: KasLambdaLogger;

  public constructor(props: QuotaApiAdapterProps) {
    this.secretArn = props.quotaApiSecretArn;
    this.logger = props.logger;
    this.jwtTokenCache = new JwtTokenCache({ oAuthSecretArn: props.quotaApiSecretArn, logger: this.logger });
  }

  public async consumeQuota(
    props: {
      nco: CoraNCOQuotaConsumeObj;
      mockApi?: boolean;
      correlationId: string;
    },
    logger: KasLambdaLogger,
  ): Promise<boolean> {
    props = { mockApi: false, ...props };
    const cachedSecret = await secretCache.getSecret<OAuthCredentials>(this.secretArn);
    const token = await this.jwtTokenCache.getToken();

    const body: QuotaApiConsumeBody = {
      orders_to_consume: [
        {
          new_car_order_id: props.nco.pk_new_car_order_id,
          model_year: props.nco.model_year,
          model_type_code: props.nco.model_type,
          importer_number: parseInt(props.nco.importer_number),
          dealer_number: parseInt(props.nco.dealer_number),
          quota_month: props.nco.quota_month,
        },
      ],
    };
    const result = props.mockApi
      ? await this.postQuotaConsumeMocked({ body: body }, logger)
      : await this.postQuotaConsume(
          { body: body, cachedSecret: cachedSecret, token: token, correlationId: props.correlationId },
          logger,
        );
    if (!result?.quota_consumed) {
      logger.log(LogLevel.DEBUG, 'Quota API Result body', { data: result });
      result &&
        logger.log(LogLevel.ERROR, 'Error consuming quota, API returned validation err', {
          data: result.validation_response?.errors[props.nco.pk_new_car_order_id],
        });
      return false;
    } else if (result.validation_response?.consumed_new_car_orders[0] !== props.nco.pk_new_car_order_id) {
      logger.log(LogLevel.ERROR, 'Error consuming quota. API response is invalid');
      return false;
    } else {
      logger.log(LogLevel.INFO, `Successfully consumed quota${props.mockApi ? ' (mocked!)' : ''}`, { data: result });
      return true;
    }
  }

  public async consumeMultipleQuota(
    props: {
      ncoList: CoraNCOQuotaConsumeObj[];
      mockApi?: boolean;
      correlationId: string;
    },
    logger: KasLambdaLogger,
  ): Promise<QuotaApiConsumeResponse | undefined> {
    logger.log(LogLevel.TRACE, 'consumeMultipleQuota input', { data: props });
    const cachedSecret = await secretCache.getSecret<OAuthCredentials>(this.secretArn);
    const token = await this.jwtTokenCache.getToken();

    const ordersToConsume = props.ncoList.map((nco) => ({
      new_car_order_id: nco.pk_new_car_order_id,
      model_year: nco.model_year,
      model_type_code: nco.model_type,
      importer_number: parseInt(nco.importer_number),
      dealer_number: parseInt(nco.dealer_number),
      quota_month: nco.quota_month,
    }));

    const body: QuotaApiConsumeBody = {
      orders_to_consume: ordersToConsume,
    };

    const result = props.mockApi
      ? await this.postQuotaConsumeMocked({ body: body }, logger)
      : await this.postQuotaConsume(
          { body: body, cachedSecret: cachedSecret, token: token, correlationId: props.correlationId },
          logger,
        );

    if (!result?.quota_consumed) {
      logger.log(LogLevel.DEBUG, 'Quota API Result body', { data: result });
      result &&
        logger.log(LogLevel.ERROR, 'Error consuming quota, API returned validation err', {
          data: result.validation_response?.errors,
        });
      return undefined;
    } else {
      logger.log(LogLevel.INFO, `Successfully consumed quota for batch${props.mockApi ? ' (mocked!)' : ''}`, {
        data: result,
      });
      return result;
    }
  }

  private async postQuotaConsume(
    props: {
      body: QuotaApiConsumeBody;
      cachedSecret: OAuthCredentials;
      token: JwtTokenObject;
      timeout?: number;
      correlationId: string;
    },
    logger: KasLambdaLogger,
  ): Promise<QuotaApiConsumeResponse | null> {
    props = { timeout: 60000, ...props };
    logger.log(LogLevel.TRACE, 'postQuotaConsume input', { data: props });

    const bodyString = JSON.stringify(props.body);
    logger.log(LogLevel.DEBUG, 'Sending request to Quota API', { data: bodyString });

    try {
      const fetchCommand: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': `${Buffer.byteLength(bodyString)}`,
          ...props.cachedSecret.headers,
          Authorization: `Bearer ${props.token.access_token}`,
          'x-kas-request-id': props.correlationId,
        },
        body: bodyString,
        signal: AbortSignal.timeout(props.timeout!),
      };

      const url = props.cachedSecret.base_url + '/quota/consumption';
      logger.log(LogLevel.TRACE, 'Quota Api consume request', { data: { url: url, req: fetchCommand } });

      const result = await fetch(url, fetchCommand);
      if (result.ok) {
        const res = (await result.json()) as QuotaApiConsumeResponse;
        logger.log(LogLevel.TRACE, 'postQuotaConsume output', { data: res });
        return res;
      } else {
        logger.log(LogLevel.ERROR, 'Error consuming quota, API call result statuscode was ' + result.status, {
          data: {
            statusText: result.statusText,
            body: await result.text(),
            url: result.url,
          },
        });
        return null;
      }
    } catch (err) {
      logger.log(LogLevel.ERROR, 'Error consuming quota, API call error', { data: err });
      return null;
    }
  }

  private async postQuotaConsumeMocked(
    props: {
      body: QuotaApiConsumeBody;
    },
    logger: KasLambdaLogger,
  ): Promise<QuotaApiConsumeResponse | null> {
    return new Promise<QuotaApiConsumeResponse | null>((resolve) => {
      logger.log(LogLevel.TRACE, 'postQuotaConsumeMocked input', { data: props });
      const params = props.body.orders_to_consume[0];
      const defaultMockedResponse: QuotaApiConsumeResponse = {
        quota_consumed: true,
        validation_response: {
          processed_quota_entries: props.body.orders_to_consume.length,
          errors: {},
          consumed_new_car_orders: props.body.orders_to_consume.map((nco) => nco.new_car_order_id),
        },
      };
      //return null for some arbitrary quota month
      if (params.quota_month === '2999-01') {
        const res = null;
        logger.log(LogLevel.INFO, 'Mocked quota API returned null', { data: res });
        resolve(res);
      }
      //return validation error for some arbitrary quota month
      else if (params.quota_month === '2888-01') {
        const res: QuotaApiConsumeResponse = {
          quota_consumed: false,
          validation_response: {
            processed_quota_entries: 0,
            consumed_new_car_orders: [],
            errors: {
              [params.new_car_order_id]: [
                {
                  quota_month: params.quota_month,
                  model_type_group: params.model_type_code,
                  model_year: parseInt(params.model_year),
                  dealer_number: params.dealer_number.toString(),
                  importer_number: params.importer_number.toString(),
                  error_message: 'Quota Count lower then consumed Quota (Quota count: 0 - Already Consumed: 5)',
                  error_code: 'ERROR_0403',
                },
              ],
            },
          },
        };
        logger.log(LogLevel.INFO, 'Mocked quota API returned response with validation errs', { data: res });
        resolve(res);
      }
      //return success otherwise
      else {
        logger.log(LogLevel.TRACE, 'postQuotaConsumeMocked output', { data: defaultMockedResponse });
        resolve(defaultMockedResponse);
      }
    });
  }
}
