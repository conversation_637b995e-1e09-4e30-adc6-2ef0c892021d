import { SendMessageBatchCommand, SendMessageBatchRequestEntry, SQSClient } from '@aws-sdk/client-sqs';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  InboundEventHandlerEvent,
  BatchItemFailureWithError,
  SpecialStatusCode,
  NotificationStatus,
  NotificationKafkaEvent,
  OneVmsEventKey,
} from '../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../lib/utils/constants';
const SQS_BATCH_SIZE = 10;

export async function dispatchToSqsQueue(
  events: { handlerKey: OneVmsEventHandlerKey; event: InboundEventHandlerEvent }[],
  messageIdBySubTransaction: Map<string, string>,
  queueMap: Record<OneVmsEventHandlerKey, string>,
  sqsClient: SQSClient,
  logger: KasLambdaLogger,
): Promise<BatchItemFailureWithError[]> {
  const batchFailures: BatchItemFailureWithError[] = [];
  const groupedEventMap = new Map<OneVmsEventHandlerKey, InboundEventHandlerEvent[]>();

  // Group events by handler key
  logger.log(LogLevel.INFO, `Dispatching ${events.length} events into SQS queues.`);
  for (const { handlerKey, event } of events) {
    if (!groupedEventMap.has(handlerKey)) {
      groupedEventMap.set(handlerKey, []);
    }
    groupedEventMap.get(handlerKey)!.push(event);
  }

  for (const [handlerKey, groupedEvents] of groupedEventMap.entries()) {
    const queueUrl = queueMap[handlerKey];

    for (let i = 0; i < groupedEvents.length; i += SQS_BATCH_SIZE) {
      const batch = groupedEvents.slice(i, i + SQS_BATCH_SIZE);
      const entries: SendMessageBatchRequestEntry[] = batch.map((event) => ({
        Id: event.sub_transaction_id,
        MessageBody: JSON.stringify(event),
      }));

      try {
        const result = await sqsClient.send(new SendMessageBatchCommand({ QueueUrl: queueUrl, Entries: entries }));
        result.Failed?.forEach((failed) => {
          const sourceMsgId = messageIdBySubTransaction.get(failed.Id!);
          if (sourceMsgId) {
            const errorMessage = failed.Message ?? 'Unknown error while dispatching Event';
            batchFailures.push({ itemIdentifier: sourceMsgId, errorMessage });
          }
        });
      } catch (err) {
        logger.log(LogLevel.ERROR, `SQS dispatch failed for queue ${queueUrl}`, { data: err });
        batch.forEach((event) => {
          const sourceMsgId = messageIdBySubTransaction.get(event.sub_transaction_id);
          if (sourceMsgId) batchFailures.push({ itemIdentifier: sourceMsgId, errorMessage: (err as Error).message });
        });
      }
    }
  }

  return batchFailures;
}

/**
 * In order to compare order status codes with status codes of inbound mappings, we need to consider the following rules:
 * 1. If the expected status code is "*" (`SpecialStatusCode.ALL`), it should match any actual status code.
 * 2. If the expected status code ends with "*", it should match any actual status code that starts with the same prefix.
 * 3. Otherwise, the expected and actual status codes must match exactly.
 *
 * Compares two status codes for equality, with support for wildcard matching.
 * @param expectedCode The expected status code, which may contain wildcards.
 * @param actualCode The actual status code to compare against.
 */

export function matchStatusCode(expectedCode: string, actualCode: string): boolean {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (expectedCode === SpecialStatusCode.ALL) {
    return true;
  } else if (expectedCode.endsWith('*')) {
    const prefix = expectedCode.slice(0, -1);
    return actualCode.startsWith(prefix);
  }
  return expectedCode === actualCode;
}

/**
 * Helper function to transform any InboundEventHandlerEvent outcome to a NotificationEvent.
 * @param event The inbound event of the event handler.
 * @param status The notification status which should be exported.
 * @param details Some specific notification details about the event outcome.
 * @returns
 */
export function eventToNotification(
  event: InboundEventHandlerEvent,
  status: NotificationStatus,
  details?: unknown,
): NotificationKafkaEvent {
  return {
    transaction_id: event.transaction_id,
    sub_transaction_id: event.sub_transaction_id,
    event_type: event.event_type,
    nco_id: event.nco_id,
    action_by: event.user_auth_context?.username ?? event.source_system,
    action_at: new Date().toISOString(),
    source_system: event.source_system,
    status,
    details,
  };
}

/**
 * Transforms a partially parsed (or unparseable) inbound event into a complete NotificationKafkaEvent.
 * This function is designed for events that do not fully conform to the expected structure. It provides
 * fallback values (e.g. 'unknown') for any missing properties so that a valid NotificationKafkaEvent is returned.
 *
 * @param event       The partially parsed event, which may be missing some fields.
 * @param event_type  A fallback event type (typically a OneVmsEventKey) to use if the event's type is unavailable.
 * @param status      The notification status that should be exported.
 * @param details     Optional details to provide additional context or error information.
 * @returns           A complete NotificationKafkaEvent with all required fields populated using fallback values where needed.
 */
export function unparseableEventToNotification(
  event: Partial<InboundEventHandlerEvent>,
  event_type: OneVmsEventKey,
  status: NotificationStatus,
  details?: unknown,
): NotificationKafkaEvent {
  return {
    transaction_id: event.transaction_id ?? 'unknown',
    sub_transaction_id: event.sub_transaction_id ?? 'unknown',
    event_type: event.event_type ?? event_type,
    nco_id: event.nco_id ?? 'unknown',
    action_by: event.user_auth_context?.username ?? 'unknown',
    action_at: new Date().toISOString(),
    source_system: event.source_system ?? 'unkown',
    status,
    details,
  };
}
