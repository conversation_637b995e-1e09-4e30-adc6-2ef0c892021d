import { OAuthCredentials, secretCache } from './secret-cache';
import { btoa } from './base64';
import { HttpClient, HttpClientResponse } from './http-client';
import { GeneralError } from './errors';
import { RequestOptions } from 'https';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { exponentialBackoff } from './utils';

export interface JwtTokenProps {
  oAuthSecretArn: string;
  logger: KasLambdaLogger;
}

export interface JwtTokenObject {
  access_token: string;
  expires_in: number; //in seconds!
  token_type: string;
}

export class JwtTokenCache {
  private readonly oAuthSecretArn: string;
  private token: Promise<JwtTokenObject>;
  private expiresAt = 0; //unix timestamp in milliseconds
  private readonly logger: KasLambdaLogger;

  public constructor(props: JwtTokenProps) {
    this.oAuthSecretArn = props.oAuthSecretArn;
    // load token asynchronously on init
    this.token = this.fetchToken();
    this.logger = props.logger;
  }

  public async getToken(): Promise<JwtTokenObject> {
    // if current token is outdated
    if (this.expiresAt > 0 && new Date().getTime() >= this.expiresAt) {
      this.token = this.fetchToken();
    }
    return this.token;
  }

  private setExpiresAt(token: JwtTokenObject): void {
    this.expiresAt = new Date().getTime() + token.expires_in * 1000;
  }

  private async fetchToken(): Promise<JwtTokenObject> {
    const oAuthSecret = await secretCache.getSecret<OAuthCredentials>(this.oAuthSecretArn);
    let data = `grant_type=client_credentials&client_id=${oAuthSecret.client_id}&client_secret=${oAuthSecret.client_secret}`;
    if (oAuthSecret.scope?.length) {
      data += `&scope=${oAuthSecret.scope.join(' ')}`;
    }
    if (oAuthSecret.body) {
      data +=
        '&' +
        Object.entries(oAuthSecret.body)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&');
    }

    let authorization = {};
    if (oAuthSecret.add_basic_auth_on_token_request) {
      authorization = {
        Authorization: 'Basic ' + btoa(`${oAuthSecret.client_id}:${oAuthSecret.client_secret}`),
      };
    }
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...authorization,
      },
    };

    try {
      const { body } = await this.requestWithRetry(HttpClient.createUrl(oAuthSecret.token_url, ''), options, data);
      const token = JSON.parse(body) as JwtTokenObject;
      this.setExpiresAt(token);
      return token;
    } catch (error) {
      throw new GeneralError({
        message: 'fetching the token failed',
        causedBy: error,
      });
    }
  }

  private async requestWithRetry(
    url: URL,
    options: RequestOptions,
    data: string,
    maxRetries = 3,
  ): Promise<HttpClientResponse> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await HttpClient.request(this.logger, url, options, data);
      } catch (error) {
        this.logger.log(LogLevel.WARN, `Token request #${attempt} failed`, { data: error });
        if (attempt > maxRetries) {
          throw new GeneralError({
            message: `Request failed after attempts ${attempt}`,
            causedBy: error,
          });
        } else {
          const waitTime: number = exponentialBackoff(attempt);
          this.logger.log(LogLevel.WARN, `Request failed. Retrying in ${waitTime} ms`);
          await new Promise((resolve) => setTimeout(resolve, waitTime)); // exponential backoff with delay
        }
      }
    }
    throw new GeneralError({
      message: 'Max retries reached',
    });
  }
}
