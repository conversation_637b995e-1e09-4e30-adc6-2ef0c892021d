import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { mockClient } from 'aws-sdk-client-mock';

import * as utils_typeorm from '../utils/utils-typeorm';
import { CoraNCOBaseApiRequest } from '../../lib/types/new-car-order-types';
import { CoraMdDealer, CoraMdImporter, CoraMdOrderType, CoraMdShippingCode } from '../../lib/types/masterdata-types';
import { KasLambdaLogger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { ModelTypeVisibilityModel } from '../../lib/entities/model-type-visibility-model';
import { SpecialStatusCode } from '../../lib/types/process-steering-types';

const ddbMock = mockClient(DynamoDBClient);
const smMock = mockClient(SecretsManagerClient);
jest.mock('./secret-cache');
jest.mock('./quota-api', () => {
  return {
    QuotaApiAdapter: jest.fn().mockImplementation(() => {
      return {
        // eslint-disable-next-line @typescript-eslint/promise-function-async
        consumeQuota: (): Promise<boolean> => new Promise((res) => res(true)),
      };
    }),
  };
});

beforeEach(() => {
  ddbMock.reset();
  smMock.reset();
  jest.resetModules();
});

const shippingCode: CoraMdShippingCode | undefined = {
  pk_shipping_code: '1',
  created_at: '2023-10-11T08:18:49.252Z',
  created_by: 'ffyerqw',
  dealers: [],
  description: 'Delivery ',
  dlr_is_blacklist: true,
  importers: [],
  imp_is_blacklist: true,
  is_deactivated: false,
  modified_at: '2023-10-23T08:22:04.653Z',
  modified_by: 'p358886',
};

const orderType: CoraMdOrderType | undefined = {
  pk_order_type: 'KF',
  created_at: '2023-10-05T11:16:44.550Z',
  created_by: 'ffyerqw',
  description: 'Kundenfahrzeug',
  dlr_is_visible: true,
  importers: [],
  imp_is_blacklist: true,
  is_customer_related: true,
  is_deactivated: false,
  modified_at: 'null',
  modified_by: 'null',
};
const importer: CoraMdImporter | undefined = {
  pk_importer_number: '4500000',
  code: 'TEST',
  display_name: 'Porsche Cars North America Inc.',
  modified_at: '2024-05-14T07:19:35.204Z',
  modified_by: 'ffyerqw',
  port_codes: ['SEA', 'SWA'],
};
const dealer: CoraMdDealer | undefined = {
  pk_importer_number: '66000001',
  sk_dealer_number: '75922921',
  display_name: 'PCC  Porsche Centre Cancun',
  modified_at: '2024-04-06T13:21:21.820Z',
  modified_by: 'boss_sync',
};

const modelTypes: ModelTypeVisibilityModel[] | undefined = [
  {
    cnr: '911',
    importer_number: '4500000',
    model_type: '911',
    my4: '1234',
    role: 'importer',
    valid_from: '2024-05-14T07:19:35.204Z',
    created_by: 'UNIT_TEST',
    modified_by: 'UNIT_TEST',
  },
];

const newCarOrder: Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id'> = {
  cnr: 'C99',
  configuration: {
    ordered_options: [
      {
        option_id: '0Q',
        option_type: 'Exterior Color',
        option_validity: {
          added_to_order_timestamp: '2024-08-27T11:56:00.753Z',
        },
      },
      {
        option_id: '0Q',
        option_type: 'Top Color',
        option_validity: {
          added_to_order_timestamp: '2024-08-27T11:56:00.753Z',
        },
      },
      {
        option_id: 'AA',
        option_type: 'Interior Color',
        option_validity: {
          added_to_order_timestamp: '2024-08-27T11:56:00.753Z',
        },
      },
    ],
  },
  configuration_expire: '',
  configuration_signature: 'testsig',
  dealer_number: '2701001',
  importer_code: 'TEST',
  importer_number: '4500000',
  model_type: 'Y1AAI1',
  model_year: '2025',
  order_type: 'LF',
  quota_month: '2024-10',
  shipping_code: '1',
};

const visibilityLevel: string = '';

const customerRelatedOt: boolean = true;
const isUserImp: boolean = true;
const skipScDisabledCheck: boolean = false;
const skipOtDisabledCheck: boolean = false;

describe('performValidation()', () => {
  it('validate is true', () => {
    const res = utils_typeorm.performValidation(
      {
        customerRelatedOt,
        dealer,
        importer,
        modelTypes,
        newCarOrder,
        orderType,
        shippingCode,
        visibilityLevel,
        isUserImp,
        skipScDisabledCheck,
        skipOtDisabledCheck,
      },
      new KasLambdaLogger('test'),
    );
    expect(res).toStrictEqual({ valid: true });
  });

  it('dealer is not allowed to change port code', () => {
    const res = utils_typeorm.performValidation(
      {
        customerRelatedOt,
        dealer: {
          ...dealer,
          alternative_port_codes: undefined,
          standard_port_code: 'STANDARD_PORT_CODE',
        },
        importer,
        modelTypes,
        newCarOrder: {
          ...newCarOrder,
          receiving_port_code: 'DIFFERENT_PORT_CODE',
        },
        orderType,
        shippingCode,
        visibilityLevel,
        isUserImp: false,
        skipScDisabledCheck,
        skipOtDisabledCheck,
      },
      new KasLambdaLogger('test'),
    );
    expect(res).toStrictEqual({ valid: false, error: 'Dealers are not allowed to change standard port code' });
  });

  it('dealer should be allowed to save order if standard port code is undefined and receiving port code is empty string', () => {
    const res = utils_typeorm.performValidation(
      {
        customerRelatedOt,
        dealer: {
          ...dealer,
          alternative_port_codes: undefined,
          standard_port_code: undefined,
        },
        importer,
        modelTypes,
        newCarOrder: {
          ...newCarOrder,
          receiving_port_code: '',
        },
        orderType,
        shippingCode,
        visibilityLevel,
        isUserImp: false,
        skipScDisabledCheck,
        skipOtDisabledCheck,
      },
      new KasLambdaLogger('test'),
    );
    expect(res).toStrictEqual({ valid: true });
  });
});

describe('combineStatusMappingWithOutboundMapping()', () => {
  it('should return undefined if mappingStatusUpdateStatement is missing and outbound contains CUSTOM', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: SpecialStatusCode.CUSTOM,
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, undefined, false);

    expect(result).toBeUndefined();
  });

  it('should use status from status mapping when outbound is CUSTOM', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: SpecialStatusCode.CUSTOM,
      order_status_onevms_error_code: SpecialStatusCode.CUSTOM,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, false);

    expect(result).toEqual({
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    });
  });

  it('should use status from outbound mapping when not CUSTOM', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: 'STATUS2',
      order_status_onevms_error_code: 'ERROR2',
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, false);

    expect(result).toEqual({
      order_status_onevms_code: 'STATUS2',
      order_status_onevms_error_code: 'ERROR2',
    });
  });

  it('should mix CUSTOM and non-CUSTOM statuses', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: SpecialStatusCode.CUSTOM,
      order_status_onevms_error_code: 'ERROR2',
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, false);

    expect(result).toEqual({
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR2',
    });
  });

  it('should handle invoice mapping when doInvoiceMapping is true', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_invoice_onevms_code: SpecialStatusCode.CUSTOM,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
      order_invoice_onevms_code: 'INVOICE1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, true);

    expect(result).toEqual({
      order_invoice_onevms_code: 'INVOICE1',
    });
  });

  it('should set invoice status to NONE when CUSTOM and mapping is missing', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_invoice_onevms_code: SpecialStatusCode.CUSTOM,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, true);

    expect(result).toEqual({
      order_invoice_onevms_code: SpecialStatusCode.NONE,
    });
  });

  it('should not include invoice mapping when doInvoiceMapping is false', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_invoice_onevms_code: SpecialStatusCode.CUSTOM,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
      order_invoice_onevms_code: 'INVOICE1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, false);

    expect(result).toEqual({});
  });

  it('should only include defined values in result', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: undefined,
      order_status_onevms_error_code: 'ERROR2',
      order_invoice_onevms_code: undefined,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, true);

    expect(result).toEqual({
      order_status_onevms_error_code: 'ERROR2',
    });
  });

  it('should handle all fields being CUSTOM', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: SpecialStatusCode.CUSTOM,
      order_status_onevms_error_code: SpecialStatusCode.CUSTOM,
      order_invoice_onevms_code: SpecialStatusCode.CUSTOM,
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
      order_invoice_onevms_code: 'INVOICE1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, true);

    expect(result).toEqual({
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
      order_invoice_onevms_code: 'INVOICE1',
    });
  });

  it('should handle all fields being non-CUSTOM', () => {
    const outboundMapping: utils_typeorm.StatusUpdateStatement = {
      order_status_onevms_code: 'STATUS2',
      order_status_onevms_error_code: 'ERROR2',
      order_invoice_onevms_code: 'INVOICE2',
    };

    const statusMapping: utils_typeorm.CoraNCOOneVmsStatus = {
      order_status_onevms_code: 'STATUS1',
      order_status_onevms_error_code: 'ERROR1',
      order_invoice_onevms_code: 'INVOICE1',
    };

    const result = utils_typeorm.combineStatusMappingWithOutboundMapping(outboundMapping, statusMapping, true);

    expect(result).toEqual({
      order_status_onevms_code: 'STATUS2',
      order_status_onevms_error_code: 'ERROR2',
      order_invoice_onevms_code: 'INVOICE2',
    });
  });
});
