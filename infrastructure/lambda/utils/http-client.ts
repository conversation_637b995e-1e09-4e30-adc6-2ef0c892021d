import { get, request, RequestOptions } from 'https';
import { URL } from 'url';
import { IncomingHttpHeaders, IncomingMessage } from 'http';
import { join } from 'path';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

export interface HttpClientResponse {
  statusCode?: number;
  headers: IncomingHttpHeaders;
  body: string;
}

export type QueryObject =
  | URLSearchParams
  | string
  | Record<string, string | readonly string[]>
  | Iterable<[string, string]>
  | readonly [string, string][];

export class HttpError extends Error {
  public readonly headers: IncomingHttpHeaders;
  public readonly body: string;
  public readonly statusCode?: number;
  public readonly url?: string | URL;

  public constructor(headers: IncomingHttpHeaders, body: string, statusCode?: number, url?: string | URL) {
    super('Http request failed');
    this.headers = headers;
    this.body = body;
    this.statusCode = statusCode;
    this.url = url;
  }
}

export class HttpClient {
  public static async get(
    logger: KasL<PERSON>bdaLogger,
    url: string | URL,
    options: RequestOptions = {},
  ): Promise<HttpClientResponse> {
    return new Promise((resolve, reject) => {
      const req = get(url, { ...options }, (resp) => {
        HttpClient.httpRequestHandler(resp, url).then(resolve).catch(reject);
      });
      req.on('error', (err) => {
        logger.log(LogLevel.ERROR, `get for: ${url.toString()} could not be loaded`, { data: err });
        reject(err);
      });
    });
  }

  public static async request(
    logger: KasLambdaLogger,
    url: string | URL,
    options: RequestOptions,
    data: string,
  ): Promise<HttpClientResponse> {
    return new Promise((resolve, reject) => {
      const req = request(
        url,
        {
          ...options,
          timeout: 10000,
          headers: {
            ...options.headers,
            'Content-Length': Buffer.byteLength(data),
          },
        },
        (resp) => {
          HttpClient.httpRequestHandler(resp, url).then(resolve).catch(reject);
        },
      );
      req.on('error', (err) => {
        logger.log(LogLevel.ERROR, `request for: ${url.toString()} could not be loaded:`, { data: err });
        reject(err);
      });
      req.on('timeout', () => {
        req.destroy();
        logger.log(LogLevel.ERROR, `request for: ${url.toString()} has been terminated because of timeout`);
        reject(new Error('Request timed out'));
      });
      req.write(data);
      req.end();
    });
  }

  public static createUrl(urlStr: string, path: string, query?: QueryObject, skipEncodeing = false): URL {
    const url = new URL(urlStr);
    url.pathname = join(
      url.pathname,
      path
        .split('/')
        .map((folder) => (skipEncodeing ? folder : encodeURIComponent(folder)))
        .join('/'),
    );
    url.search = new URLSearchParams(query).toString();
    return url;
  }

  private static async httpRequestHandler(response: IncomingMessage, url?: string | URL): Promise<HttpClientResponse> {
    let body = '';
    for await (const chunk of response) {
      // A chunk of data has been received.
      body += chunk;
    }

    // The whole response has been received. Print out the result.
    const statusCode = response.statusCode;
    const headers = response.headers;
    if (!statusCode) {
      throw new HttpError(headers, body, statusCode, url);
    } else if (statusCode && (statusCode < 200 || statusCode >= 300)) {
      throw new HttpError(headers, body, statusCode, url);
    } else {
      return { body, statusCode, headers };
    }
  }
}
