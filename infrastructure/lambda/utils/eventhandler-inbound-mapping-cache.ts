import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource } from 'typeorm';
import { InboundProcessMappingModel } from '../../lib/entities/inbound-mapping-model';
import { OneVmsEventHandlerKey } from '../../lib/utils/constants';
import { matchStatusCode } from './process-steering-helpers';

type InboundMappingMatchInput = Omit<
  InboundProcessMappingModel,
  'id' | 'target_event_handler' | 'created_by' | 'modified_by'
>;
export class InboundMappingCache {
  private mappings: InboundProcessMappingModel[] = [];

  // Cache invalidation after 2 minutes. Adjust as needed.
  private lastLoadedAt: number = 0;
  private readonly ttlMs = 2 * 60 * 1000;

  public async init(dataSource: DataSource, logger: KasLambdaLogger, force = false): Promise<void> {
    const now = Date.now();
    if (!force && this.mappings.length > 0 && now - this.lastLoadedAt < this.ttlMs) return;
    this.lastLoadedAt = now;

    const inboundMappingRepo = dataSource.getRepository(InboundProcessMappingModel);
    this.mappings = await inboundMappingRepo.find();

    logger.log(LogLevel.INFO, `Inbound mapping cache initialized with ${this.mappings.length} entries.`);
  }

  public getHandlerKey(input: InboundMappingMatchInput, logger: KasLambdaLogger): OneVmsEventHandlerKey | undefined {
    const matching = this.mappings.filter(
      (mapping) =>
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        mapping.event === input.event &&
        mapping.source_system === input.source_system &&
        matchStatusCode(mapping.order_status_code, input.order_status_code) &&
        matchStatusCode(mapping.error_status_code, input.error_status_code) &&
        matchStatusCode(mapping.invoice_status_code, input.invoice_status_code),
    );

    if (matching.length === 0) {
      return undefined;
    } else if (matching.length > 1) {
      logger.log(
        LogLevel.WARN,
        'Multiple inbound mappings found for the same input. Picking the first one in line. This could be a possible mistake!',
        {
          data: {
            input,
            matchedMappings: matching,
            pickedMapping: matching[0],
          },
        },
      );
    }

    return matching[0].target_event_handler;
  }
}

export const inboundMappingCache = new InboundMappingCache();
