import { Client, ClientConfig } from 'pg';
import { RdsSecret } from './secret-cache';
import { KasLamb<PERSON>Logger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

export async function setupPgClientConnection(
  rdsSecret: RdsSecret,
  logger: KasLambdaLogger,
): Promise<Client | undefined> {
  const conParams: ClientConfig = {
    user: rdsSecret.username,
    password: rdsSecret.password,
    database: rdsSecret.dbname,
    host: rdsSecret.host,
    port: parseInt(rdsSecret.port, 10),
    ssl: {
      rejectUnauthorized: false,
    },
  };

  const pgClient = new Client(conParams);

  try {
    await pgClient.connect();
    return pgClient;
  } catch (error) {
    const errorMessage = 'PostgreSQL Client connection could not be established.';
    logger.log(LogLevel.ERROR, errorMessage, {
      data: { error: error, connectionParams: conParams },
    });
    return;
  }
}
