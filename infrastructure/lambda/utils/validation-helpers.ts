import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { CoraOrgRelModel } from '../../lib/types/boss-org-types';
import { CoraMdDealer, CoraMdImporter, CoraMdOrderType, CoraMdShippingCode } from '../../lib/types/masterdata-types';
import { Constants } from '../../lib/utils/constants';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { APIGatewayProxyEvent } from 'aws-lambda/trigger/api-gateway-proxy';
import { getPpnId, sanitizeApiGwEvent } from './api-helpers';
import { CoraNCOBaseApiRequest, CoraNCOConfiguration } from '../../lib/types/new-car-order-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from './utils';
import type { Repository } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../lib/entities/model-type-visibility-model';
import { ObjectValidator } from '../../lib/utils/object-validation';

export const extractDlrsAndImpsFromOrgs = (
  props: {
    orgs: CoraOrgRelModel[];
  },
  logger: KasLambdaLogger,
): { imps: CoraOrgRelModel[]; dlrs: CoraOrgRelModel[] } => {
  const dlrs: CoraOrgRelModel[] = [];
  const imps: CoraOrgRelModel[] = [];
  logger.log(LogLevel.TRACE, 'extractDlrsAndImpsFromOrgs input', { data: props });
  props.orgs.forEach((org) => {
    //only if both values are set we are looking at a dealer or importer org
    if (org.importer_number && org.dealer_number) {
      if (org.importer_number === org.dealer_number) {
        //found importer
        imps.push(org);
      } else {
        if (org.is_relevant_for_order_create) {
          //found dealer
          dlrs.push(org);
        }
      }
    }
  });
  const res = { imps, dlrs };
  logger.log(LogLevel.TRACE, 'extractDlrsAndImpsFromOrgs output', { data: res });
  return res;
};

export interface NcoValidationTables {
  coraOrgRelationTableName: string;
  shippingCodeTableName: string;
  orderTypeTableName: string;
  importerTableName: string;
  dealerTableName: string;
}

export interface NcoValidationParams {
  dynamoDb: DynamoDBClient;
  newCarOrder: Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id'>;
  ppnId: string;
  visibilityLevel: string;
  tables: NcoValidationTables;
  mtvRespoitory: Repository<ModelTypeVisibilityModel>;
  customerRelatedOt: boolean;
  skipScDisabledCheck?: boolean;
  skipOtDisabledCheck?: boolean;
}

export interface ValidationReturnType {
  valid: boolean;
  error?: string;
}

export const validateShippingCode = (props: {
  isUserImp: boolean;
  skipScDisabledCheck?: boolean;
  shippingCode?: CoraMdShippingCode;
  ncoImporterNumber: string;
  ncoDealerNumber: string;
}): ValidationReturnType => {
  if (!props.shippingCode || (props.shippingCode.is_deactivated && !props.skipScDisabledCheck)) {
    return { valid: false, error: 'Order params do not match masterdata (ShippingCode)' };
  }
  //validate shipping code importer if user is importer
  if (props.isUserImp) {
    const scHasOrderImporter = props.shippingCode.importers.includes(props.ncoImporterNumber);
    if (props.shippingCode.imp_is_blacklist && scHasOrderImporter) {
      return { valid: false, error: 'Shipping code not allowed for importer by blacklist' };
    } else if (!props.shippingCode.imp_is_blacklist && !scHasOrderImporter) {
      return { valid: false, error: 'Shipping code not allowed for importer by whitelist' };
    }
  }
  //validate shipping code dealer if user is a dealer
  else if (!props.skipScDisabledCheck) {
    const scHasOrderDealer = props.shippingCode.dealers.includes(props.ncoDealerNumber);
    if (props.shippingCode.dlr_is_blacklist && scHasOrderDealer) {
      return { valid: false, error: 'Shipping code not allowed for dealer by blacklist' };
    } else if (!props.shippingCode.dlr_is_blacklist && !scHasOrderDealer) {
      return { valid: false, error: 'Shipping code not allowed for dealer by whitelist' };
    }
  }
  return { valid: true };
};

export const validateOrderType = (props: {
  isUserImp: boolean;
  skipOtDisabledCheck?: boolean;
  orderType?: CoraMdOrderType;
  ncoImporterNumber: string;
  ncoDealerNumber: string;
}): ValidationReturnType => {
  if (!props.orderType || (props.orderType.is_deactivated && !props.skipOtDisabledCheck)) {
    return { valid: false, error: 'Order params do not match masterdata (OrderType)' };
  }

  //validate order type for importer
  if (props.isUserImp) {
    const otHasOrderImporter = props.orderType.importers.includes(props.ncoImporterNumber);
    if (props.orderType.imp_is_blacklist && otHasOrderImporter) {
      return { valid: false, error: 'Order type not allowed for importer by blacklist' };
    } else if (!props.orderType.imp_is_blacklist && !otHasOrderImporter) {
      return { valid: false, error: 'Order type not allowed for importer by whitelist' };
    }
  }
  //validate order type for dealer
  else if (!props.skipOtDisabledCheck) {
    if (!props.orderType.dlr_is_visible) {
      return { valid: false, error: 'Order type not allowed for dealers' };
    }
  }
  return { valid: true };
};

export const validatePortCode = (props: {
  dealer?: CoraMdDealer;
  importer?: CoraMdImporter;
  isUserImp: boolean;
  skipOtDisabledCheck?: boolean;
  ncoReceivingPortCode?: string | null;
  ncoImporterNumber: string;
  ncoDealerNumber: string;
}): ValidationReturnType => {
  if (!props.importer || !props.dealer) {
    return { valid: false, error: 'Order params do not match masterdata (Imp/Dlr)' };
  }
  //validate port code
  // TODO Check this again
  if (
    !props.isUserImp &&
    props.ncoReceivingPortCode !== props.dealer.standard_port_code &&
    !(!props.ncoReceivingPortCode && props.dealer.standard_port_code === undefined)
  ) {
    return { valid: false, error: 'Dealers are not allowed to change standard port code' };
  } else if (
    props.isUserImp &&
    props.ncoReceivingPortCode &&
    props.ncoReceivingPortCode !== props.dealer.standard_port_code
  ) {
    if (
      !props.importer.port_codes?.includes(props.ncoReceivingPortCode) &&
      !props.dealer.alternative_port_codes?.includes(props.ncoReceivingPortCode)
    ) {
      return { valid: false, error: 'Port code is not standard and not in list of importer or dealer' };
    }
  }
  return { valid: true };
};

export const getPermissionForDealer = (
  props: {
    dealerNumber: string;
    authorizedOrgs: CoraOrgRelModel[];
  },
  logger: KasLambdaLogger,
): 'Importer' | 'Dealer' | undefined => {
  logger.log(LogLevel.TRACE, 'getPermissionForDealer input', { data: props });
  // A real dealer must have an importer number configured, dealer without importer number will return undefined in this function
  const _dealer = props.authorizedOrgs.find((org) => org.importer_number && org.dealer_number === props.dealerNumber);
  if (_dealer) {
    const res = props.authorizedOrgs.find((org) => org.dealer_number === _dealer.importer_number)
      ? 'Importer'
      : 'Dealer';
    logger.log(LogLevel.TRACE, 'getPermissionForDealer output', { data: res });
    return res;
  }
  const res = undefined;
  logger.log(LogLevel.TRACE, 'getPermissionForDealer output', res);
  return res;
};

export const getAllAuthorizedOrgs = async (
  props: {
    dynamoDb: DynamoDBClient;
    orgRelsTableName: string;
    ppnId: string;
    includeDeactivated?: boolean;
  },
  logger: KasLambdaLogger,
): Promise<CoraOrgRelModel[]> => {
  logger.log(LogLevel.TRACE, 'getAllAuthorizedOrgs input', { data: props });
  props = { includeDeactivated: false, ...props };
  //get all Orgs the user has access to
  const cmd = new QueryCommand({
    TableName: props.orgRelsTableName,
    IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    KeyConditionExpression: `${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk} = :value`,
    ExpressionAttributeValues: { ':value': props.ppnId },
  });

  try {
    logger.log(LogLevel.DEBUG, 'QueryCommand', { data: cmd });
    const dynamoResult = await props.dynamoDb.send(cmd);
    if (dynamoResult.Items) {
      if (props.includeDeactivated) {
        logger.log(LogLevel.TRACE, 'getAllAuthorizedOrgs output', { data: dynamoResult.Items });
        return dynamoResult.Items as CoraOrgRelModel[];
      }
      return (dynamoResult.Items as CoraOrgRelModel[]).filter((org) => !org.is_deactivated);
    } else {
      logger.log(LogLevel.TRACE, 'getAllAuthorizedOrgs output', { data: [] });
      return [];
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to query dynamodb`, { data: error });
    throw error;
  }
};

export const getAllowedDealersAndImportersWithEvent = async function (
  props: {
    dynamoDb: DynamoDBClient;
    event: APIGatewayProxyEvent;
    includeDeactivated?: boolean;
  },
  logger: KasLambdaLogger,
): Promise<{ uniqueDlrs: CoraOrgRelModel[]; uniqueImps: CoraOrgRelModel[] }> {
  logger.log(LogLevel.TRACE, 'getAllowedDealersAndImporters input', { data: props });
  const ppnId = getPpnId({ event: props.event }, logger);
  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event: props.event }, logger) });
    return { uniqueDlrs: [], uniqueImps: [] };
  }
  return getAllowedDealersAndImporters(
    { dynamoDb: props.dynamoDb, ppnId, includeDeactivated: props.includeDeactivated },
    logger,
  );
};

export const getAllowedDealersAndImporters = async function (
  props: {
    dynamoDb: DynamoDBClient;
    ppnId: string;
    includeDeactivated?: boolean;
  },
  logger: KasLambdaLogger,
): Promise<{ uniqueDlrs: CoraOrgRelModel[]; uniqueImps: CoraOrgRelModel[] }> {
  logger.log(LogLevel.TRACE, 'getAllowedDealersAndImporters input', { data: props });

  const tableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');

  const authorizedOrgs = await getAllAuthorizedOrgs(
    {
      dynamoDb: props.dynamoDb,
      orgRelsTableName: tableName,
      ppnId: props.ppnId,
      includeDeactivated: props.includeDeactivated,
    },
    logger,
  );
  const { dlrs, imps } = extractDlrsAndImpsFromOrgs({ orgs: authorizedOrgs }, logger);

  // Create array of unique dealers
  const seenDlrs = new Set<string>();
  const uniqueDlrs = dlrs.filter((dlr) => {
    if (!seenDlrs.has(dlr.dealer_number!)) {
      seenDlrs.add(dlr.dealer_number!);
      return true;
    }
    return false;
  });

  // Create array of unique importers.
  // Imps array will always be empty for a DLR and therefore the importers will be determined from the dlrs array.
  // TODO discuss if this is correct, seems wrong, consider the case of importer that is authorized for a dealer of another importer
  const seenImps = new Set<string>();
  const uniqueImps = (imps.length > 0 ? imps : dlrs).filter((item) => {
    if (!seenImps.has(item.importer_number!)) {
      seenImps.add(item.importer_number!);
      return true;
    }
    return false;
  });
  const _res = { uniqueDlrs, uniqueImps };
  logger.log(LogLevel.INFO, 'getAllowedDealersAndImporters output', { data: _res });
  return _res;
};

export const getAllowedDealers = async function (
  props: {
    dynamoDb: DynamoDBClient;
    event: APIGatewayProxyEvent;
    includeDeactivated?: boolean;
  },
  logger: KasLambdaLogger,
): Promise<CoraOrgRelModel[]> {
  return (await getAllowedDealersAndImportersWithEvent(props, logger)).uniqueDlrs;
};

export const getAllowedImporters = async function (
  props: {
    dynamoDb: DynamoDBClient;
    event: APIGatewayProxyEvent;
  },
  logger: KasLambdaLogger,
): Promise<CoraOrgRelModel[]> {
  return (await getAllowedDealersAndImportersWithEvent(props, logger)).uniqueImps;
};

/**
 *
 * Calculates Quotamonth validity for a given KccConfiguration
 *
 * @param config KccConfiguration in KAS Format
 * @returns Obj: {from: "YYYY-MM" (inclusive), until: "YYYY-MM" (inclusive)}
 */
export const getValidQuotaMonthForConfig = (
  props: {
    config: CoraNCOConfiguration;
    old_config?: CoraNCOConfiguration;
  },
  logger: KasLambdaLogger,
): { from: string; until: string } => {
  logger.log(LogLevel.TRACE, 'getValidQuotaMonthForConfig input', { data: props });
  const DAY_IN_MS = 1000 * 60 * 60 * 24;
  let maxMaterialLeadTime = new Date(0);
  let minFromOptionValidity = new Date(0);
  let minTillOptionValidity = new Date('9999-12-01'); // Large possible Date
  for (const option of props.config.ordered_options) {
    if (option.option_validity?.material_lead_time) {
      // We start with the timestamp when an option was added, or now if the option is new
      const _fromCalculationDate = new Date(
        props.old_config?.ordered_options.find((_o) => _o.option_id === option.option_id)?.option_validity
          ?.added_to_order_timestamp ?? Date.now(),
      );
      const _ml = new Date(
        _fromCalculationDate.getTime() + Number(option.option_validity.material_lead_time) * DAY_IN_MS,
      );
      maxMaterialLeadTime = maxMaterialLeadTime < _ml ? _ml : maxMaterialLeadTime;
    }
    if (option.option_validity?.valid_from) {
      const _fov = new Date(option.option_validity.valid_from);
      minFromOptionValidity = minFromOptionValidity > _fov ? _fov : minFromOptionValidity;
    }
    if (option.option_validity?.valid_until) {
      const _tov = new Date(option.option_validity.valid_until);
      minTillOptionValidity = minTillOptionValidity > _tov ? _tov : minTillOptionValidity;
    }
  }
  let fromQuotaMonth: Date;
  let untilQuotaMonth: Date;
  /**
   * Referenztag für die Auswahl des möglichen Quotenmonats ist der 15 des Monats.
   * Materialbeschaffungszeitpunkt muss kleiner sein wie der 16. 00:00:00 des Monats
   * Kleinstes Optionsgültigkeiten Datum muss größer sein wie 00:00:00 wie der 1. des Monats
   * see https://skyway.porsche.com/jira/browse/ARTKAST-669
   */

  const maxMaterialLeadTimeDate = new Date(maxMaterialLeadTime);
  const maxMaterialLeadTimeDateThreshold = new Date(maxMaterialLeadTimeDate);
  maxMaterialLeadTimeDateThreshold.setUTCDate(16);
  maxMaterialLeadTimeDateThreshold.setUTCHours(0, 0, 0, 0);
  const _minQuotaMonthMaterialLeadTime = new Date(maxMaterialLeadTimeDate);
  // If >= 16. 00:00:00 add one month
  if (maxMaterialLeadTimeDate >= maxMaterialLeadTimeDateThreshold) {
    // This is doubled, because otherwise if e.g. _minQuotaMonthMaterialLeadTime is 2025-03-31 the month logic will caluculate 2025-05-01 in JavaScript
    _minQuotaMonthMaterialLeadTime.setUTCDate(1);
    // Don't use 0 here as hour, because winter -> summertime change will make march -> april transition march->march otherwise for some locales
    _minQuotaMonthMaterialLeadTime.setUTCHours(8, 0, 0, 0);
    _minQuotaMonthMaterialLeadTime.setMonth(_minQuotaMonthMaterialLeadTime.getMonth() + 1);
  }
  _minQuotaMonthMaterialLeadTime.setUTCDate(1);
  _minQuotaMonthMaterialLeadTime.setUTCHours(0, 0, 0, 0);

  // OUT OF SCOPE: _minQuotaTillOptionValiditiy, calculation to be added here

  // min(_minQuotaMonthMaterialLeadTime, _minQuotaTillOptionValidity)
  // eslint-disable-next-line prefer-const
  fromQuotaMonth = _minQuotaMonthMaterialLeadTime;

  const _untilMinQuotaMonthTillOptionValidity = new Date(minTillOptionValidity);
  _untilMinQuotaMonthTillOptionValidity.setUTCDate(1);
  _untilMinQuotaMonthTillOptionValidity.setUTCHours(0, 0, 0, 0);
  // eslint-disable-next-line prefer-const
  untilQuotaMonth = _untilMinQuotaMonthTillOptionValidity;

  const res = { from: fromQuotaMonth.toISOString().slice(0, 7), until: untilQuotaMonth.toISOString().slice(0, 7) };
  logger.log(LogLevel.TRACE, 'getValidQuotaMonthForConfig output', { data: res });
  return res;
};

export const isQuotaMonthInAllowedRange = (
  props: {
    quotaMonth: string;
    from: string;
    until: string;
  },
  logger: KasLambdaLogger,
): boolean => {
  logger.log(LogLevel.TRACE, 'isQuotaMonthInAllowedRange input', { data: props });
  if (props.quotaMonth >= props.from && props.quotaMonth <= props.until) {
    const res = true;
    logger.log(LogLevel.TRACE, 'isQuotaMonthInAllowedRange output', { data: res });
    return res;
  }
  const res = false;
  logger.log(LogLevel.TRACE, 'isQuotaMonthInAllowedRange output', { data: res });
  return res;
};

export const updateKasConfigurationWithOptionAddedAtTimestamp = (
  props: {
    config: CoraNCOConfiguration;
    old_config?: CoraNCOConfiguration;
  },
  logger: KasLambdaLogger,
): CoraNCOConfiguration => {
  logger.log(LogLevel.TRACE, 'isQuotaMonthInAllowedRange input', { data: props });
  for (const opt of props.config.ordered_options) {
    if (!opt.option_validity) {
      opt.option_validity = {};
    }
    // if option exists in old config, take added timestamp from there, else set to now
    opt.option_validity.added_to_order_timestamp =
      props.old_config?.ordered_options.find((_o) => _o.option_id === opt.option_id)?.option_validity
        ?.added_to_order_timestamp ?? new Date().toISOString();
  }
  logger.log(LogLevel.TRACE, 'isQuotaMonthInAllowedRange output', { data: props.config });
  return props.config;
};

export const stringToBoolean = (str: string | undefined | null): boolean => {
  if (!str) {
    return false;
  }
  switch (str.toLowerCase()) {
    case 'true':
    case '1':
    case 'yes':
      return true;
    default:
      return false;
  }
};

export function validateSqsEvent<T>(input: unknown, validator: ObjectValidator<T>, logger: KasLambdaLogger): boolean {
  const [bodyValidated, errors] = validator.validate(input);
  if (bodyValidated === null) {
    logger.log(LogLevel.ERROR, 'AJV validation failed', { data: errors });
    return false;
  }
  return true;
}
