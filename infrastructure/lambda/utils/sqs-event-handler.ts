import { Context, SQSBatchResponse, SQSEvent } from 'aws-lambda';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { v4 as uuidv4 } from 'uuid';

export function createSqsEventHandlerWithInitLogger(
  logger: KasLambdaLogger,
): (
  event: SQSEvent,
  context: Context,
  handlerLogic: (event: SQSEvent, context: Context, logger: KasLambdaLogger) => Promise<SQSBatchResponse | void>,
) => Promise<SQSBatchResponse | void> {
  return async (
    event: SQSEvent,
    context: Context,
    handlerLogic: (event: SQSEvent, context: Context, logger: KasLambdaLogger) => Promise<SQSBatchResponse | void>,
  ): Promise<SQSBatchResponse | void> => {
    logger.setRequestContext(context);
    //TODO correlation id from sqs message?
    logger.setCorrelationId(uuidv4());
    return handlerLogic(event, context, logger);
  };
}

export function createSqsEventHandler(
  serviceName: string,
  logLevel: LogLevel = LogLevel.TRACE,
): (
  event: SQSEvent,
  context: Context,
  handlerLogic: (event: SQSEvent, context: Context, logger: KasLambdaLogger) => Promise<SQSBatchResponse | void>,
) => Promise<SQSBatchResponse | void> {
  const logger = new KasLambdaLogger(serviceName, logLevel);
  return createSqsEventHandlerWithInitLogger(logger);
}
