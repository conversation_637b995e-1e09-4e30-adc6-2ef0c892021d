import { Constants } from '../../lib/utils/constants';

//auth context apps test data
export const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
} as const;

export const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
} as const;

export const appsWithVisibilityDlr = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'DLR',
    },
  ],
} as const;
