import { APIGatewayProxyEvent, APIGatewayProxyEventHeaders, APIGatewayProxyResult } from 'aws-lambda';
import { gzipSync } from 'zlib';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

const CORS_DOMAIN = process.env.CORS_DOMAIN ?? null;

type CoraHeaders = Record<string, string | boolean>;

export function getUserName(props: { event: APIGatewayProxyEvent }, logger: KasLambdaLogger): string {
  logger.log(LogLevel.TRACE, 'getUserName input', { data: props });
  const res = getAuthContext(props, logger)?.username ?? 'user_not_found';
  logger.log(LogLevel.TRACE, 'getUserName output', { data: res });
  return res;
}

export function getAuthContext(
  props: {
    event: APIGatewayProxyEvent;
  },
  logger: <PERSON>sLambdaLogger,
): KasAuthEndpointResponse | undefined {
  logger.log(LogLevel.TRACE, 'getAuthContext input', { data: props.event });
  const userAttributes = props.event.requestContext.authorizer?.userAttributes as string | undefined;
  const res = userAttributes ? (JSON.parse(userAttributes) as KasAuthEndpointResponse) : undefined;
  logger.log(LogLevel.TRACE, 'getAuthContext output', { data: res });
  return res;
}

export function getVisibilityLevel(
  props: { event: APIGatewayProxyEvent; applicationNameToAuthorize: string },
  logger: KasLambdaLogger,
): string | undefined {
  const userAttributes = getAuthContext(props, logger);
  //look at the first entry for the cora application and take the visibility from there
  const visibilityLevel = userAttributes?.kasApplications[props.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
  return visibilityLevel;
}

export function getPpnId(props: { event: APIGatewayProxyEvent }, logger: KasLambdaLogger): string | undefined {
  logger.log(LogLevel.TRACE, 'getPpnId input', { data: props });
  const authContext = getAuthContext({ event: props.event }, logger);
  const res = authContext?.organizationId;
  logger.log(LogLevel.TRACE, 'getPpnId output', { data: res });
  return res;
}

export interface DefaultSendProps {
  allowedOrigins?: string[];
  reqHeaders: APIGatewayProxyEventHeaders | null;
}
export interface SendFailProps extends DefaultSendProps {
  message: string;
  status: number;
  data?: unknown;
}
export function sendFail(props: SendFailProps, logger: KasLambdaLogger): APIGatewayProxyResult {
  logger.log(LogLevel.TRACE, 'sendFail input', { data: props });
  let headers: CoraHeaders = {
    'Content-Type': 'application/json',
  };
  headers = handleAddingCorsToResponseHeaders(headers, props.reqHeaders, props.allowedOrigins);

  const res = {
    statusCode: props.status,
    headers,
    body: JSON.stringify({ message: props.message, data: props.data }),
  };
  logger.log(LogLevel.DEBUG, 'sendFail output', { data: res });
  return res;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function sendSuccess<T extends Record<string, any> | Record<string, any>[]>(
  props: {
    body: T;
  } & DefaultSendProps,
  logger: KasLambdaLogger,
): APIGatewayProxyResult {
  logger.log(LogLevel.TRACE, 'sendSuccess input', { data: props });
  let headers: CoraHeaders = {
    'Content-Type': 'application/json',
  };
  headers = handleAddingCorsToResponseHeaders(headers, props.reqHeaders, props.allowedOrigins);
  const response = {
    statusCode: 200,
    headers: headers,
    body: JSON.stringify(props.body),
  };
  const doCompression = hasCompressionHeader({ headers: props.reqHeaders }, logger);
  const res = doCompression ? getCompressedResponse({ response: response }, logger) : response;
  logger.log(LogLevel.DEBUG, 'sendSuccess output', { data: res });
  return res;
}

export function handleAddingCorsToResponseHeaders(
  resHeaders: CoraHeaders,
  reqHeaders?: APIGatewayProxyEventHeaders | null,
  allowedOrigins?: string[],
): CoraHeaders {
  const _allowedOrigins = allowedOrigins ?? [CORS_DOMAIN];
  const origin = reqHeaders?.origin ?? reqHeaders?.Origin;
  const isAllowed = origin && _allowedOrigins.includes(origin);
  if (isAllowed) {
    resHeaders['Access-Control-Allow-Origin'] = origin;
    resHeaders['Access-Control-Allow-Credentials'] = true;
  }
  return resHeaders;
}

export function hasCompressionHeader(
  props: {
    headers?: APIGatewayProxyEventHeaders | null;
  },
  logger: KasLambdaLogger,
): boolean {
  logger.log(LogLevel.TRACE, 'hasCompressionHeader input', { data: props });
  const res =
    props.headers &&
    (props.headers['Accept-Encoding']?.includes('gzip') ?? props.headers['accept-encoding']?.includes('gzip'))
      ? true
      : false;
  logger.log(LogLevel.TRACE, 'hasCompressionHeader output', { data: res });
  return res;
}

function getCompressedResponse(
  props: {
    response: APIGatewayProxyResult;
  },
  logger: KasLambdaLogger,
): APIGatewayProxyResult {
  logger.log(LogLevel.TRACE, 'hasCompressionHeader input', { data: props });
  const gzip = gzipSync(props.response.body);
  const base64 = gzip.toString('base64');

  const res = {
    isBase64Encoded: true,
    statusCode: props.response.statusCode,
    headers: {
      ...props.response.headers,
      'Content-Encoding': 'gzip',
    },
    body: base64,
  };
  logger.log(LogLevel.TRACE, 'hasCompressionHeader output', { data: res });
  return res;
}

export function sanitizeApiGwEvent(
  props: {
    event: APIGatewayProxyEvent;
  },
  logger: KasLambdaLogger,
): APIGatewayProxyEvent {
  logger.log(LogLevel.TRACE, 'sanitizeApiGwEvent input', { data: props });
  const res = JSON.parse(JSON.stringify(props.event)) as APIGatewayProxyEvent;
  res.headers = {};
  logger.log(LogLevel.TRACE, 'sanitizeApiGwEvent output', { data: res });
  return res;
}
