import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { KafkaSecret, OAuthCredentials, RdsSecret } from './secret-cache';
import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import * as utils_typeorm from '../utils/utils-typeorm';
import * as utils from '../utils/utils';
import * as type_config from '../config/typeorm-config';
import { DataSource } from 'typeorm';
import { NewCarOrderModel } from '../../lib/entities/new-car-order-model';
import newCarOrderModelsJson from '../../test/data/new-car-order-models.json';
import purchaseIntentionModelsJson from '../../test/data/purchase-intention-models.json';
import newCarOrderAuditTrailModelsJson from '../../test/data/new-car-order-audit-models.json';
import { NewCarOrderAuditTrailModel } from '../../lib/entities/new-car-order-audit-trail-model';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';
import { SQSClient } from '@aws-sdk/client-sqs';
import { QuotaApiConsumeResponse } from '../../lib/types/quota-api-types';

export interface SetupMockOptions {
  /**
   * @default true
   */
  mockDynamoDb?: boolean;
  /**
   * @default true
   */
  mockSecretsmanager?: boolean;
  /**
   * @default true
   */
  mockSQS?: boolean;
  /**
   * @default true
   */
  mockSecretCache?: boolean;
  /**
   * @default true
   */
  mockTypeOrmCreateDataSource?: boolean;
  /**
   * @default mocks and returns null
   */
  mockGetExistingNco?: false | { returnNco: NewCarOrderModel };
  /**
   * @default true
   */
  mockQuotaApi?: boolean | QuotaApiConsumeResponse;
  /**
   * @default true
   */
  mockConfigSignatureVerifier?: boolean;
  /**
   * @default true
   */
  mockValidateNcoRequest?: boolean;
  /**
   * @default true
   */
  mockGenerateNewCarOrderId?: boolean | string;
  /**
   * @default false
   */
  mockFindNotFinishedOrders?: false | { returnNcos: NewCarOrderModel[] };
}
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function setupMocks(props?: SetupMockOptions) {
  const output = {
    ddbMock: props?.mockDynamoDb !== false ? mockClient(DynamoDBClient) : undefined,
    smMock: props?.mockSecretsmanager !== false ? mockClient(SecretsManagerClient) : undefined,
    sqsMock: props?.mockSQS !== false ? mockClient(SQSClient) : undefined,
    createTypeOrmDataSourceMock:
      props?.mockTypeOrmCreateDataSource !== false
        ? jest
            .spyOn(type_config, 'createTypeORMDataSource')
            .mockReturnValue(new Promise((res) => res(mockDataSource as unknown as DataSource)))
        : undefined,
    getExistingNcoMock:
      props?.mockGetExistingNco === false
        ? undefined
        : jest
            .spyOn(utils_typeorm, 'getExistingNco')
            .mockReturnValue(
              new Promise((res) => res(props?.mockGetExistingNco ? props.mockGetExistingNco.returnNco : null)),
            ),
    generateNewCarOrderId:
      props?.mockGenerateNewCarOrderId === false
        ? undefined
        : jest
            .spyOn(utils, 'generateNewCarOrderId')
            .mockReturnValue(
              props?.mockGenerateNewCarOrderId ? (props.mockGenerateNewCarOrderId as string) : 'TEST0001',
            ),
    createNewCarOrderIdMock:
      props?.mockGenerateNewCarOrderId === false
        ? undefined
        : jest
            .spyOn(utils_typeorm, 'createNewCarOrderId')
            .mockReturnValue(new Promise((resolve) => resolve('TESTNCO01'))),
    createNewCarOrderIdsMock:
      props?.mockGenerateNewCarOrderId === false
        ? undefined
        : jest
            .spyOn(utils_typeorm, 'createNewCarOrderIds')
            .mockReturnValue(new Promise((resolve) => resolve(['COPYOD01']))),
    secretCacheMock: props?.mockSecretCache !== false ? jest.mock('../utils/secret-cache') : undefined,
    validateNcoRequestMock:
      props?.mockValidateNcoRequest !== false
        ? jest.spyOn(utils_typeorm, 'validateNcoRequest').mockReturnValue(new Promise((res) => res({ valid: true })))
        : undefined,
    quotaApiMock:
      props?.mockQuotaApi !== false
        ? jest.mock('../utils/quota-api', () => {
            return {
              QuotaApiAdapter: jest.fn().mockImplementation(() => {
                return {
                  // eslint-disable-next-line @typescript-eslint/promise-function-async
                  consumeQuota: (): Promise<boolean> => new Promise((res) => res(true)),
                  // eslint-disable-next-line @typescript-eslint/promise-function-async
                  consumeMultipleQuota: (): Promise<QuotaApiConsumeResponse | undefined> =>
                    new Promise((res) =>
                      res(props?.mockQuotaApi ? (props.mockQuotaApi as QuotaApiConsumeResponse) : undefined),
                    ),
                };
              }),
            };
          })
        : undefined,
    configSignatureVerifieryMock:
      props?.mockConfigSignatureVerifier !== false
        ? jest.mock('../utils/kcc-config-signature-verifier/kcc-config-signature-verifier', () => {
            return {
              ConfigSignatureVerifier: {
                fetchPublicKey: jest.fn().mockResolvedValue({
                  kty: 'RSA',
                  n: 'modulus',
                  e: 'AQAB',
                }),
                verifySignature: jest.fn().mockResolvedValue(true),
              },
            };
          })
        : undefined,
  };
  return output;
}
export const mockManager = {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getRepository: jest.fn().mockImplementation((entity) => ({
    findOne: jest.fn().mockResolvedValue({ modified_at: new Date().toISOString() }), // Mock repository methods
    findBy: jest.fn().mockResolvedValue(newCarOrderAuditTrailModelsJson),
    find: jest.fn().mockResolvedValue(newCarOrderModelsJson),
    delete: jest.fn().mockResolvedValue({ affected: 1 }),
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    preload: jest.fn().mockImplementation((_entity, obj) => obj),
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    save: jest.fn().mockImplementation((_entity) => _entity),
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    create: jest.fn().mockImplementation((_entity) => _entity),
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    insert: jest.fn().mockResolvedValue({
      generatedMaps: newCarOrderAuditTrailModelsJson,
    }),
  })),
  findOne: jest.fn().mockResolvedValue(null),
  delete: jest.fn().mockResolvedValue({ affected: 1 }),
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  preload: jest.fn().mockImplementation((_entity, obj) => obj),
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  save: jest.fn().mockImplementation((_entity) => _entity),
};

export const mockDataSource = {
  initialize: jest.fn().mockResolvedValue(true),
  destroy: jest.fn().mockResolvedValue(true),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getRepository: jest.fn().mockImplementation((entity) => {
    const r = {
      metadata: { columns: ['a', 'b', 'c'] },
      find: jest.fn().mockResolvedValue([]),
      findOne: jest.fn().mockResolvedValue(null),
      findBy: jest.fn().mockResolvedValue(null),
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      save: jest.fn().mockImplementation((_entity) => _entity),
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      update: jest.fn().mockImplementation((_entity) => _entity),
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      create: jest.fn().mockImplementation((_entity) => _entity),
      delete: jest.fn().mockResolvedValue({ affected: 1 }),
      createQueryBuilder: jest.fn().mockImplementation((_entity): unknown => {
        const res = {
          where: jest.fn().mockImplementation(),
          orWhere: jest.fn().mockImplementation(),
          andWhere: jest.fn().mockImplementation(),
          limit: jest.fn().mockImplementation(),
          offset: jest.fn().mockImplementation(),
          orderBy: jest.fn().mockImplementation(),
          getQuery: jest.fn().mockImplementation(),
          innerJoin: jest.fn().mockImplementation(),
          getParameters: jest.fn().mockImplementation(),
          select: jest.fn().mockImplementation(),
          getMany: jest.fn().mockImplementation(async (): Promise<unknown[]> => new Promise((_res) => _res([]))),
        };
        console.warn('ENTITY', _entity);
        switch (_entity) {
          case 'new_car_order': {
            res.getMany = jest
              .fn()
              .mockImplementation(
                async (): Promise<NewCarOrderModel[]> =>
                  new Promise((_res) => _res(newCarOrderModelsJson as NewCarOrderModel[])),
              );
            break;
          }
          case 'pi': {
            res.getMany = jest
              .fn()
              .mockImplementation(
                async (): Promise<CoraPurchaseIntentionModel[]> =>
                  new Promise((_res) => _res(purchaseIntentionModelsJson)),
              );
            break;
          }
        }
        return res;
      }),
    };
    if (entity === NewCarOrderAuditTrailModel) {
      r.findBy = jest
        .fn()
        .mockImplementation(
          async (): Promise<NewCarOrderAuditTrailModel[]> =>
            new Promise((_res) => _res(newCarOrderAuditTrailModelsJson as NewCarOrderAuditTrailModel[])),
        );
    } else if (entity === NewCarOrderModel) {
      r.findBy = jest.fn().mockResolvedValue(newCarOrderModelsJson);
    }
    return r;
  }),
  manager: {
    // eslint-disable-next-line @typescript-eslint/require-await
    transaction: jest.fn().mockImplementation(async (isolationLevel, callback) => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
      return callback(mockManager);
    }),
    find: jest.fn().mockResolvedValue(newCarOrderModelsJson),
  },
};

export const mockContext: Context = {
  callbackWaitsForEmptyEventLoop: true,
  functionName: 'test',
  functionVersion: '1',
  invokedFunctionArn: 'test',
  memoryLimitInMB: '128',
  awsRequestId: 'test',
  logGroupName: 'test',
  logStreamName: 'test',
  getRemainingTimeInMillis: () => 1000,
  done: () => {},
  fail: () => {},
  succeed: () => {},
};

export const auroraUnitTestSecret: RdsSecret = {
  username: 'psuser',
  password: 'testpw',
  host: 'localhost',
  dbname: 'coradb',
  port: '5432',
};

export const dummyKafkaSecret: KafkaSecret = {
  password: 'test',
  username: 'test',
};

export const quotaApiSecret: OAuthCredentials = {
  headers: {
    'X-Porsche-Client-Id': 'testid',
    'X-Porsche-Client-Secret': 'testsecret',
  },
  base_url: 'https://eu-1.test.api.porsche.io/porsche-group/test/quotamanagement',
  client_secret: 'clientsecret',
  token_url: 'https://ppnlite.porsche.com/as/token.oauth2',
  client_id: 'clientid',
};

export function generateApiGatewayEvent(props?: {
  queryStringParameters?: Record<string, string>;
  pathParameters?: Record<string, string>;
  body?: string;
  userAttributes?: string;
}): APIGatewayProxyEvent {
  props = props ?? {};
  props.queryStringParameters = props.queryStringParameters ?? { foo: 'bar' };
  props.pathParameters = props.pathParameters ?? {};
  props.userAttributes =
    props.userAttributes ?? '{"organizationId":"asdffss","kasApplications":{"cora":[{"modelTypeVisibility":"DLR"}]}}';
  props.body = props.body ?? 'eyJ0ZXN0IjoiYm9keSJ9';
  return {
    body: props.body,
    resource: '/{proxy+}',
    path: '/path/to/resource',
    httpMethod: 'POST',
    isBase64Encoded: true,
    queryStringParameters: props.queryStringParameters,
    multiValueQueryStringParameters: {
      foo: ['bar'],
    },
    pathParameters: props.pathParameters,
    stageVariables: {
      baz: 'qux',
    },
    headers: {
      Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Encoding': 'deflate, sdch',
      'Accept-Language': 'en-US,en;q=0.8',
      'Cache-Control': 'max-age=0',
      'CloudFront-Forwarded-Proto': 'https',
      'CloudFront-Is-Desktop-Viewer': 'true',
      'CloudFront-Is-Mobile-Viewer': 'false',
      'CloudFront-Is-SmartTV-Viewer': 'false',
      'CloudFront-Is-Tablet-Viewer': 'false',
      'CloudFront-Viewer-Country': 'US',
      Host: '**********.execute-api.us-east-1.amazonaws.com',
      'Upgrade-Insecure-Requests': '1',
      'User-Agent': 'Custom User Agent String',
      Via: '1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)',
      'X-Amz-Cf-Id': 'cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA==',
      'X-Forwarded-For': '127.0.0.1, *********',
      'X-Forwarded-Port': '443',
      'X-Forwarded-Proto': 'https',
    },
    multiValueHeaders: {
      Accept: ['text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'],
      'Accept-Encoding': ['deflate, sdch'],
      'Accept-Language': ['en-US,en;q=0.8'],
      'Cache-Control': ['max-age=0'],
      'CloudFront-Forwarded-Proto': ['https'],
      'CloudFront-Is-Desktop-Viewer': ['true'],
      'CloudFront-Is-Mobile-Viewer': ['false'],
      'CloudFront-Is-SmartTV-Viewer': ['false'],
      'CloudFront-Is-Tablet-Viewer': ['false'],
      'CloudFront-Viewer-Country': ['US'],
      Host: ['**********.execute-api.us-east-1.amazonaws.com'],
      'Upgrade-Insecure-Requests': ['1'],
      'User-Agent': ['Custom User Agent String'],
      Via: ['1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)'],
      'X-Amz-Cf-Id': ['cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA=='],
      'X-Forwarded-For': ['127.0.0.1, *********'],
      'X-Forwarded-Port': ['443'],
      'X-Forwarded-Proto': ['https'],
    },
    requestContext: {
      accountId: '**********12',
      resourceId: '123456',
      stage: 'prod',
      requestId: 'c6af9ac6-7b61-11e6-9a41-93e8deadbeef',
      requestTime: '09/Apr/2015:12:34:56 +0000',
      requestTimeEpoch: *************,
      identity: {
        cognitoIdentityPoolId: null,
        accountId: null,
        cognitoIdentityId: null,
        caller: null,
        accessKey: null,
        sourceIp: '127.0.0.1',
        cognitoAuthenticationType: null,
        cognitoAuthenticationProvider: null,
        userArn: null,
        userAgent: 'Custom User Agent String',
        user: null,
        apiKey: null,
        apiKeyId: null,
        clientCert: null,
        principalOrgId: null,
      },
      authorizer: {
        userAttributes: props.userAttributes,
      },
      path: '/prod/path/to/resource',
      resourcePath: '/{proxy+}',
      httpMethod: 'POST',
      apiId: '**********',
      protocol: 'HTTP/1.1',
    },
  };
}
export const dateNMonthFromNow = (n: number): Date => {
  return new Date(new Date().setMonth(new Date().getMonth() + n));
};

export const quotaMonthNMonthFromNow = (n: number): string => {
  // Calulating Dates in JS, has some extremly strange edgecases
  // Because of that we cannot use dateNMonthFromNow
  const beginningOfThisMonth = new Date();
  const thisMonth = beginningOfThisMonth.getMonth();
  beginningOfThisMonth.setUTCDate(1);
  // Don't use 00:00 as time here, timechanges (summer/winter) will lead to bugs
  beginningOfThisMonth.setUTCHours(8, 0, 0, 0);
  // Now add the N Month
  beginningOfThisMonth.setMonth(thisMonth + n);
  return beginningOfThisMonth.toISOString().slice(0, 7);
};

export const quotaMonthQuotasRecord = (months: number): Record<string, number> => {
  const quotaRecord: Record<string, number> = {};
  for (let i = 0; i < months; i++) {
    const quotaMonth = quotaMonthNMonthFromNow(i);
    quotaRecord[quotaMonth] = i + 1;
  }
  return quotaRecord;
};
