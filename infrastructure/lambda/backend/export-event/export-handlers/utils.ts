import { MSKRecord } from 'aws-lambda';
import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
import { ncoConfigDbToApiObj } from '../../../utils/utils-typeorm';
import { NewCarOrderKafkaObject } from '../../export-nco/types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { correlationHeader } from '../../../utils/utils';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { NotificationKafkaEvent } from '../../../../lib/types/process-steering-types';

export function flatToKafkaExportNewCarOrderObj(obj: NewCarOrderModel): NewCarOrderKafkaObject {
  const kafkaObj: NewCarOrderKafkaObject = {
    ids: {
      new_car_order_id: obj.pk_new_car_order_id,
      business_partner_id: obj.business_partner_id ?? undefined,
    },
    logistics_info: {
      receiving_port_code: obj.receiving_port_code ?? undefined,
      shipping_code: obj.shipping_code,
    },
    model_info: {
      model_type: obj.model_type,
      model_year: Number(obj.model_year),
      country_code: obj.cnr,
    },
    order_info: {
      base_info: {
        order_type: obj.order_type,
        quota_month: obj.quota_month,
        created_by: obj.created_by,
        last_modified_by: obj.modified_by,
        cancellation_reason: obj.cancellation_reason ?? undefined,
      },
      trading_partner: {
        importer_code: obj.importer_code,
        dealer_sold_to_number: obj.dealer_number,
        dealer_ship_to_number: obj.dealer_number,
        importer_number: obj.importer_number,
      },
    },
    status_info: {
      order_status_code: obj.order_status_onevms_code,
      order_status_timestamp: obj.order_status_onevms_timestamp_last_change,
      order_error_status_code: obj.order_status_onevms_error_code,
      order_error_status_timestamp: obj.order_status_onevms_timestamp_last_change,
    },
    appointment_date_info: {
      production_logistic_dates: {
        order_creation_timestamp: obj.created_at ?? '',
        order_last_modification_timestamp: obj.modified_at,
        requested_dealer_delivery_date: obj.requested_dealer_delivery_date ?? undefined,
      },
    },
    configuration: ncoConfigDbToApiObj(obj.configuration),
    configuration_expire: obj.configuration_expire,
  };
  return kafkaObj;
}

export function parseAndValidateNotificationKafkaRecord(
  record: MSKRecord,
  notificationEventValidator: ObjectValidator<NotificationKafkaEvent>,
  logger: KasLambdaLogger,
): NotificationKafkaEvent | undefined {
  try {
    const correlationId = correlationHeader(record);
    logger.setCorrelationId(correlationId);

    if (!record.value) {
      logger.log(LogLevel.WARN, 'Kafka record has no value – skipping');
      return undefined;
    }

    const payload = Buffer.from(record.value, 'base64').toString('utf8');
    const rawEvent = JSON.parse(payload) as unknown;

    const [validated, validationErrors] = notificationEventValidator.validate(rawEvent);
    if (!validated) {
      logger.log(LogLevel.ERROR, 'NotificationKafkaEvent failed schema validation', {
        data: { raw: rawEvent, errors: validationErrors },
      });
      return undefined;
    }
    return validated;
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Could not parse NotificationKafkaEvent', {
      data: { raw: record.value, error: err },
    });
    return undefined;
  }
}
