/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource, Repository } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { CoraQuo<PERSON> } from '../../../../lib/types/quota-api-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';

const lambdaArn = buildLambdaArn('fetch-quotas');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const quotaTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_QUOTA_TABLE_NAME);

const importerOrgId1 = '152eafb2-d69e-4684-a295-6a0f5d02a8bd';
const importerOrgId2 = '53767110-9941-43f2-8b48-23710cd0195c';
const dealerGroupOrgId1 = 'f9f44a04-0f43-47cd-a981-ea461c528d52';
const dealerGroupOrgId2 = '16fe22ad-7c99-4a0c-ad8c-52d609e67172';
const dealerOrgId1 = '86d71653-ed84-4d0b-8a97-347d23341b6c';
const dealerOrgId2 = '96328ea6-8fda-4ca0-9335-e1f8a17807b3';
const dealerOrgIdInactive = '92d8ba68-6bce-4cf0-9940-42335a5662a0';

const importerNumber1 = '9999991';
const importerNumber2 = '9999992';
const dealerNumber1 = '9999993';
const dealerNumber2 = '9999994';
const dealerNumberInactive = '9999995';

const orgRels: CoraOrgRelModel[] = [
  //importer 1 relation to itself
  {
    pk_ppn_id: importerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: importerNumber1,
    display_name: 'IntegrationTest - QuotaImporter1',
    importer_number: importerNumber1,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //importer 2 relation to itself
  {
    pk_ppn_id: importerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: importerNumber2,
    display_name: 'IntegrationTest - QuotaImporter2',
    importer_number: importerNumber2,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to parent importer1
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: importerOrgId1,
    display_name: 'IntegrationTest - Quota Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 1 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    display_name: 'IntegrationTest - Quota Dealer Group1',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to parent importer2
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: importerOrgId2,
    display_name: 'IntegrationTest - Quota Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group 2 relation to itself
  {
    pk_ppn_id: dealerGroupOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    display_name: 'IntegrationTest - Quota Dealer Group2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: dealerNumber1,
    display_name: 'IntegrationTest - Quota Dealer 1',
    importer_number: importerNumber1,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId1,
    dealer_number: dealerNumber1,
    display_name: 'IntegrationTest - Quota Dealer 1',
    importer_number: importerNumber1,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: dealerNumber1,
    display_name: 'IntegrationTest - Quota Dealer 1',
    importer_number: importerNumber1,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent dealer group2
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId2,
    dealer_number: dealerNumber2,
    display_name: 'IntegrationTest - Quota Dealer 2',
    importer_number: importerNumber2,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId2,
    dealer_number: dealerNumber2,
    display_name: 'IntegrationTest - Quota Dealer 2',
    importer_number: importerNumber2,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer 2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: dealerNumber2,
    display_name: 'IntegrationTest - Quota Dealer 2',
    importer_number: importerNumber2,
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent dealer group1
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerGroupOrgId1,
    dealer_number: dealerNumberInactive,
    display_name: 'IntegrationTest - Quota Dealer Inactive',
    importer_number: importerNumber1,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to parent importer
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: importerOrgId1,
    dealer_number: dealerNumberInactive,
    display_name: 'IntegrationTest - Quota Dealer Inactive',
    importer_number: importerNumber1,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //inactive dealer relation to itself
  {
    pk_ppn_id: dealerOrgIdInactive,
    parent_ppn_id: dealerOrgIdInactive,
    dealer_number: dealerNumberInactive,
    display_name: 'IntegrationTest - Quota Dealer Inactive',
    importer_number: importerNumber1,
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const mtvs: ModelTypeVisibilityModel[] = [
  {
    importer_number: importerNumber1,
    cnr: 'XXX',
    model_type: 'QUOTAMT0001',
    my4: '6666',
    role: 'IMP',
    valid_from: '2017-03-14',
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
  {
    importer_number: importerNumber1,
    cnr: 'XXX',
    model_type: 'QUOTAMT0001',
    my4: '6666',
    role: 'DLR',
    valid_from: '2017-03-14',
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
  {
    importer_number: importerNumber1,
    cnr: 'XXX',
    model_type: 'QUOTAMT0001',
    my4: '6667',
    role: 'IMP',
    valid_from: '2017-03-14',
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
  {
    importer_number: importerNumber1,
    cnr: 'XXX',
    model_type: 'QUOTAMT0002',
    my4: '6666',
    role: 'IMP',
    valid_from: '2017-03-14',
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
];

const quotas: CoraQuota[] = [
  //should be matched
  {
    quota_id_without_month: `QA-${importerNumber1}-${dealerNumber1}-QUOTAMT0001-6666`,
    quota_month: '6666-01',
    consumed_new_car_order_ids: [],
    created_at: '2024-04-17T07:02:26.720594',
    created_by: 'unknown',
    dealer_number: parseInt(dealerNumber1),
    importer_number: parseInt(importerNumber1),
    last_modified_at: '2024-04-17T07:02:26.720594',
    last_modified_by: 'mgesualdi',
    model_type: 'QUOTAMT0001',
    model_year: 6666,
    quota_consumed: 0,
    quota_count: 10,
    quota_id: `QA-${importerNumber1}-${dealerNumber1}-QUOTAMT0001-6666-666601`,
    quota_open: 10,
  },
  //should not be matched because model type or year is wrong in query
  {
    quota_id_without_month: `QA-${importerNumber1}-${dealerNumber1}-QUOTAMT0002-6667`,
    quota_month: '6666-01',
    consumed_new_car_order_ids: [],
    created_at: '2024-04-17T07:02:26.720594',
    created_by: 'unknown',
    dealer_number: parseInt(dealerNumber1),
    importer_number: parseInt(importerNumber1),
    last_modified_at: '2024-04-17T07:02:26.720594',
    last_modified_by: 'mgesualdi',
    model_type: 'QUOTAMT0002',
    model_year: 6667,
    quota_consumed: 0,
    quota_count: 10,
    quota_id: `QA-${importerNumber1}-${dealerNumber1}-QUOTAMT0002-6667-666601`,
    quota_open: 10,
  },
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const quotaPks = quotas.map((quota) => ({
  quota_id_without_month: quota.quota_id_without_month,
  quota_month: quota.quota_month,
}));

let dataSource: DataSource;
let repository: Repository<ModelTypeVisibilityModel>;
let testMtvs: ModelTypeVisibilityModel[] = [];

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([ModelTypeVisibilityModel]);
  repository = dataSource.getRepository(ModelTypeVisibilityModel);
  testMtvs = await repository.save(mtvs);
  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: quotaTableName, objs: quotas },
  ]);
});

afterAll(async () => {
  await repository.remove(testMtvs);
  await dataSource.destroy();
  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: quotaTableName,
      pks: quotaPks,
    },
  ]);
});

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithVisibilityDlr = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'DLR',
    },
  ],
};

const appsWithVisibilityPag = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'PAG',
    },
  ],
};

const defaultQueryStringParameters = {
  importer_number: importerNumber1,
  dealer_number: dealerNumber1,
  model_type: 'QUOTAMT0001',
  model_year: '6666',
};

it('should return 400 if importer_number is missing from query params', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, importer_number: undefined },
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 if dealer_number is missing from query params', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, dealer_number: undefined },
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 if model_type is missing from query params', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, model_type: undefined },
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 400 if model_year is missing from query params', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, model_year: undefined },
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 401 if org is missing in auth context', async () => {
  const event = createApiGwEvent({
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: defaultQueryStringParameters,
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 401 if visibility is missing in auth context', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithNoRole,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(401);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for a user with org ID that is not in DB', async () => {
  const event = createApiGwEvent({
    userOrgId: 'somethingWrongHere',
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for a dealer whos org is inactive', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgIdInactive,
    kasApplications: appsWithVisibilityDlr,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for importer that does not match query data', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId2,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for dealer that does not match query data', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId2,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 if model type is invalid', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, model_type: 'InvalidMT' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 403 for PAG if model type is not visible', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityPag,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(Array.isArray(body)).not.toBe(true);
});

it('should return 200 with empty array for importer if model type missmatches', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, model_type: 'QUOTAMT0002' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  // 3 Mock Quotas always on dev
  expect(body.length).toEqual(3);
});

it('should return 200 with empty array for importer if model year missmatches', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: { ...defaultQueryStringParameters, model_year: '6667' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  // 3 Mock Quotas always on dev
  expect(body.length).toEqual(3);
});

it('should return 200 with the list of correct Quotas for an authorized importer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId1,
    kasApplications: appsWithVisibilityImp,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  // 3 Mock Quotas always on dev
  expect(body.length).toEqual(4);
  expect(body[0]).toEqual(quotas[0]);
});

it('should return 200 with the list of correct Quotas for an authorized dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    kasApplications: appsWithVisibilityDlr,
    queryStringParameters: defaultQueryStringParameters,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  // 3 Mock Quotas always on dev
  expect(body.length).toEqual(4);
  expect(body[0]).toEqual(quotas[0]);
});
