/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { Constants } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../../utils/integration-test-helpers';
import { CoraNCOBaseApiRequest, CoraNCOBaseApiResponse } from '../../../../lib/types/new-car-order-types';
import { CoraMdOneVmsStatus } from '../../../../lib/types/masterdata-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { DataSource, In, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import { ncoApiToDbObj } from '../../../utils/utils-typeorm';
import { OneVmsSourceSystemKey } from '../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn('get-new-car-order');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const statusMappingTableName = `${Constants.MASTER_DATA_APP_NAME}-${'dev'}-${Constants.DYNAMODB_CORA_STATUS_MAPPING_TABLE_NAME}`;

//org relations test data
const importerOrgId = 'fb6b94cb-9a51-4b80-9868-32bf56d50169';
const dealerGroupOrgId = 'e586f7d9-8471-4388-8a64-2c6c7ed32b68';
const dealerOrgId1 = 'a5d68af2-5c25-4f26-9686-996c28a98f90';
const dealerOrgId2 = 'df6332d2-6574-4cf8-b240-7bac1c7500b3';
const unrelatedDealerOrgId = '6842d5c6-3c44-4c6e-856a-581727245896';
const orgWithoutDealers = 'cfa11616-e05a-4808-abb8-c41f760e2d0e';

const orgRels: CoraOrgRelModel[] = [
  //importer relation to itself
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetImp',
    display_name: 'IntegrationTest - Importer',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //org without dealers relation to itself
  {
    pk_ppn_id: orgWithoutDealers,
    parent_ppn_id: orgWithoutDealers,
    display_name: 'IntegrationTest - Importer2',
    importer_number: 'ItNcoGetImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to parent importer
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: importerOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to itself
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: dealerGroupOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //unrelated dealer relation to itself
  {
    pk_ppn_id: unrelatedDealerOrgId,
    parent_ppn_id: unrelatedDealerOrgId,
    dealer_number: 'ItUnrelatedGetDlr',
    display_name: 'IntegrationTest - Unrelated Dlr',
    importer_number: 'ItUnrelatedGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const statusMappings: CoraMdOneVmsStatus[] = [
  {
    one_vms_status: 'order_status_changeable_get',
    one_vms_error_status: 'null',
    description_de: '',
    description_en: '',
    order_changeable: true,
  },
];

//existing nco test objects
const ncoDlr1: NewCarOrderModel = {
  pk_new_car_order_id: 'ITGETGET1',
  dealer_number: 'ItNcoGetDlr1',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '202311',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'order_status_changeable_get',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: {
    created_by: 'IntegrationTester',
    modified_by: 'IntegrationTester',
    ordered_options: [
      {
        created_by: 'IntegrationTester',
        modified_by: 'IntegrationTester',
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            created_by: 'IntegrationTester',
            modified_by: 'IntegrationTester',
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
    technical_options: null,
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const ncoDlr1WrongStatus: NewCarOrderModel = {
  pk_new_car_order_id: 'ITGTFAIL1',
  dealer_number: 'ItNcoGetDlr1',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'non_changeable_order_status',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const ncoDlr2: NewCarOrderModel = {
  pk_new_car_order_id: 'ITGTFAIL2',
  dealer_number: 'ItNcoGetDlr2',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'order_status_changeable_get',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const ncoUnrelatedDlr: NewCarOrderModel = {
  pk_new_car_order_id: 'ITGTFAIL3',
  dealer_number: 'ItUnrelatedGetDlr',
  importer_code: 'IT',
  importer_number: 'XXXXXXXX',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'order_status_changeable_get',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;

function transformBeforeSave(nco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(nco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', nco);
}

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
  ]);
  repository = dataSource.getRepository(NewCarOrderModel);

  await repository.save(transformBeforeSave(ncoDlr1));
  await repository.save(transformBeforeSave(ncoDlr2));
  await repository.save(transformBeforeSave(ncoUnrelatedDlr));
  await repository.save(transformBeforeSave(ncoDlr1WrongStatus));

  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    {
      tableName: statusMappingTableName,
      objs: statusMappings,
    },
  ]);
});

afterAll(async () => {
  await repository.delete({
    pk_new_car_order_id: In([
      ncoDlr1.pk_new_car_order_id,
      ncoDlr2.pk_new_car_order_id,
      ncoUnrelatedDlr.pk_new_car_order_id,
      ncoDlr1WrongStatus.pk_new_car_order_id,
    ]),
  });
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: In([
      ncoDlr1.pk_new_car_order_id,
      ncoDlr2.pk_new_car_order_id,
      ncoUnrelatedDlr.pk_new_car_order_id,
      ncoDlr1WrongStatus.pk_new_car_order_id,
    ]),
  });
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: statusMappingTableName,
      pks: statusMappings.map((org) => ({
        one_vms_status: org.one_vms_status,
        one_vms_error_status: org.one_vms_error_status,
      })),
    },
  ]);
});

it('should return 500 if org is missing in auth context', async () => {
  const event = createApiGwEvent({
    pathParameters: { ncoId: ncoDlr1.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(500);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('An error occurred in the backend');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 400 if ncoId is missing pathParameters', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not get order, invalid id');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 400 if ncoId is invalid', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    pathParameters: { ncoId: 'not_a_valid_ncoid' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not get order, invalid id');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 404 if ncoId does not exist', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    pathParameters: { ncoId: 'ITINVALID' },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(404);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not find order or access was denied');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 404 if nco is in wrong status', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    pathParameters: { ncoId: ncoDlr1WrongStatus.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(404);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not find order or access was denied');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 404 for importer if nco is for unrelated dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    pathParameters: { ncoId: ncoUnrelatedDlr.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(404);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not find order or access was denied');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 404 for dealer if nco is for different dealer', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    pathParameters: { ncoId: ncoDlr2.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(404);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Could not find order or access was denied');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 200 for importer with the complete order object', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    pathParameters: { ncoId: ncoDlr1.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();

  const typedBody = body as CoraNCOBaseApiResponse;
  expect(typedBody.pk_new_car_order_id).toEqual(ncoDlr1.pk_new_car_order_id);
  expect(typedBody.dealer_number).toEqual(ncoDlr1.dealer_number);
  expect(typedBody.importer_code).toEqual(ncoDlr1.importer_code);
  expect(typedBody.importer_number).toEqual(ncoDlr1.importer_number);
  expect(typedBody.model_type).toEqual(ncoDlr1.model_type);
  expect(typedBody.model_year).toEqual(ncoDlr1.model_year);
  expect(typedBody.order_type).toEqual(ncoDlr1.order_type);
  expect(typedBody.shipping_code).toEqual(ncoDlr1.shipping_code);
  expect(typedBody.quota_month).toEqual(ncoDlr1.quota_month);
  expect(typedBody.receiving_port_code).toEqual(ncoDlr1.receiving_port_code);
  expect(typedBody.requested_dealer_delivery_date).toEqual(ncoDlr1.requested_dealer_delivery_date);

  //check status fields
  expect(typedBody.order_status_onevms_code).toEqual(ncoDlr1.order_status_onevms_code);
  expect(typedBody.order_status_onevms_error_code).toEqual(ncoDlr1.order_status_onevms_error_code);

  //check configuration
  expect(typedBody.configuration.ordered_options.length).toEqual(ncoDlr1.configuration.ordered_options.length);
  expect(typedBody.configuration.ordered_options[0].option_type).toEqual(
    ncoDlr1.configuration.ordered_options[0].option_type,
  );

  expect(typedBody.configuration.ordered_options[0].option_validity).toBeDefined();
  expect(typedBody.configuration.ordered_options[0].content?.length).toEqual(
    ncoDlr1.configuration.ordered_options[0].content?.length,
  );
  expect(typedBody.configuration.ordered_options[0].content?.[0].option_type).toEqual(
    ncoDlr1.configuration.ordered_options[0].content?.[0].option_type,
  );
  expect(typedBody.configuration.ordered_options[0].content?.[0].option_validity).toBeDefined();
});

it('should return 200 for dealer with the complete order object', async () => {
  const event = createApiGwEvent({
    userOrgId: dealerOrgId1,
    pathParameters: { ncoId: ncoDlr1.pk_new_car_order_id },
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(body.pk_new_car_order_id).toEqual(ncoDlr1.pk_new_car_order_id);
});
