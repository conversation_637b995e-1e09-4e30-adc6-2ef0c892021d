import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { getAuthContext, getUserName, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getAllowedDealers, updateKasConfigurationWithOptionAddedAtTimestamp } from '../../../utils/validation-helpers';
import { secretCache } from '../../../utils/secret-cache';
import { QuotaApiAdapter } from '../../../utils/quota-api';
import { getEnvVarWithAssert, validateCopyRequest } from '../../../utils/utils';
import {
  CoraNCOBaseApiRequest,
  CoraNCOCopyApiRequest,
  CoraNCOCopyApiResponse,
  CoraNCOCreateApiRequest,
} from '../../../../lib/types/new-car-order-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGWHandlerWithInitLogger } from '../../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { EntityManager, Repository } from 'typeorm';
import { CoraQuota } from '../../../../lib/types/quota-api-types';
import { CoraMdDealer } from '../../../../lib/types/masterdata-types';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { NcoExportActionType } from '../../export-nco/types';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import {
  createNewCarOrderIds,
  getExistingNco,
  ncoApiToDbObj,
  ncoDbToApiObj,
  saveNcosWithAuditTrail,
  validateNcoRequest,
} from '../../../utils/utils-typeorm';
import { GeneralError } from '../../../utils/errors';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { DealerRelationError } from '../../../../lib/types/custom-error-types';

const objectValidator = new ObjectValidator<CoraNCOCopyApiRequest>('CoraNCOCopyApiRequest');
const createRequestValidator = new ObjectValidator<CoraNCOCreateApiRequest>('CoraNCOCreateApiRequest');

// Initialize table names, DB and Lambda Client
const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');
const quotaTableName = getEnvVarWithAssert('TABLE_NAME_QUOTA');
const ncoValidationTables = {
  coraOrgRelationTableName,
  shippingCodeTableName,
  orderTypeTableName,
  importerTableName,
  dealerTableName,
};
const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

// Other envs
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');
const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
const stage = getEnvVarWithAssert('STAGE');
const allowMockQuotaApi = process.env.ALLOW_QUOTA_API_MOCK ?? 'false';
const kasLambdaLogger = new KasLambdaLogger('copy-new-car-order', LogLevel.TRACE);

// Initialize secret cache and quota api adapter
secretCache.initCache(quotaApiSecretArn, auroraSecretArn);
const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: kasLambdaLogger,
});

const DEFAULT_ORDER_TYPE = 'LF';

const copyNewCarOrderFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  let errMessage: string;
  const queryParameters = event.queryStringParameters;
  const ncoId = event.pathParameters ? event.pathParameters['ncoId'] : undefined;
  const regex = /^[A-Z0-9]{8,9}$/;
  if (!ncoId || !regex.test(ncoId.trim())) {
    const message = 'Could not get order, invalid id';
    logger.log(LogLevel.ERROR, message, { data: { ncoId: ncoId, pathParameters: event.pathParameters } });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }

  // This should only be allowed for dev stage. E2e and integration tests set the query string
  const mockQuotaApi = allowMockQuotaApi === 'true' ? true : false;

  // Checking user permissions
  const userAttributes = getAuthContext({ event: event }, logger);
  const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
  const ppnId = userAttributes?.organizationId;

  if (!ppnId || !visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId or visibility level', {
      data: sanitizeApiGwEvent({ event: event }, logger),
    });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  // Create nco datasource and repo
  const ncoDataSource = await createTypeORMDataSource(logger, auroraSecretArn, stage, [
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    ModelTypeVisibilityModel,
  ]);
  const ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  const allowedDealers = await getAllowedDealers({ dynamoDb, event }, logger);

  if (allowedDealers.length === 0) {
    errMessage = 'User is not authorized to any dealers and cannot change the dealer for this copy operation.';
    logger.log(LogLevel.WARN, errMessage);
    return sendFail({ message: errMessage, status: 403, reqHeaders: event.headers }, logger);
  }

  // Fetch source NewCarOrder and check if applicable for copy order
  let sourceNewCarOrder: NewCarOrderModel | undefined;
  logger.log(LogLevel.DEBUG, 'Fetching source NewCarOrder and validating, that it can be copied.');
  try {
    sourceNewCarOrder = await fetchSourceNewCarOrder(ncoId, ncoRepo, allowedDealers, logger);
  } catch (error) {
    errMessage = 'Error fetching source NewCarOrder';
    logger.log(LogLevel.WARN, errMessage, { data: error });
    return sendFail({ message: errMessage, status: 404, reqHeaders: event.headers }, logger);
  }

  // Add a dummy signature to the order for now until KCC decides on a solution
  const userName = getUserName({ event: event }, logger);
  const _newCarOrder: CoraNCOCreateApiRequest = {
    ...ncoDbToApiObj(sourceNewCarOrder), // this adds fields like modifed_at etc.
    configuration_signature: 'dummySignature',
    configuration_expire_signature: 'dummySignature',
    order_type: DEFAULT_ORDER_TYPE,
    quota_month: sourceNewCarOrder.quota_month ?? '',
  };

  // Used to remove extra fields from the response
  const [newCarOrder, create_validation_errors] = createRequestValidator.validate(_newCarOrder);
  if (newCarOrder === null) {
    logger.log(LogLevel.WARN, 'CarOrder ajv validation failed', { data: create_validation_errors });
    return sendFail(
      {
        message: 'Ajv validation failed with: ' + JSON.stringify(create_validation_errors),
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  // Parse request body and validate delivered quotas and number of orders to be created
  const quotas = JSON.parse(event.body ?? '{}') as CoraNCOCopyApiRequest;

  // Validate request body against the schema
  const [body_validated, validation_errors] = objectValidator.validate(quotas);
  if (body_validated === null) {
    logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
    return sendFail(
      {
        message: 'Ajv validation failed with: ' + JSON.stringify(validation_errors),
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  // Custom validation for copy request
  const validation = validateCopyRequest(quotas, logger);
  if (!validation.isValid) {
    return validation.error!;
  }

  /**
   * If the copied orders have a different dealer selected than the source NewCarOrder,
   * check if the user has the privilege to perform this operation and if the newly selected dealer is in the same market.
   */
  if (queryParameters?.newSelectedDealer) {
    const newSelectedDealer: string | undefined = queryParameters.newSelectedDealer;
    const newDealerOrgRel: CoraOrgRelModel | undefined = allowedDealers.find(
      (dealer) => dealer.dealer_number === newSelectedDealer,
    );
    let newDealerRelation: CoraMdDealer;
    if (!newDealerOrgRel) {
      errMessage =
        'Newly selected dealer could not be found in allowed dealers of the user. This should have been prevented';
      logger.log(LogLevel.WARN, errMessage);
      return sendFail({ message: errMessage, status: 403, reqHeaders: event.headers }, logger);
    }
    try {
      newDealerRelation = await validateDealerRelation(newDealerOrgRel, logger);
    } catch (error) {
      if (error instanceof DealerRelationError) {
        logger.log(LogLevel.WARN, error.message, { data: { error } });
        return sendFail({ message: error.message, status: error.errorCode, reqHeaders: event.headers }, logger);
      }
      return sendFail(
        {
          message: 'Internal server error while validating newly selected dealer relation.',
          status: 500,
          reqHeaders: event.headers,
        },
        logger,
      );
    }

    if (newSelectedDealer === newCarOrder.dealer_number) {
      logger.log(LogLevel.INFO, 'Dealer number has not changed, validation is skipped.');
    } else {
      if (newDealerRelation.pk_importer_number !== newCarOrder.importer_number) {
        errMessage = 'The selected dealer is not in the same market as the initial dealer';
        logger.log(LogLevel.WARN, errMessage, {
          data: { oldDealer: newCarOrder.dealer_number, selectedDealer: newSelectedDealer },
        });
        return sendFail({ message: errMessage, status: 409, reqHeaders: event.headers }, logger);
      } else {
        logger.log(LogLevel.DEBUG, 'Selected dealer is valid and order(s) can be created.', {
          data: { oldDealer: newCarOrder.dealer_number, selectedDealer: newSelectedDealer },
        });
        newCarOrder.dealer_number = newSelectedDealer;
      }
    }
  }

  // Validate all parameters based on users permissions
  const _res = await validateNcoRequest(
    {
      dynamoDb,
      newCarOrder,
      ppnId,
      visibilityLevel,
      tables: ncoValidationTables,
      customerRelatedOt: false,
      mtvRespoitory: ncoDataSource.getRepository(ModelTypeVisibilityModel),
    },
    logger,
  );
  if (!_res.valid) {
    logger.log(LogLevel.WARN, 'Custom validation failed with: ' + _res.error);
    return sendFail(
      { message: `Custom validation failed with: ${_res.error}`, status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  // Initialize array for successful and failed order creations
  const quotaResults: CoraNCOCopyApiResponse = [];

  /**
   * This loop processes the copy order functionality in four steps:
   *
   * 1. Retrieve available quotas:
   *    The available quotas for the requested quota month are fetched and compared to the requested amount.
   *    If the requested amount exceeds the available quotas, it is reduced to the number of open quotas.
   *
   * 2. Generate NewCarOrder IDs:
   *    NewCarOrder IDs are created and stored with a retry mechanism limited to five retries, if duplicates are created.
   *    Corresponding NewCarOrder objects are initialized and prepared for storage.
   *
   * 3. Consume quotas via API:
   *    The quota consumption is processed in bulk through the Quota API. Partial success is possible and will be handled accordingly.
   *
   * 4. Save NewCarOrders to database:
   *    Upon (partially) successful completion of all previous steps, the NewCarOrders are saved to the database, finalizing their creation.
   */

  for (const [quota_month, quota_requested] of Object.entries(quotas)) {
    // Prepare all variables
    const partitionKey = `QA-${newCarOrder.importer_number}-${newCarOrder.dealer_number}-${newCarOrder.model_type}-${newCarOrder.model_year}`;
    let availableQuota = 0;
    let failedQuotaCount = 0;
    let quotasToConsume = quota_requested;

    // Reusable full fail result
    const fullyFailedQuotaResult = {
      quota_month,
      quota_consumed: 0,
      quota_failed: quota_requested,
      new_car_order_ids: [],
    };

    // 1. Query dynamodb table for available dealer quota
    if (!mockQuotaApi) {
      try {
        logger.log(LogLevel.DEBUG, `Fetching all quotas for the quota month ${quota_month}`);
        const quotaResponse = await dynamoDb.send(
          new QueryCommand({
            TableName: quotaTableName,
            KeyConditionExpression: 'quota_id_without_month = :pk AND quota_month = :sk',
            ExpressionAttributeValues: {
              ':pk': partitionKey,
              ':sk': quota_month,
            },
          }),
        );

        if (quotaResponse.Items && quotaResponse.Items.length > 0) {
          const quotaItems = quotaResponse.Items as CoraQuota[];
          availableQuota = quotaItems[0].quota_open;
        }
        logger.log(LogLevel.INFO, `Quota available for this month: ${availableQuota}`, { data: quota_month });

        // The quota to be consumed is determined by the smaller value between the requested and available quotas.
        // If the requested quota exceeds the available quota, the excess is considered as failed copy operation.
        quotasToConsume = Math.min(quota_requested, availableQuota);
        failedQuotaCount = quota_requested - quotasToConsume;
      } catch (error) {
        logger.log(LogLevel.ERROR, `Error querying available quota for ${quota_month}`, { data: error });
        quotaResults.push(fullyFailedQuotaResult);
        continue; // skip this month
      }
    }

    // 2. Create NCO Ids for all consumable quotas with retry mechanism, if duplicates are created
    let ncoRequests: CoraNCOBaseApiRequest[] = [];

    let newCarOrderIds = await createNewCarOrderIds(
      { amount: quotasToConsume, importerCode: newCarOrder.importer_code, ncoRepo },
      logger,
    );

    for (const createdNcoId of newCarOrderIds) {
      const newCarOrderForQuotaApi: CoraNCOBaseApiRequest = {
        ...newCarOrder,
        pk_new_car_order_id: createdNcoId,
        business_partner_id: undefined,
        configuration: updateKasConfigurationWithOptionAddedAtTimestamp({ config: newCarOrder.configuration }, logger),
        quota_month,
      };

      ncoRequests.push(newCarOrderForQuotaApi);
    }

    logger.log(LogLevel.INFO, `Pushed all NCO's for this quota month into an array.`, { data: newCarOrderIds });

    // 3. Consume quota
    try {
      logger.log(LogLevel.DEBUG, `Consuming quota for all ${newCarOrderIds.length} NCO's of this quota month`);
      const quotaConsumptionResult = await quotaApiAdapter.consumeMultipleQuota(
        {
          ncoList: ncoRequests,
          mockApi: mockQuotaApi,
          correlationId: event.headers['x-kas-request-id'] ?? logger.getCorrelationId() ?? 'unknown',
        },
        logger,
      );

      if (!quotaConsumptionResult) {
        logger.log(LogLevel.WARN, `Failed to consume quota for ${quota_month}`, {
          data: {
            quotaResponse: quotaConsumptionResult,
            requestedNewCarOrderIds: newCarOrderIds,
          },
        });
        quotaResults.push(fullyFailedQuotaResult);
        continue; // skip this month
      }
      // Processing of quota consumption response
      const consumedNcoIds = quotaConsumptionResult.validation_response?.consumed_new_car_orders ?? [];
      const notConsumedNcoIds = newCarOrderIds.filter((id) => !consumedNcoIds.includes(id));

      // Validate API response data.
      // Reduce NCOs, if quota consume was partial success.
      if (consumedNcoIds.length === 0) {
        throw new GeneralError({ message: 'Invalid quota API response, aborting this quota month.' });
      } else if (notConsumedNcoIds.length > 0) {
        logger.log(
          LogLevel.INFO,
          `Partial quota consumption success. ${notConsumedNcoIds.length} out of ${newCarOrderIds.length} quotas could not be consumed`,
          { data: { consumedNcoIds } },
        );

        failedQuotaCount += notConsumedNcoIds.length;

        newCarOrderIds = newCarOrderIds.filter((id) => consumedNcoIds.includes(id));
        ncoRequests = ncoRequests.filter((nco) => consumedNcoIds.includes(nco.pk_new_car_order_id));
      }

      // 4. Save all successful consumptions into the NewCarOrder list as new orders.

      try {
        const ncoDbObjList = ncoRequests.map((nco) => ncoApiToDbObj(nco, userName));
        // ncoDbObjList.forEach((nco) => {
        //   // delete nco.configuration.pk_config_id; // remove the configuration id to avoid conflicts
        //   nco.configuration.ordered_options.forEach((option) => {
        //     // delete (option as Partial<NcoConfigOrderedOptionsModel>).pk_option_id;
        //     //Currently we consider only the content but not any descendants of the content
        //     option.content?.forEach((subOption) => {
        //       // delete (subOption as Partial<NcoConfigOrderedOptionsModel>).pk_option_id;
        //     });
        //   });
        // });
        logger.log(LogLevel.TRACE, `NewCarOrder object for DB`, { data: ncoDbObjList });
        await saveNcosWithAuditTrail(
          ncoDataSource,
          [...ncoDbObjList.map((nco) => nco.pk_new_car_order_id)],
          NcoExportActionType.CREATE,
          async (transactionManager: EntityManager) => {
            const res = await transactionManager.getRepository(NewCarOrderModel).save(ncoDbObjList);
            return res;
          },
          logger,
        );
      } catch (error) {
        logger.log(LogLevel.ERROR, 'Error saving New Car Order to database', { data: error });
        quotaResults.push(fullyFailedQuotaResult);
        continue; // skip this month
      }
    } catch (error) {
      logger.log(LogLevel.ERROR, `Error consuming quota for ${quota_month}`, { data: error });
      quotaResults.push(fullyFailedQuotaResult);
      continue; // skip this month
    }

    // 5. Save all results for this quota month, to prepare the lambda response.
    quotaResults.push({
      quota_month,
      quota_consumed: newCarOrderIds.length,
      quota_failed: failedQuotaCount,
      new_car_order_ids: newCarOrderIds,
    });
  }

  // Check if any Quota was consumed. If not send fail.
  const totalConsumed = quotaResults.reduce((sum, result) => sum + result.quota_consumed, 0);
  if (totalConsumed === 0) {
    return sendFail(
      { message: 'Error creating new car order, no quota available', status: 409, reqHeaders: event.headers },
      logger,
    );
  }
  return sendSuccess({ body: quotaResults, reqHeaders: event.headers }, logger);
};

// Helper function to fetch order
async function fetchSourceNewCarOrder(
  ncoId: string,
  ncoRepo: Repository<NewCarOrderModel>,
  allowedDealers: CoraOrgRelModel[],
  logger: KasLambdaLogger,
): Promise<NewCarOrderModel> {
  try {
    // Fetch the source NewCarOrder from DB
    const sourceNewCarOrder = await getExistingNco({ ncoRepo, ncoId }, logger);
    if (!sourceNewCarOrder) {
      logger.log(LogLevel.WARN, `Order with ncoId ${ncoId} not found or access denied.`);
      throw new Error('Could not find order or access was denied');
    }
    logger.log(LogLevel.INFO, `Got DB response for ncoId ${ncoId}`, { data: sourceNewCarOrder });

    // Checking if user is allowed to see this order
    if (!allowedDealers.some((dealer) => dealer.dealer_number === sourceNewCarOrder.dealer_number)) {
      const errMessage = 'User is not allowed to see this order.';
      logger.log(LogLevel.WARN, errMessage, {
        data: { sourceOrderDealerNumber: sourceNewCarOrder.dealer_number, allowedDealers },
      });
      throw new Error(errMessage);
    }

    // Status validation: Has to start with PP, IP or ID
    const statusPrefix = sourceNewCarOrder.order_status_onevms_code.slice(0, 2);
    if (!['PP', 'IP', 'ID'].includes(statusPrefix)) {
      logger.log(LogLevel.WARN, 'Order has invalid status prefix, not allowed to copy', { data: sourceNewCarOrder });
      throw new Error('Order status not allowed for copying');
    }
    return sourceNewCarOrder;
  } catch (error) {
    logger.log(LogLevel.WARN, 'Error fetching or validating source New Car Order', { data: error });
    throw new Error(`Error during fetch, CorrelationId: ${logger.getCorrelationId()}`);
  }
}

async function validateDealerRelation(dealerOrgRel: CoraOrgRelModel, logger: KasLambdaLogger): Promise<CoraMdDealer> {
  logger.log(LogLevel.INFO, `Fetching dealer relation for dealer number ${dealerOrgRel.dealer_number}`);
  const params = {
    TableName: dealerTableName,
    KeyConditionExpression: 'pk_importer_number = :importerNumber AND sk_dealer_number = :dealerNumber',
    ExpressionAttributeValues: {
      ':importerNumber': dealerOrgRel.importer_number,
      ':dealerNumber': dealerOrgRel.dealer_number,
    },
  };
  const result = await dynamoDb.send(new QueryCommand(params));

  if (!result.Items || result.Items.length === 0) {
    throw new DealerRelationError('Newly selected dealer could not be found', 404);
  }
  if (dealerOrgRel.is_deactivated || !dealerOrgRel.is_relevant_for_order_create) {
    throw new DealerRelationError('Newly selected dealer cannot be used for order creation.', 409);
  }

  return result.Items[0] as CoraMdDealer;
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(kasLambdaLogger)(event, context, copyNewCarOrderFunc);
