import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { mockClient } from 'aws-sdk-client-mock';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { SendMessageBatchCommand, SQSClient } from '@aws-sdk/client-sqs';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { auroraUnitTestSecret, mockContext, quotaApiSecret } from '../../../utils/test-utils';
import { DataSource, In } from 'typeorm';
import { KasLambdaLogger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { CoraPurchaseIntentionModel } from '../../../../lib/entities/purchase-intention-model';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import existingOrdersJson from '../../../../test/data/new-car-order-models.json';

const existingOrders = existingOrdersJson.map((nco, index) => {
  return { ...nco, pk_new_car_order_id: 'COPY000' + index };
});
const ddbMock = mockClient(DynamoDBClient);
const sqsMock = mockClient(SQSClient);
sqsMock.on(SendMessageBatchCommand).resolves({});
const smMock = mockClient(SecretsManagerClient);
smMock
  .on(GetSecretValueCommand, { SecretId: 'quotasecretarn' })
  .resolves({ SecretString: JSON.stringify(quotaApiSecret) });
smMock
  .on(GetSecretValueCommand, { SecretId: 'aurorasecretarn' })
  .resolves({ SecretString: JSON.stringify(auroraUnitTestSecret) });
jest.mock('../../../utils/quota-api', () => {
  return {
    QuotaApiAdapter: jest.fn().mockImplementation(() => {
      return {
        consumeQuota: jest.fn().mockImplementation().mockResolvedValue(true),
        consumeMultipleQuota: jest.fn().mockImplementation().mockResolvedValue(true),
      };
    }),
  };
});

import { handler } from './index';
import { event } from '../../../../test/test-types';
import { BatchGetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import coraOrgRelData from '../../../../test/data/cora_org_rel.json';
import { appsWithVisibilityImp } from '../../../utils/test-constants';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { CoraNCOCopyApiRequest, CoraNCOCopyApiResponse } from '../../../../lib/types/new-car-order-types';
import { ncoApiToDbObj } from '../../../utils/utils-typeorm';

async function setupDatabase(): Promise<DataSource> {
  const dataSource = await createTypeORMDataSource(new KasLambdaLogger('test'), 'aurorasecretarn', 'test', [
    CoraPurchaseIntentionModel,
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
  ]);

  // Add test data for purchase intentions
  const newCarOrderRepository = dataSource.getRepository(NewCarOrderModel);

  for (const existingNco of existingOrders) {
    await newCarOrderRepository.save(ncoApiToDbObj(existingNco, 'test'));
  }
  return dataSource;
}

async function teardownDatabase(dataSource: DataSource): Promise<void> {
  const ncoRepo = dataSource.getRepository(NewCarOrderModel);
  await ncoRepo
    .createQueryBuilder()
    .delete()
    .from(NewCarOrderModel)
    .where('pk_new_car_order_id LIKE :prefix', { prefix: 'COPY%' })
    .execute();
  await dataSource.destroy();
  return;
}

describe('copyNewCarOrder Local Database Integration Test', () => {
  let dataSource: DataSource | undefined = undefined;
  const ppnId = 'ppn_id-IMPORTER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    pathParameters: {
      ncoId: existingOrders[0].pk_new_car_order_id,
    },
    body: JSON.stringify({ '2025-01': 100 } satisfies CoraNCOCopyApiRequest),
    requestContext: {
      ...event.requestContext,
      authorizer: {
        userAttributes: JSON.stringify({
          organizationId: ppnId,
          kasApplications: appsWithVisibilityImp,
        }),
      },
    },
    queryStringParameters: {},
  };

  beforeEach(() => {
    ddbMock
      .on(QueryCommand)
      .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
    // ddbMock.on(GetCommand).resolves({
    //   Item: oneVmsStatusData.find((s) => s.one_vms_status === old_new_car_order.order_status_onevms_code),
    // });
    ddbMock.on(BatchGetCommand).resolves({
      Responses: {
        [process.env.TABLE_NAME_SC!]: [
          {
            pk_shipping_code: '1',
            description: 'SC 01',
            imp_is_blacklist: true,
            importers: [],
            dlr_is_blacklist: true,
            dealers: [],
            is_deactivated: false,
          },
        ],
        [process.env.TABLE_NAME_OT!]: [
          {
            pk_order_type: 'LF',
            description: 'OT LF',
            imp_is_blacklist: true,
            importers: [],
            dlr_is_visible: true,
            is_deactivated: false,
            is_customer_related: false,
          },
        ],
        [process.env.TABLE_NAME_IMP!]: [
          { pk_importer_number: existingOrders[0].importer_number, code: existingOrders[0].importer_code },
        ],
        [process.env.TABLE_NAME_DLR!]: [{ sk_dealer_number: existingOrders[0].dealer_number }],
        [process.env.TABLE_NAME_MTV!]: [
          {
            valid_from: new Date(0).toISOString(),
          },
        ],
      },
    });
  });
  beforeAll(async () => {
    dataSource = await setupDatabase();
  });

  afterAll(async () => {
    if (dataSource) {
      await teardownDatabase(dataSource);
    }
  });

  it('should copy new car order successfully', async () => {
    const result = (await handler(_event, mockContext, () => {
      return;
    })) as APIGatewayProxyResult;
    const created_orders = JSON.parse(result.body) as CoraNCOCopyApiResponse;
    const res = await dataSource?.getRepository(NewCarOrderModel).find({
      where: { pk_new_car_order_id: In(created_orders[0].new_car_order_ids) },
      relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
    });
    expect(result).toBeDefined();
    expect(res?.length).toBe(created_orders[0].new_car_order_ids.length);
    const first_res = res?.[0];
    const package_option = first_res?.configuration.ordered_options.find((o) => o.option_type === 'Package');
    const now = new Date();
    expect(new Date(first_res?.modified_at ?? 0).getTime()).toBeGreaterThan(now.getTime() - 30 * 60 * 1000);
    expect(package_option?.content?.length).toBe(2);
    expect(result.statusCode).toBe(200);
  });
});
