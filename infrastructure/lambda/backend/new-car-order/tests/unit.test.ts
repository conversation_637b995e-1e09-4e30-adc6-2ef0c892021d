import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import path from 'path';
import { createGenerator } from 'ts-json-schema-generator';
import frontendRequestJson from './frontend_request_example.json';
import { CoraNCOCreateApiRequest } from '../../../../lib/types/new-car-order-types';

const ajv = new Ajv({ removeAdditional: true });
addFormats(ajv);

beforeEach(() => {
  jest.resetModules();
});

describe('Validation with example requests should succeed', () => {
  it('validate frontend request', () => {
    const config = {
      path: path.join(__dirname, '../../../../lib/types/*.ts'),
      type: '*',
    };
    console.log(config);
    // Schema for input validation using AJV
    const schema = createGenerator(config).createSchema('CoraNCOCreateApiRequest');
    const validate = ajv.compile(schema);

    // Validate body against the schema
    const valid = validate(frontendRequestJson);
    const validatedInput = frontendRequestJson as CoraNCOCreateApiRequest;
    console.log(validatedInput);
    if (validate.errors) console.log(validate.errors);
    expect(valid).toBe(true);
    expect(frontendRequestJson.testAdditional).toBe(undefined);
  });
});
