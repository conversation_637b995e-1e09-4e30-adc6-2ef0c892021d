export enum OrderActionStatusCodes {
  SUCCESS = 'order_action_success',
  WRONG_STATUS = 'order_action_wrong_status',
  ORDER_FORBIDDEN = 'order_action_forbidden',
  ORDER_NOT_FOUND = 'order_not_found',
  AURORA_ERROR = 'aurora_error',
  UNKNOWN = 'unknown',
  //Totalloss
  TOO_LONG_AGO = 'total_loss_too_long_ago',
  RACING_CONDITION = 'racing_condition',
}

export type OrderActionResult = Record<string, OrderActionStatusCodes>;
