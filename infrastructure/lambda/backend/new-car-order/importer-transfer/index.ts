import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { getUserName, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { secretCache } from '../../../utils/secret-cache';
import { getAllowedDealersAndImporters } from '../../../utils/validation-helpers';

import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { getEnvVarWithAssert, transformModifiedAt } from '../../../utils/utils';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource, EntityManager, In, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
  NewCarOrderModelWithOptionalActionType,
  NewCarOrderModelWithQuota,
} from '../../../../lib/entities/new-car-order-model';
import {
  CoraNCOConfiguration,
  CoraNCOImporterTransferApiRequest,
  CoraNCOImporterTransferApiResponse,
  SOURCE_OBJ_TYPES,
} from '../../../../lib/types/new-car-order-types';
import { Constants } from '../../../../lib/utils/constants';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { createApiGWHandlerWithInitLogger } from '../../../utils/api-gw-handler';
import { QuotaApiAdapter } from '../../../utils/quota-api';
import {
  createNewCarOrderIds,
  ncoConfigApiToDbObj,
  removeLocalOptionsFromNco,
  saveNcosWithAuditTrail,
} from '../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../export-nco/types';
import {
  ApiRequestValidationError,
  isImporterTransferStatusValid,
  validateImporterTransferParams,
  validateRequest,
} from './importer_transfer_req_validation';
import { IMPORTER_TRANSFER_ERRORS } from './types';
import { OneVmsSourceSystemKey } from '../../../../lib/types/process-steering-types';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
// const stage = getEnvVarWithAssert('STAGE');
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');
const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');

const kasLambdaLogger = new KasLambdaLogger('importer-transfer', LogLevel.TRACE);

// Initialize secret cache and quota api adapter
secretCache.initCache(quotaApiSecretArn, auroraSecretArn);
const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: kasLambdaLogger,
});

const objectValidator = new ObjectValidator<CoraNCOImporterTransferApiRequest>('CoraNCOImporterTransferApiRequest');

const importerTransferFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.DEBUG, 'Incoming Event', { data: event });
  try {
    const validateRes = validateRequest<CoraNCOImporterTransferApiRequest>(event, logger, objectValidator);
    const userName = getUserName({ event }, logger);
    const res = await _importerTransferFunc(validateRes.body_validated, validateRes.ppnId, userName, logger);
    return sendSuccess({ body: res, reqHeaders: event.headers }, logger);
  } catch (err) {
    if (err instanceof ApiRequestValidationError) {
      logger.log(LogLevel.WARN, err.message, { data: err.statusCode });
      return sendFail({ status: err.statusCode, message: err.message, reqHeaders: event.headers }, logger);
    }
    throw err;
  }
};

const _importerTransferFunc = async (
  validated_req: CoraNCOImporterTransferApiRequest,
  ppnId: string,
  userName: string,
  logger: KasLambdaLogger,
): Promise<CoraNCOImporterTransferApiResponse> => {
  const allowedOrgs = await getAllowedDealersAndImporters({ dynamoDb: dynamoDb, ppnId }, logger);
  logger.log(LogLevel.DEBUG, '_importerTransferFunc', { data: { validated_req, ppnId, userName } });

  /**
   * Validation:
   * 1. to_dealer is allowed
   * 2. to_importer is allowed
   * 3. to_dealer hast to_importer as its importer
   * 4. new order_type is allowed
   * 5. new port_code is allowed
   * 6. new shipping_code is allowed
   */
  const toDealer = allowedOrgs.uniqueDlrs.find((dlr) => dlr.dealer_number === validated_req.to_dealer_number);
  if (!toDealer) throw new ApiRequestValidationError(400, 'Invalid Dealer');
  const toImporter = allowedOrgs.uniqueImps.find((imp) => imp.importer_number === validated_req.to_importer_number);
  if (!toImporter) throw new ApiRequestValidationError(400, 'Invalid Importer');
  if (toDealer.importer_number !== toImporter.importer_number) {
    logger.log(
      LogLevel.WARN,
      'Dealer does not have to_importer as the importer_number, should never happen, except when client manipulates request',
    );
    throw new ApiRequestValidationError(400, 'Invalid Dealer');
  }

  const { importer } = await validateImporterTransferParams({
    req: validated_req,
    dynamoDb: dynamoDb,
    importerTableName: getEnvVarWithAssert('TABLE_NAME_IMP'),
    shippingCodeTableName: getEnvVarWithAssert('TABLE_NAME_SC'),
    orderTypeTableName: getEnvVarWithAssert('TABLE_NAME_OT'),
    dealerTableName: getEnvVarWithAssert('TABLE_NAME_DLR'),
  });

  const result: CoraNCOImporterTransferApiResponse = [];
  const createNcos: NewCarOrderModelWithQuota[] = [];
  try {
    //create the typeorm datasource and repository obj for ncos
    const ncoDataSource = await createTypeORMDataSource(logger, auroraSecretArn, 'prod', [
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
    ]);
    const ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);

    let ncos: NewCarOrderModel[] = [];

    ncos = await ncoRepo.find({
      where: { pk_new_car_order_id: In(Object.keys(validated_req.new_car_order_ids)) },
      relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
    });
    logger.log(LogLevel.TRACE, 'old NCOs', { data: ncos });
    /**
     * Validate for every nco
     * nco status
     * source importer !== target importer
     *
     * No checks for old modeltype/ordertype/portcode https://skyway.porsche.com/jira/browse/KASHEARTBE-1336
     */
    const dbModifiedAtMap: Record<string, string> = Object.fromEntries(
      ncos.map((order) => [order.pk_new_car_order_id, transformModifiedAt(order.modified_at)]),
    );
    for (const _nco of ncos) {
      // We are copying the here, so we can make changes on _nco without typeorm committing them
      const nco = JSON.parse(JSON.stringify(_nco)) as NewCarOrderModel;
      // Weird as shit issues here with the config being duplicated by typeorm and completly delete and all other kinds of extreme weirdness
      delete nco.configuration.fk_new_car_order_id;
      delete nco.configuration.pk_config_id;
      const requestModifiedAt = validated_req.new_car_order_ids[nco.pk_new_car_order_id]; // From frontend
      const dbModifiedAt = dbModifiedAtMap[nco.pk_new_car_order_id]; // From DB
      if (requestModifiedAt !== dbModifiedAt) {
        logger.log(LogLevel.WARN, `Timestamp mismatch for order ${nco.pk_new_car_order_id}, skipping update`, {
          data: { dbModifiedAt, requestModifiedAt },
        });
        result.push({
          isSuccess: false,
          old_new_car_order_id: nco.pk_new_car_order_id,
          error_msg: IMPORTER_TRANSFER_ERRORS.RACING_CONDITION,
        });
        continue;
      }
      if (!isImporterTransferStatusValid({ ...nco })) {
        result.push({
          isSuccess: false,
          old_new_car_order_id: nco.pk_new_car_order_id,
          error_msg: IMPORTER_TRANSFER_ERRORS.INVALID_STATUS,
        });
        continue;
      }
      if (nco.importer_number === validated_req.to_importer_number) {
        result.push({
          isSuccess: false,
          old_new_car_order_id: nco.pk_new_car_order_id,
          error_msg: IMPORTER_TRANSFER_ERRORS.SAME_IMPORTER,
        });
        continue;
      }
      if (!nco.quota_month) {
        result.push({
          isSuccess: false,
          old_new_car_order_id: nco.pk_new_car_order_id,
          error_msg: IMPORTER_TRANSFER_ERRORS.NCO_WITHOUT_QUOTA,
        });
        continue;
      }
      const new_nco: NewCarOrderModel = {
        ...removeLocalOptionsFromNco(nco),
        source_obj_id: nco.pk_new_car_order_id,
        source_obj_type: SOURCE_OBJ_TYPES.NEW_CAR_ORDER,
        business_partner_id: undefined,
        dealer_number: validated_req.to_dealer_number,
        importer_number: validated_req.to_importer_number,
        importer_code: importer.code,
      };

      createNcos.push(new_nco as NewCarOrderModelWithQuota); // QuotaCheck above guarantees this
    }
    const createResult = await handleCreate(createNcos, ncoDataSource, ncoRepo, userName, logger);
    logger.log(LogLevel.DEBUG, 'Result', { data: result });
    return result.concat(createResult);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to query new car order table', { data: error });
    const err = error as { message: string; name: string };
    if (
      err.name === 'QueryFailedError' &&
      err.message.includes('could not serialize access due to concurrent update')
    ) {
      Object.keys(validated_req.new_car_order_ids).forEach((nco) => {
        result.push({
          isSuccess: false,
          old_new_car_order_id: nco,
          error_msg: IMPORTER_TRANSFER_ERRORS.RACING_CONDITION,
        });
      });
      return result;
    } else throw new ApiRequestValidationError(500, 'Error loading new car orders');
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(kasLambdaLogger)(event, context, importerTransferFunc);

async function handleCreate(
  ncoRequests: NewCarOrderModelWithQuota[],
  ncoDataSource: DataSource,
  ncoRepo: Repository<NewCarOrderModel>,
  userName: string,
  logger: KasLambdaLogger,
): Promise<CoraNCOImporterTransferApiResponse> {
  if (ncoRequests.length < 1) {
    logger.log(LogLevel.WARN, 'No NCOs to create, check others logs why');
    return [];
  }
  logger.log(LogLevel.DEBUG, 'handleCreate input', { data: ncoRequests });
  const newCarOrderIds = await createNewCarOrderIds(
    { amount: ncoRequests.length, importerCode: ncoRequests[0].importer_code, ncoRepo },
    logger,
  );

  // Map generatedIds onto requests
  for (let i = 0; i < ncoRequests.length; i++) {
    ncoRequests[i].pk_new_car_order_id = newCarOrderIds[i];
  }
  const oldNcoIds = ncoRequests.map((nco) => nco.source_obj_id) as string[]; // source_obj_id must be set to oldNcoId

  try {
    const quotaConsumptionResult = await quotaApiAdapter.consumeMultipleQuota(
      {
        ncoList: ncoRequests,
        mockApi: process.env.ALLOW_QUOTA_API_MOCK === 'true',
        correlationId: logger.getCorrelationId() ?? 'unknown',
      },
      logger,
    );

    if (!quotaConsumptionResult) {
      logger.log(LogLevel.WARN, `Failed to consume quota for ncos`, {
        data: {
          quotaResponse: quotaConsumptionResult,
          requestedNewCarOrders: oldNcoIds,
        },
      });
      return oldNcoIds.map((ncoId) => ({
        isSuccess: false,
        old_new_car_order_id: ncoId,
        error_msg: 'could_not_consume_quota',
      }));
    }

    try {
      const ncoDbObjList = ncoRequests.map((nco) => {
        return {
          ...nco,
          created_by: userName,
          modified_by: userName,
          configuration: ncoConfigApiToDbObj(
            nco.configuration as CoraNCOConfiguration,
            userName,
            nco.pk_new_car_order_id,
          ),
        };
      });
      logger.log(LogLevel.DEBUG, `NewCarOrder object for DB`, { data: ncoDbObjList });
      const createdOrders = await saveNcosWithAuditTrail(
        ncoDataSource,
        [...ncoDbObjList.map((nco) => nco.pk_new_car_order_id), ...oldNcoIds],
        NcoExportActionType.CREATE_IMPORTER_TRANSFER,
        async (transactionManager: EntityManager) => {
          const res: NewCarOrderModel[] = await transactionManager.getRepository(NewCarOrderModel).save(ncoDbObjList);
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: In(oldNcoIds) },
            {
              order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_CANCELLED,
              order_status_onevms_error_code: 'null',
              cancellation_reason: Constants.CANCELLATION_REASONS[8].id, //will be 'A9' as long as hardcoded order does not change...
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: userName,
            },
          );
          const res_created: NewCarOrderModelWithOptionalActionType[] = res.map((nco) => ({
            ...nco,
            action_type: NcoExportActionType.CREATE_IMPORTER_TRANSFER,
          }));
          const _res = await transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: In(oldNcoIds),
              order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_CANCELLED,
            },
          });
          const res_canceled: NewCarOrderModelWithOptionalActionType[] = _res.map((nco) => ({
            ...nco,
            action_type: NcoExportActionType.CANCEL_IMPORTER_TRANSFER,
          }));
          logger.log(LogLevel.DEBUG, 'Transfered new car orders', { data: { res_created, res_canceled } });
          return res_created.concat(res_canceled);
        },
        logger,
      );
      const result: CoraNCOImporterTransferApiResponse = [];
      for (const oldNcoId of oldNcoIds) {
        const createdNco = createdOrders.find((nco) => nco.source_obj_id === oldNcoId);
        const canceledNco = createdOrders.find((nco) => nco.pk_new_car_order_id == oldNcoId);
        if (createdNco && canceledNco) {
          result.push({
            isSuccess: true,
            old_new_car_order_id: canceledNco.pk_new_car_order_id,
            new_new_car_order_id: createdNco.pk_new_car_order_id,
          });
          continue;
        }
        if (createdNco && !canceledNco) {
          result.push({
            isSuccess: false,
            old_new_car_order_id: oldNcoId,
            new_new_car_order_id: createdNco.pk_new_car_order_id,
            error_msg: 'old_did_not_get_canceled',
          });
          continue;
        }
        if (!createdNco && canceledNco) {
          result.push({
            isSuccess: false,
            old_new_car_order_id: oldNcoId,
            error_msg: 'old_canceled_but_no_new_nco',
          });
          continue;
        }
        result.push({
          isSuccess: false,
          old_new_car_order_id: oldNcoId,
          error_msg: 'error_nothing_happened_to_this_nco',
        });
      }
      return result;
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Error saving New Car Orders to database', { data: error });
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      )
        return oldNcoIds.map((ncoId) => ({
          isSuccess: false,
          old_new_car_order_id: ncoId,
          error_msg: IMPORTER_TRANSFER_ERRORS.RACING_CONDITION,
        }));
      else
        return oldNcoIds.map((ncoId) => ({
          isSuccess: false,
          old_new_car_order_id: ncoId,
          error_msg: 'general_db_error',
        }));
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, `Error consuming quota`, { data: error });
    return oldNcoIds.map((ncoId) => ({
      isSuccess: false,
      old_new_car_order_id: ncoId,
      error_msg: 'general_quota_err',
    }));
  }
}
