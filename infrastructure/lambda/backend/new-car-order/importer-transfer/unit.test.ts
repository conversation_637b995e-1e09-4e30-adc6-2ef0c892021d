import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
import ncoWithLocalOptions from '../../../../test/data/new-car-order-model-with-local-options.json';
import { removeLocalOptionsFromNco } from '../../../utils/utils-typeorm';

describe('helper validations', () => {
  it('remove local options test', () => {
    const amountOfOptions = ncoWithLocalOptions.configuration.ordered_options.length;
    const res = removeLocalOptionsFromNco(ncoWithLocalOptions as NewCarOrderModel);
    expect(res.configuration.ordered_options.length).toBe(amountOfOptions - 1);
    const withLocalOptionPackage = res.configuration.ordered_options.find(
      (opt) => opt.option_id === 'WithPackageLocal',
    );
    expect(withLocalOptionPackage).toBeDefined();
    expect(withLocalOptionPackage?.content?.length).toBe(1);
  });
});
