import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { FailedStatusMappingOrderDTO } from '../../../../lib/types/failed-status-mapping-orders';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { secretCache } from '../../../utils/secret-cache';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { FailedStatusMappingOrdersModel } from '../../../../lib/entities/failed-status-mapping-orders-model';
import { PvmsOrderDataDTO } from '../../../../lib/types/pvms-types';

const stage = getEnvVarWithAssert('STAGE');
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const fetchFailedOrderDataFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  let failedOrderObjects: FailedStatusMappingOrdersModel[] = [];
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [
      FailedStatusMappingOrdersModel,
    ]);
    const failedOrdersRepo = dataSource.getRepository(FailedStatusMappingOrdersModel);
    failedOrderObjects = await failedOrdersRepo.find();
    logger.log(LogLevel.DEBUG, `Retrieved ${failedOrderObjects.length} failed orders`);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to fetch FailedOrderListObject', { data: { error: error } });
    return sendFail({ message: 'Error loading failed orders', status: 500, reqHeaders: event.headers }, logger);
  }

  const failedOrders: FailedStatusMappingOrderDTO[] = failedOrderObjects.map(convertToFailedStatusMappingOrderDTO);
  // filter part later?
  failedOrders.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  return sendSuccess({ body: failedOrders, reqHeaders: event.headers }, logger);
};

function convertToFailedStatusMappingOrderDTO(source: FailedStatusMappingOrdersModel): FailedStatusMappingOrderDTO {
  let pvmsOrderDataDTO: PvmsOrderDataDTO;
  //jsonb type will return a deserialized javsscript object
  if (typeof source.value === 'object') {
    pvmsOrderDataDTO = source.value as PvmsOrderDataDTO;
  } else {
    pvmsOrderDataDTO = JSON.parse(source.value as string) as PvmsOrderDataDTO;
  }
  return {
    key: source.key,
    numOfTries: source.num_of_tries,
    timestamp: source.timestamp,
    value: {
      ids: pvmsOrderDataDTO.ids,
      order_info: pvmsOrderDataDTO.order_info,
    },
  };
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-failed-orders', LogLevel.TRACE)(event, context, fetchFailedOrderDataFunc);
