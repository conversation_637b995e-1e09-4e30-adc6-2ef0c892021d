/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../utils/integration-test-helpers';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { DataSource, In, Repository } from 'typeorm';
import { Constants } from '../../../lib/utils/constants';
import { CoraOrgRelModel } from '../../../lib/types/boss-org-types';
import { CoraNCOBuySellTransferApiRequest } from '../../../lib/types/new-car-order-types';
import { OneVmsSourceSystemKey } from '../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn('buy-sell-transfer');
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);

//org relations test data
const importerOrgId = '48efff89-7721-4b7b-920f-d0a752af9d6c';
const dealerGroupOrgId = 'fab3d227-1ef8-417d-b673-b188c9db1caa';
const dealerOrgId1 = '2f241d3f-034b-482c-bee5-5f600130252f';
const dealerOrgId2 = '1ec63370-7a42-4ce5-906f-d60565e00c78';
const unrelatedDealerOrgId = 'c10f084d-2dc1-42d4-b537-306177025c3e';
const orgWithoutDealers = '06e342e2-b087-42fe-aaa2-e36af35bae79';
const dealerNr1 = '1122334';
const dealerNr2 = '4455667';

const orgRels: CoraOrgRelModel[] = [
  //importer relation to itself
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoTransferImp',
    display_name: 'IntegrationTest - Importer',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //org without dealers relation to itself
  {
    pk_ppn_id: orgWithoutDealers,
    parent_ppn_id: orgWithoutDealers,
    display_name: 'IntegrationTest - Importer2',
    importer_number: 'ItNcoTransferImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to parent importer
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: importerOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to itself
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: dealerGroupOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: dealerNr1,
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId,
    dealer_number: dealerNr1,
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: dealerNr1,
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: dealerNr2,
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId,
    dealer_number: dealerNr2,
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: true,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: dealerNr2,
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoTransferImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //unrelated dealer relation to itself
  {
    pk_ppn_id: unrelatedDealerOrgId,
    parent_ppn_id: unrelatedDealerOrgId,
    dealer_number: 'ItUnrelatedDlr',
    display_name: 'IntegrationTest - Unrelated Dlr',
    importer_number: 'ItUnrelatedImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

//existing nco test objects
const ncoDlr1: NewCarOrderModel = {
  pk_new_car_order_id: 'ITTEST1',
  dealer_number: dealerNr1,
  importer_code: 'IT',
  importer_number: 'ItNcoTransferImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '202311',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'order_status_changeable',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_PAG_ORDER_INVOICED,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const ncoDlr1OC: NewCarOrderModel = {
  pk_new_car_order_id: 'ITTEST2',
  dealer_number: dealerNr1,
  importer_code: 'IT',
  importer_number: 'ItNcoTransferImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'OCXXXX',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_PAG_ORDER_INVOICED,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const InitBuySellTransferBody = {
  source_dealer_number: '123',
  target_dealer_number: '456',
  new_car_order_ids: ['ITTEST1', 'ITTEST2'],
} satisfies CoraNCOBuySellTransferApiRequest;

const CorrectBySellTransferBody = {
  source_dealer_number: dealerNr1,
  target_dealer_number: dealerNr2,
  new_car_order_ids: ['ITTEST1'],
};

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([NewCarOrderModel, NcoConfigurationModel, NcoConfigOrderedOptionsModel]);
  repository = dataSource.getRepository(NewCarOrderModel);

  await repository.save(ncoDlr1);
  await repository.save(ncoDlr1OC);

  await prepareDynamodb([{ tableName: orgRelTableName, objs: orgRels }]);
});

afterAll(async () => {
  await repository.delete({
    pk_new_car_order_id: In([ncoDlr1.pk_new_car_order_id, ncoDlr1OC.pk_new_car_order_id]),
  });
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
  ]);
});

afterEach(async () => {
  //reset orders after every test
  await repository.save(ncoDlr1);
  await repository.save(ncoDlr1OC);
});

it('should return 400 if orderId list is empty', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    body: { ...InitBuySellTransferBody, new_car_order_ids: [] } satisfies CoraNCOBuySellTransferApiRequest,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(400);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('Invalid request parameters');
});

it('should return 403 if user has no access to any dealers', async () => {
  const event = createApiGwEvent({
    userOrgId: orgWithoutDealers,
    body: InitBuySellTransferBody satisfies CoraNCOBuySellTransferApiRequest,
  });
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(403);
  const body = JSON.parse(res.body);
  expect(body.message).not.toBeUndefined();
  expect(body.message).toContain('User is not authorized for any dealers');
  expect(body[ncoDlr1.pk_new_car_order_id]).toBeUndefined();
});

it('should return 200 if one order could be found', async () => {
  const event = createApiGwEvent({
    userOrgId: importerOrgId,
    body: {
      ...CorrectBySellTransferBody,
    } satisfies CoraNCOBuySellTransferApiRequest,
  });

  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);

  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();

  expect(body.new_car_order_ids).toBeDefined();
  expect(Array.isArray(body.new_car_order_ids)).toBe(true);
  expect(body.new_car_order_ids.length).toBe(1);
  expect(body.new_car_order_ids[0]).toEqual(ncoDlr1.pk_new_car_order_id);
});
