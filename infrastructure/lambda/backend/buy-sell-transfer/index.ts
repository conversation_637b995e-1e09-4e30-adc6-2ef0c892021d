import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { EntityManager, In } from 'typeorm';

import { getUserName, sendFail, sendSuccess } from '../../utils/api-helpers';
import { getAllowedDealers } from '../../utils/validation-helpers';
import { getEnvVarWithAssert } from '../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import { secretCache } from '../../utils/secret-cache';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { CoraNCOBuySellTransferApiRequest } from '../../../lib/types/new-car-order-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { saveNcosWithAuditTrail } from '../../utils/utils-typeorm';
import { CoraOrgRelModel } from '../../../lib/types/boss-org-types';
import { NewCarOrderAuditTrailModel } from '../../../lib/entities/new-car-order-audit-trail-model';
import { NcoExportActionType } from '../export-nco/types';
import { OneVmsSourceSystemKey } from '../../../lib/types/process-steering-types';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);
const objectValidator = new ObjectValidator<CoraNCOBuySellTransferApiRequest>('CoraNCOBuySellTransferApiRequest');

const buySellTransferFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.DEBUG, 'Buy sell transfer invoked', { data: event.body });

  let requestBody: unknown;
  try {
    requestBody = JSON.parse(event.body ?? '{}');
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Invalid JSON in request body', { data: error });
    return sendFail({ message: 'Invalid JSON format in request body', status: 400, reqHeaders: event.headers }, logger);
  }

  const [validatedBody, validationErrors] = objectValidator.validate(requestBody);
  if (!validatedBody) {
    logger.log(LogLevel.WARN, 'AJV validation failed', { data: validationErrors });
    return sendFail(
      {
        message: `Invalid request parameters: ${JSON.stringify(validationErrors)}`,
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  logger.log(LogLevel.DEBUG, 'Request validated successfully', { data: validatedBody });

  const {
    source_dealer_number: sourceDealer,
    target_dealer_number: targetDealer,
    new_car_order_ids: inputOrderIds,
  } = validatedBody;

  if (sourceDealer === targetDealer) {
    logger.log(LogLevel.WARN, 'Source and target dealer numbers are identical');
    return sendFail(
      { message: 'Source and target dealer numbers must be different', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  const allowedDealers = await getAllowedDealers({ dynamoDb, event, includeDeactivated: true }, logger);
  if (allowedDealers.length === 0) {
    return sendFail(
      { message: 'User is not authorized for any dealers', status: 403, reqHeaders: event.headers },
      logger,
    );
  }

  const { sourceAuthorized, targetAuthorized, sourceImporter, targetImporter } = validateDealerAuthorization(
    allowedDealers,
    sourceDealer,
    targetDealer,
  );

  if (!sourceAuthorized || !targetAuthorized) {
    return sendFail({ message: 'Invalid dealer numbers', status: 403, reqHeaders: event.headers }, logger);
  }
  if (!sourceImporter || !targetImporter || sourceImporter !== targetImporter) {
    return sendFail(
      { message: 'Source and target dealers belong to different importers', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, 'prod', [
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
  ]);
  const ncoRepo = dataSource.getRepository(NewCarOrderModel);
  let dbOrders: NewCarOrderModel[] = [];

  // Query for new car orders that have the source dealer_number and the onevms status is not OC or OX
  try {
    dbOrders = await ncoRepo
      .createQueryBuilder('order')
      .select('order.pk_new_car_order_id')
      .where('order.dealer_number = :dealerNumber', { dealerNumber: sourceDealer })
      .andWhere('order.order_status_onevms_code NOT LIKE :ocPrefix', { ocPrefix: 'OC%' })
      .andWhere('order.order_status_onevms_code NOT LIKE :oxPrefix', { oxPrefix: 'OX%' })
      .getMany();
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error fetching orders from database', { data: error });
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  const dbOrderIds = dbOrders.map((order) => order.pk_new_car_order_id);

  if (inputOrderIds.length !== dbOrderIds.length) {
    return sendFail(
      { message: 'Input order IDs and database order IDs differ in count', status: 400, reqHeaders: event.headers },
      logger,
    );
  }
  const sortedInputIds = [...inputOrderIds].sort();
  const sortedDbIds = [...dbOrderIds].sort();
  const orderIdsMatch = sortedInputIds.every((id, index) => id === sortedDbIds[index]);
  if (!orderIdsMatch) {
    return sendFail(
      { message: 'Input order IDs do not match the database order IDs', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  try {
    await saveNcosWithAuditTrail(
      dataSource,
      dbOrderIds,
      NcoExportActionType.TRANSFER_BUY_SELL,
      async (transactionManager: EntityManager) => {
        await transactionManager.getRepository(NewCarOrderModel).update(
          { pk_new_car_order_id: In(dbOrderIds) },
          {
            dealer_number: targetDealer,
            changed_by_system: OneVmsSourceSystemKey.CORA,
            order_status_onevms_timestamp_last_change: new Date().toISOString(),
            modified_by: getUserName({ event }, logger),
          },
        );
        const updatedOrders = await transactionManager.getRepository(NewCarOrderModel).find({
          where: { pk_new_car_order_id: In(dbOrderIds) },
        });
        logger.log(LogLevel.DEBUG, 'Updated new car orders', { data: updatedOrders });
        return updatedOrders;
      },
      logger,
    );
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to update new car orders', { data: error });
    return sendFail(
      { message: 'Failed to execute the buy sell transfer', status: 500, reqHeaders: event.headers },
      logger,
    );
  }

  return sendSuccess({ body: { new_car_order_ids: dbOrderIds }, reqHeaders: event.headers }, logger);
};

function validateDealerAuthorization(
  allowedDealers: CoraOrgRelModel[],
  sourceDealer: string,
  targetDealer: string,
): {
  sourceAuthorized: boolean;
  targetAuthorized: boolean;
  sourceImporter?: string;
  targetImporter?: string;
} {
  let sourceAuthorized = false;
  let targetAuthorized = false;
  let sourceImporter: string | undefined;
  let targetImporter: string | undefined;

  for (const dealer of allowedDealers) {
    if (dealer.dealer_number === sourceDealer) {
      sourceAuthorized = true;
      sourceImporter = dealer.importer_number;
    }
    if (dealer.dealer_number === targetDealer) {
      targetAuthorized = true;
      targetImporter = dealer.importer_number;
    }
  }
  return { sourceAuthorized, targetAuthorized, sourceImporter, targetImporter };
}

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('buy-sell-transfer', LogLevel.TRACE)(event, context, buySellTransferFunc);
