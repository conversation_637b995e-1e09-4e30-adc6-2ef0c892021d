import { Brackets, SelectQueryBuilder, WhereExpressionBuilder } from 'typeorm';
import { CoraMdOneVmsStatus } from '../../../lib/types/masterdata-types';
import { CoraNCOBaseApiResponse, FilterModelRequest, NCOActionFilters } from '../../../lib/types/new-car-order-types';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { ScanCommand, ScanCommandInput } from '@aws-sdk/lib-dynamodb';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { FilterModel, SetFilterModel, TextFilterModel } from '../../../lib/types/ag-grid-ssrm-types';
import { NewCarOrderModel } from '../../../lib/entities/new-car-order-model';

function _getStatusQuery(
  status: CoraMdOneVmsStatus,
  index: number,
): { queryString: string; queryParams: Record<string, string | null> } {
  return {
    queryString: `new_car_order.order_status_onevms_code = :statusCode${index} AND new_car_order.order_status_onevms_error_code = :errorCode${index}`,
    queryParams: {
      [`statusCode${index}`]: status.one_vms_status,
      [`errorCode${index}`]: status.one_vms_error_status,
    },
  };
}
export function getStatusWhereQuery(statusCodesForQuery: CoraMdOneVmsStatus[]): Brackets {
  return new Brackets((qb) => {
    statusCodesForQuery.forEach((status, index) => {
      const _query = _getStatusQuery(status, index);
      if (index === 0) {
        //find all orders that have the exact combination of statusCode and errorCode. Because we have multiple, we need a forEach loop
        //index === 0 is the first iteration, so we use where instead of orWhere
        qb.where(_query.queryString, _query.queryParams);
      } else {
        //index > 0 is the following iterations, so we use orWhere
        qb.orWhere(_query.queryString, _query.queryParams);
      }
    });
  });
}

export async function getCompositeStatusFilterForActionFilters(
  actionFilters: NCOActionFilters,
  dynamoDB: DynamoDBClient,
  statusMappingTableName: string,
  logger: KasLambdaLogger,
): Promise<CoraMdOneVmsStatus[]> {
  const result = await dynamoDB.send(new ScanCommand(buildScanParams(statusMappingTableName, actionFilters)));
  logger.log(LogLevel.TRACE, 'Got Statusmapping result from dynamo:', { data: result });

  if (!result.Items) {
    return [];
  }
  return result.Items as CoraMdOneVmsStatus[];
}

function buildScanParams(tableName: string, actionFilters: NCOActionFilters): ScanCommandInput {
  const params: ScanCommandInput = {
    TableName: tableName,
  };

  const filterExpressions = [];
  const expressionAttributeValues: Record<string, unknown> = {};

  switch (actionFilters.changeable) {
    case true:
      filterExpressions.push('order_changeable = :changeable');
      expressionAttributeValues[':changeable'] = true;
      break;
    case false:
      filterExpressions.push('order_changeable = :changeable');
      expressionAttributeValues[':changeable'] = false;
      break;
    default:
    // Do nothing if undefined
  }

  if (filterExpressions.length > 0) {
    params.FilterExpression = filterExpressions.join(' AND ');
    params.ExpressionAttributeValues = expressionAttributeValues;
  }

  return params;
}

function agGridSsrmCreateTextFilterSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel> | WhereExpressionBuilder,
  key: keyof CoraNCOBaseApiResponse,
  filter: TextFilterModel,
  useOrWhere: boolean = false,
  index: number = 0,
): void {
  switch (filter.type) {
    case 'equals':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} = :${key}${index}`, { [`${key}${index}`]: filter.filter });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} = :${key}${index}`, { [`${key}${index}`]: filter.filter });
      }
      break;
    case 'notEqual':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} != :${key}${index}`, { [`${key}${index}`]: filter.filter });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} != :${key}${index}`, { [`${key}${index}`]: filter.filter });
      }
      break;
    case 'contains':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      }
      break;
    case 'notContains':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} NOT ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} NOT ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      }
      break;
    case 'startsWith':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, { [`${key}${index}`]: `%${filter.filter}` });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}`,
        });
      }
      break;
    case 'endsWith':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, { [`${key}${index}`]: `${filter.filter}%` });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `${filter.filter}%`,
        });
      }
  }
}

function agGridSsrmCreateSetFilterSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  key: keyof CoraNCOBaseApiResponse,
  filter: SetFilterModel,
): void {
  queryBuilder.andWhere(`new_car_order.${key} IN (:...${key}s)`, { [`${key}s`]: filter.values });
}

export function agGridSsrmCreateWhereSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  filterModel: FilterModelRequest,
  logger: KasLambdaLogger,
): void {
  const keySet = Object.entries(filterModel) as [keyof CoraNCOBaseApiResponse, FilterModel][];
  for (const [k, v] of keySet) {
    if ('operator' in v) {
      // JoinFilterModel
      queryBuilder.andWhere(
        new Brackets((qb) => {
          let index = 0;
          for (const condition of v.conditions) {
            agGridSsrmCreateTextFilterSql(qb, k, condition, v.operator === 'OR', index);
            index++;
          }
        }),
      );
    } else if (v.filterType === 'text') {
      // TextFilterModel
      agGridSsrmCreateTextFilterSql(queryBuilder, k, v);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } else if (v.filterType === 'set') {
      // SetFilterModel
      agGridSsrmCreateSetFilterSql(queryBuilder, k, v);
    } else {
      logger.log(LogLevel.ERROR, 'Unknown filter type', { data: v });
    }
  }
}
