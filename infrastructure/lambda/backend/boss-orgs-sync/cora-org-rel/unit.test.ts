//Add the mocks for kafka module and process environments
const groupId = 'offset_lag_zero_groupId';
const topic = 'offset_lag_zero_topic';
const coraOrgRelName = 'cora-org-rel';
const bossOrgName = 'boss-org';
process.env.GROUP_ID = groupId;
process.env.KAFKA_TOPIC = topic;
process.env.TABLE_NAME_BOSS_ORG = bossOrgName;
process.env.TABLE_NAME_CORA_ORG_REL = coraOrgRelName;

import * as fs from 'fs';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import { Constants } from '../../../../lib/utils/constants';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Batch<PERSON>riteCommand, QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoDBStreamEvent } from 'aws-lambda';
import path from 'path';
import { BossOrgModel, CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { dummyKafkaSecret, mockContext } from '../../../utils/test-utils';

const ddbMock = mockClient(DynamoDBClient);
const smMock = mockClient(SecretsManagerClient);
smMock.on(GetSecretValueCommand).resolves({ SecretString: JSON.stringify(dummyKafkaSecret) });
jest.mock('../../../utils/kafka', () => {
  return {
    KafkaAdapter: jest.fn().mockImplementation(() => {
      return {
        isConsumerOffsetLagZero: jest.fn().mockResolvedValue(true),
      };
    }),
  };
});

// Import MUST be after mock!
import { handler } from '.';

beforeEach(() => {
  ddbMock.reset();
  jest.resetModules();
});

const defBossOrgProps = {
  ppn_importer_partner_number: null,
  ppn_porsche_partner_no: null,
  ppn_status: 'OPERATIVE',
  is_deactivated: false,
};

const defCorProps = {
  importer_number: '',
  dealer_number: '',
  is_deactivated: false,
  ppn_status: 'OPERATIVE',
};

const bossOrgScanRes_withCycle = {
  Items: [
    {
      ...defBossOrgProps,
      pk_ppn_id: '1',
      parent_ppn_id: null,
      additional_children: null,
      display_name: 'Node 1',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '2',
      parent_ppn_id: '1',
      additional_children: null,
      display_name: 'Node 2',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '3',
      parent_ppn_id: '1',
      additional_children: null,
      display_name: 'Node 3',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '4',
      parent_ppn_id: '3',
      additional_children: null,
      display_name: 'Node 4',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '5',
      parent_ppn_id: '3',
      additional_children: null,
      display_name: 'Node 5',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '6',
      parent_ppn_id: '4',
      additional_children: null,
      display_name: 'Node 6',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '7',
      parent_ppn_id: '4',
      additional_children: ['3'],
      display_name: 'Node 7',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '8',
      parent_ppn_id: '5',
      additional_children: null,
      display_name: 'Node 5',
    },
    {
      ...defBossOrgProps,
      pk_ppn_id: '9',
      parent_ppn_id: '5',
      additional_children: null,
      display_name: 'Node 9',
    },
  ],
  Count: 9,
  ScannedCount: 9,
};

const allBossOrgsFromDDB: BossOrgModel[] = [];

describe('To Test with the real boss orgs. When the ConsumerOffsetLag is zero and the table cora-org-rel is empty', () => {
  const insertEvent: DynamoDBStreamEvent = {
    Records: [
      {
        eventID: '1',
        eventName: 'INSERT',
        eventVersion: '1.0',
        eventSource: 'aws:dynamodb',
        awsRegion: 'eu-west-1',
        dynamodb: {
          ApproximateCreationDateTime: 1628655861,
          Keys: {
            pk_ppn_id: { S: '9' },
          },
          NewImage: {
            pk_ppn_id: { S: '9' },
            parent_ppn_id: { S: '5' },
            additional_children: { NULL: true },
            ppn_importer_partner_number: { NULL: true },
            ppn_porsche_partner_no: { NULL: true },
            ppn_status: { S: 'OPERATIVE' },
            is_deactivated: { BOOL: false },
          },
          StreamViewType: 'NEW_IMAGE',
        },
        eventSourceARN: 'dynamodb_arn',
      },
    ],
  };

  beforeAll((done) => {
    const testData = 'test-data';
    const testDataPath = path.join(__dirname, testData);

    fs.readdir(testDataPath, (err, files) => {
      if (err) {
        console.error('Error reading directory:', err);
        done();
        return;
      }

      let filesProcessed = 0;
      files.forEach((file) => {
        const filePath = path.join(testDataPath, file);
        fs.readFile(filePath, 'utf8', (error, fileContents) => {
          if (error) {
            console.error(`Error reading file ${file}:`, error);
            done();
            return;
          }

          const lines = fileContents.split('\n');
          lines
            .filter((line) => line.trim())
            .forEach((line) => {
              /* eslint-disable @typescript-eslint/no-unsafe-assignment */
              const dynamoDBJSON = JSON.parse(line);
              /* eslint-disable @typescript-eslint/no-unsafe-argument  */
              /* eslint-disable @typescript-eslint/no-unsafe-member-access  */
              allBossOrgsFromDDB.push(unmarshall(dynamoDBJSON.Item) as BossOrgModel);
            });

          filesProcessed++;
          if (filesProcessed === files.length) {
            console.log('The count of Boss Orgs is', allBossOrgsFromDDB.length);
            done();
          }
        });
      });
    });
  });
  it('the handler should start the first flat calculation of the complete boss orgs and retry processing UnprocessedItems', async () => {
    const scanResponse = {
      Items: allBossOrgsFromDDB,
    };

    const unprocessedItems = {
      [coraOrgRelName]: [
        {
          PutRequest: {
            Item: { pk_ppn_id: '1', parent_ppn_id: '1' },
          },
        },
        {
          PutRequest: {
            Item: { pk_ppn_id: '2', parent_ppn_id: '1' },
          },
        },
        {
          PutRequest: {
            Item: { pk_ppn_id: '3', parent_ppn_id: '1' },
          },
        },
      ],
    };

    ddbMock
      .on(BatchWriteCommand)
      .resolvesOnce({ UnprocessedItems: unprocessedItems })
      .resolves({ UnprocessedItems: {} });

    ddbMock
      .on(ScanCommand, { TableName: coraOrgRelName })
      .resolves({})
      .on(ScanCommand, { TableName: bossOrgName })
      .resolves(scanResponse);

    await handler(insertEvent, mockContext);
  });
});

describe('When the ConsumerOffsetLag is zero and the table cora-org-rel is empty', () => {
  const insertEvent: DynamoDBStreamEvent = {
    Records: [
      {
        eventID: '1',
        eventName: 'INSERT',
        eventVersion: '1.0',
        eventSource: 'aws:dynamodb',
        awsRegion: 'eu-west-1',
        dynamodb: {
          ApproximateCreationDateTime: 1628655861,
          Keys: {
            pk_ppn_id: { S: '9' },
          },
          NewImage: {
            pk_ppn_id: { S: '9' },
            parent_ppn_id: { S: '5' },
            additional_children: { NULL: true },
            ppn_importer_partner_number: { NULL: true },
            ppn_porsche_partner_no: { NULL: true },
            ppn_status: { S: 'OPERATIVE' },
            is_deactivated: { BOOL: false },
          },
          StreamViewType: 'NEW_IMAGE',
        },
        eventSourceARN: 'dynamodb_arn',
      },
    ],
  };
  it('the handler should start the first flat calculation', async () => {
    ddbMock.on(BatchWriteCommand).resolves({ UnprocessedItems: {} });
    ddbMock
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves({})
      .on(ScanCommand, {
        TableName: bossOrgName,
      })
      .resolves(bossOrgScanRes_withCycle);
    await handler(insertEvent, mockContext);

    const coraOrgRelModels: CoraOrgRelModel[] = [];
    expect(ddbMock.commandCalls(BatchWriteCommand).length).toBe(2);

    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        expect(request.DeleteRequest).toBeUndefined();
        expect(request.PutRequest).toBeDefined();
        expect(request.PutRequest?.Item).not.toEqual({});
        coraOrgRelModels.push(request.PutRequest?.Item as CoraOrgRelModel);
      });
    });

    expect(coraOrgRelModels.length).toBe(37);
    const record = convertToParentChildrenRecord(coraOrgRelModels);
    expect(record['7']).toBe('3,4,5,6,7,8,9');
    expect(record['4']).toBe('3,4,5,6,7,8,9');
  });
});

const corScanRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '1',
      parent_ppn_id: '1',
      display_name: 'Node 1',
    },
    {
      ...defCorProps,
      pk_ppn_id: '2',
      parent_ppn_id: '2',
      display_name: 'Node 2',
    },
    {
      ...defCorProps,
      pk_ppn_id: '2',
      parent_ppn_id: '1',
      display_name: 'Node 2',
    },
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '3',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '1',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '7',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '4',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '4',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '3',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '1',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '7',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '5',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '3',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '1',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '7',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '4',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '6',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '4',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '3',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '1',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '7',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '7',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '4',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '3',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '1',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '8',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '5',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '3',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '1',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '7',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '4',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '9',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '5',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '3',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '1',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '7',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '4',
      display_name: 'Node 9',
    },
  ],
};

const sk3QueryRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '3',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '3',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '3',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '3',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '3',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '3',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '3',
      display_name: 'Node 9',
    },
  ],
};

const sk7QueryRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '7',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '4',
      parent_ppn_id: '7',
      display_name: 'Node 4',
    },
    {
      ...defCorProps,
      pk_ppn_id: '3',
      parent_ppn_id: '7',
      display_name: 'Node 3',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '7',
      display_name: 'Node 5',
    },
    {
      ...defCorProps,
      pk_ppn_id: '6',
      parent_ppn_id: '7',
      display_name: 'Node 6',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '7',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '7',
      display_name: 'Node 9',
    },
  ],
};

const sk5QueryRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '5',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '9',
      parent_ppn_id: '5',
      display_name: 'Node 9',
    },
    {
      ...defCorProps,
      pk_ppn_id: '5',
      parent_ppn_id: '5',
      display_name: 'Node 5',
    },
  ],
};

const pk7QueryRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '7',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '4',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '3',
      display_name: 'Node 7',
    },
    {
      ...defCorProps,
      pk_ppn_id: '7',
      parent_ppn_id: '1',
      display_name: 'Node 7',
    },
  ],
};

const pk8QueryRes = {
  Items: [
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '1',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '3',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '5',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '4',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '7',
      display_name: 'Node 8',
    },
    {
      ...defCorProps,
      pk_ppn_id: '8',
      parent_ppn_id: '8',
      display_name: 'Node 8',
    },
  ],
};

describe('When the ConsumerOffsetLag is zero and the table cora-org-rel is not empty, proceed to handle the insert action', () => {
  it('handle the single insert event', async () => {
    const insertEvent: DynamoDBStreamEvent = {
      Records: [
        {
          eventID: '1',
          eventName: 'INSERT',
          eventVersion: '1.0',
          eventSource: 'aws:dynamodb',
          awsRegion: 'eu-west-1',
          dynamodb: {
            ApproximateCreationDateTime: 1628655861,
            Keys: {
              pk_ppn_id: { S: 'your_ppn_id' },
            },
            NewImage: {
              pk_ppn_id: { S: '10' },
              parent_ppn_id: { S: '7' },
              additional_children: { L: [{ S: '3' }] },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            StreamViewType: 'NEW_IMAGE',
          },
          eventSourceARN: 'your_dynamodb_arn',
        },
      ],
    };
    ddbMock
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '7',
        },
      })
      .resolves({ Items: pk7QueryRes.Items })
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '3',
        },
      })
      .resolves(sk3QueryRes)
      .on(BatchWriteCommand)
      .resolves({})
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves(corScanRes);
    await handler(insertEvent, mockContext);

    const coraOrgRelModels: CoraOrgRelModel[] = [];
    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        expect(request.DeleteRequest).toBeUndefined();
        expect(request.PutRequest).toBeDefined();
        expect(request.PutRequest?.Item).not.toEqual({});
        coraOrgRelModels.push(request.PutRequest?.Item as CoraOrgRelModel);
      });
    });

    expect(coraOrgRelModels.length).toBe(40);
    const record = convertToParentChildrenRecord(coraOrgRelModels);
    expect(record['10']).toBe('10,3,4,5,6,7,8,9');
  });
});

describe('When the ConsumerOffsetLag is zero and the table cora-org-rel is not empty, proceed to handle the modify action', () => {
  it('Relocate the node 5 to become a child of node 2', async () => {
    const pk4QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '1',
          display_name: 'Node 4',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '3',
          display_name: 'Node 4',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '4',
          display_name: 'Node 4',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '7',
          display_name: 'Node 4',
        },
      ],
    };
    const pk2QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '1',
          display_name: 'Node 2',
        },
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '2',
          display_name: 'Node 2',
        },
      ],
    };

    const modifyEvent: DynamoDBStreamEvent = {
      Records: [
        {
          eventID: '1',
          eventName: 'MODIFY',
          eventVersion: '1.0',
          eventSource: 'aws:dynamodb',
          awsRegion: 'eu-west-1',
          dynamodb: {
            ApproximateCreationDateTime: 1628655861,
            Keys: {
              pk_ppn_id: { S: '7' },
            },
            OldImage: {
              pk_ppn_id: { S: '7' },
              parent_ppn_id: { S: '4' },
              additional_children: {
                L: [{ S: '3' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            NewImage: {
              pk_ppn_id: { S: '7' },
              parent_ppn_id: { S: '2' },
              additional_children: {
                L: [{ S: '3' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            StreamViewType: 'NEW_IMAGE',
          },
          eventSourceARN: 'your_dynamodb_arn',
        },
      ],
    };
    ddbMock
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '7',
        },
      })
      .resolves(pk7QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '4',
        },
      })
      .resolves(pk4QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '2',
        },
      })
      .resolves(pk2QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '7',
        },
      })
      .resolves(sk7QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '5',
        },
      })
      .resolves(sk5QueryRes);
    ddbMock.on(BatchWriteCommand).resolves({});
    bossOrgScanRes_withCycle.Items[6].parent_ppn_id = '2';
    ddbMock
      .on(ScanCommand, {
        TableName: bossOrgName,
      })
      .resolves(bossOrgScanRes_withCycle)
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves(corScanRes);
    await handler(modifyEvent, mockContext);

    const toPutCors: CoraOrgRelModel[] = [];
    const toDeleteCors: { pk_ppn_id: string; parent_ppn_id: string }[] = [];
    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        if (request.PutRequest) {
          toPutCors.push(request.PutRequest.Item as CoraOrgRelModel);
        }
        if (request.DeleteRequest) {
          toDeleteCors.push(request.DeleteRequest.Key as { pk_ppn_id: string; parent_ppn_id: string });
        }
      });
    });

    expect(toDeleteCors.length).toBe(5);
    expect(toPutCors.length).toBe(14);
    const toPutParentChildrenRecord = convertToParentChildrenRecord(toPutCors);
    expect(toPutParentChildrenRecord['2']).toBe('3,4,5,6,7,8,9');
    expect(toPutParentChildrenRecord['1']).toBe('3,4,5,6,7,8,9');
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '7' && toDel.parent_ppn_id === '4')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '3' && toDel.parent_ppn_id === '4')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '5' && toDel.parent_ppn_id === '4')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '8' && toDel.parent_ppn_id === '4')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '9' && toDel.parent_ppn_id === '4')).toBe(true);
  });

  it('move the node 2 to become a child of node 4 to test whether the flag is_relevant_for_order_create is correctly setted', async () => {
    const pk2QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '1',
          display_name: 'Node 2',
        },
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '2',
          display_name: 'Node 2',
        },
      ],
    };
    const pk4QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '1',
          display_name: 'Node ',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '3',
          display_name: 'Node 4',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '4',
          display_name: 'Node 4',
        },
        {
          ...defCorProps,
          pk_ppn_id: '4',
          parent_ppn_id: '7',
          display_name: 'Node 4',
        },
      ],
    };
    const pk1QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '1',
          parent_ppn_id: '1',
          display_name: 'Node 1',
        },
      ],
    };
    const sk2QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '2',
          display_name: 'Node 2',
        },
        {
          ...defCorProps,
          pk_ppn_id: '8',
          parent_ppn_id: '2',
          display_name: 'Node 8',
        },
      ],
    };

    const modifyEvent: DynamoDBStreamEvent = {
      Records: [
        {
          eventID: '1',
          eventName: 'MODIFY',
          eventVersion: '1.0',
          eventSource: 'aws:dynamodb',
          awsRegion: 'eu-west-1',
          dynamodb: {
            ApproximateCreationDateTime: 1628655861,
            Keys: {
              pk_ppn_id: { S: '2' },
            },
            OldImage: {
              pk_ppn_id: { S: '2' },
              parent_ppn_id: { S: '1' },
              additional_children: {
                L: [{ S: '8' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: true },
              is_relevant_for_order_create: { BOOL: false },
            },
            NewImage: {
              pk_ppn_id: { S: '2' },
              parent_ppn_id: { S: '4' },
              additional_children: {
                L: [{ S: '8' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: true },
              is_relevant_for_order_create: { BOOL: true },
            },
            StreamViewType: 'NEW_IMAGE',
          },
          eventSourceARN: 'your_dynamodb_arn',
        },
      ],
    };
    ddbMock
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '7',
        },
      })
      .resolves(pk7QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '1',
        },
      })
      .resolves(pk1QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '4',
        },
      })
      .resolves(pk4QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '2',
        },
      })
      .resolves(pk2QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '7',
        },
      })
      .resolves(sk7QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '5',
        },
      })
      .resolves(sk5QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '2',
        },
      })
      .resolves(sk2QueryRes);

    ddbMock.on(BatchWriteCommand).resolves({});
    bossOrgScanRes_withCycle.Items[1].parent_ppn_id = '4';
    ddbMock
      .on(ScanCommand, {
        TableName: bossOrgName,
      })
      .resolves(bossOrgScanRes_withCycle)
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves(corScanRes);
    await handler(modifyEvent, mockContext);

    const toPutCors: CoraOrgRelModel[] = [];
    const toDeleteCors: { pk_ppn_id: string; parent_ppn_id: string }[] = [];
    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        if (request.PutRequest) {
          toPutCors.push(request.PutRequest.Item as CoraOrgRelModel);
        }
        if (request.DeleteRequest) {
          toDeleteCors.push(request.DeleteRequest.Key as { pk_ppn_id: string; parent_ppn_id: string });
        }
      });
    });

    expect(toDeleteCors.length).toBe(0);
    expect(toPutCors.length).toBe(8);
    toPutCors.forEach((toPutCor) => {
      if (toPutCor.pk_ppn_id === '2') {
        expect(toPutCor.is_relevant_for_order_create).toBe(true);
      }
    });
  });

  it('Execute the single update action for the additional_children attribute. Specifically, change the value of additional_children for node 7 from 3 to 5.', async () => {
    const modifyEvent: DynamoDBStreamEvent = {
      Records: [
        {
          eventID: '1',
          eventName: 'MODIFY',
          eventVersion: '1.0',
          eventSource: 'aws:dynamodb',
          awsRegion: 'eu-west-1',
          dynamodb: {
            ApproximateCreationDateTime: 1628655861,
            Keys: {
              pk_ppn_id: { S: '7' },
            },
            OldImage: {
              pk_ppn_id: { S: '7' },
              parent_ppn_id: { S: '4' },
              additional_children: {
                L: [{ S: '3' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            NewImage: {
              pk_ppn_id: { S: '7' },
              parent_ppn_id: { S: '4' },
              additional_children: {
                L: [{ S: '5' }],
              },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            StreamViewType: 'NEW_IMAGE',
          },
          eventSourceARN: 'your_dynamodb_arn',
        },
      ],
    };
    ddbMock
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
      })
      .resolves(pk7QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '3',
        },
      })
      .resolves(sk3QueryRes)
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
        KeyConditionExpression: 'parent_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '5',
        },
      })
      .resolves(sk5QueryRes);
    ddbMock.on(BatchWriteCommand).resolves({});
    bossOrgScanRes_withCycle.Items[6].additional_children![0] = '5';
    ddbMock
      .on(ScanCommand, {
        TableName: bossOrgName,
      })
      .resolves(bossOrgScanRes_withCycle)
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves(corScanRes);
    await handler(modifyEvent, mockContext);

    const toPutCors: CoraOrgRelModel[] = [];
    const toDeleteCors: { pk_ppn_id: string; parent_ppn_id: string }[] = [];
    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        if (request.PutRequest) {
          toPutCors.push(request.PutRequest.Item as CoraOrgRelModel);
        }
        if (request.DeleteRequest) {
          toDeleteCors.push(request.DeleteRequest.Key as { pk_ppn_id: string; parent_ppn_id: string });
        }
      });
    });

    expect(toPutCors.length).toBe(12);
    const toPutChildParentRecord = convertToParentChildrenRecord(toPutCors);
    expect(toPutChildParentRecord['7']).toBe('5,8,9');
    expect(toPutChildParentRecord['4']).toBe('5,8,9');
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '4' && toDel.parent_ppn_id === '7')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '6' && toDel.parent_ppn_id === '7')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '3' && toDel.parent_ppn_id === '4')).toBe(true);
    expect(toDeleteCors.some((toDel) => toDel.pk_ppn_id === '3' && toDel.parent_ppn_id === '7')).toBe(true);
  });

  it('Deactivate node 2', async () => {
    const pk2QueryRes = {
      Items: [
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '1',
          display_name: 'Node 2',
        },
        {
          ...defCorProps,
          pk_ppn_id: '2',
          parent_ppn_id: '2',
          display_name: 'Node 2',
        },
      ],
    };
    const modifyEvent: DynamoDBStreamEvent = {
      Records: [
        {
          eventID: '1',
          eventName: 'MODIFY',
          eventVersion: '1.0',
          eventSource: 'aws:dynamodb',
          awsRegion: 'eu-west-1',
          dynamodb: {
            ApproximateCreationDateTime: 1628655861,
            Keys: {
              pk_ppn_id: { S: '2' },
            },
            OldImage: {
              pk_ppn_id: { S: '2' },
              parent_ppn_id: { S: '1' },
              additional_children: { NULL: true },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: false },
            },
            NewImage: {
              pk_ppn_id: { S: '2' },
              parent_ppn_id: { S: '1' },
              additional_children: { NULL: true },
              ppn_importer_partner_number: { NULL: true },
              ppn_porsche_partner_no: { NULL: true },
              ppn_status: { S: 'OPERATIVE' },
              is_deactivated: { BOOL: true },
            },
            StreamViewType: 'NEW_IMAGE',
          },
          eventSourceARN: 'your_dynamodb_arn',
        },
      ],
    };
    ddbMock
      .on(QueryCommand, {
        TableName: coraOrgRelName,
        IndexName: undefined,
        KeyConditionExpression: 'pk_ppn_id = :value',
        ExpressionAttributeValues: {
          ':value': '2',
        },
      })
      .resolves(pk2QueryRes);

    ddbMock
      .on(ScanCommand, {
        TableName: coraOrgRelName,
      })
      .resolves(corScanRes);
    ddbMock.on(BatchWriteCommand).resolves({ UnprocessedItems: {} });
    await handler(modifyEvent, mockContext);

    const toPutCors: CoraOrgRelModel[] = [];
    const toDeleteCors: { pk_ppn_id: string; parent_ppn_id: string }[] = [];
    ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
      const requestItems = call.args[0].input.RequestItems;
      requestItems![coraOrgRelName].forEach((request) => {
        if (request.PutRequest) {
          toPutCors.push(request.PutRequest.Item as CoraOrgRelModel);
        }
        if (request.DeleteRequest) {
          toDeleteCors.push(request.DeleteRequest.Key as { pk_ppn_id: string; parent_ppn_id: string });
        }
      });
    });

    expect(toDeleteCors.length).toBe(0);
    expect(toPutCors.length).toBe(2);
    expect(
      toPutCors.some((toPut) => toPut.pk_ppn_id === '2' && toPut.parent_ppn_id === '2' && toPut.is_deactivated),
    ).toBe(true);
    expect(
      toPutCors.some((toPut) => toPut.pk_ppn_id === '2' && toPut.parent_ppn_id === '1' && toPut.is_deactivated),
    ).toBe(true);
  });

  describe('Ensure consistency when firstly adding and then removing an additional child', () => {
    let initialCoraOrgRels: CoraOrgRelModel[] = [];
    let intermediateCoraOrgRels: CoraOrgRelModel[] = [];

    it('should maintain the same cora-org-rel count before and after adding/removing an additional child', async () => {
      // Run the full calculation
      ddbMock.on(BatchWriteCommand).resolves({ UnprocessedItems: {} });
      ddbMock
        .on(ScanCommand, { TableName: coraOrgRelName })
        .resolves({ Items: [] })
        .on(ScanCommand, { TableName: bossOrgName })
        .resolves(bossOrgScanRes_withCycle);

      // Initial full calculation
      await handler({ Records: [] } as DynamoDBStreamEvent, mockContext);

      // Capture initial state
      initialCoraOrgRels = [];
      ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
        const requestItems = call.args[0].input.RequestItems;
        requestItems![coraOrgRelName].forEach((request) => {
          if (request.PutRequest) {
            initialCoraOrgRels.push(request.PutRequest.Item as CoraOrgRelModel);
          }
        });
      });
      const initialCount = initialCoraOrgRels.length;

      // Add an additional child Node 7 to node 8
      const addAdditionalChildEvent: DynamoDBStreamEvent = {
        Records: [
          {
            eventID: '1',
            eventName: 'MODIFY',
            eventVersion: '1.0',
            eventSource: 'aws:dynamodb',
            awsRegion: 'eu-west-1',
            dynamodb: {
              ApproximateCreationDateTime: 1628655861,
              Keys: { pk_ppn_id: { S: '8' } },
              OldImage: {
                pk_ppn_id: { S: '8' },
                parent_ppn_id: { S: '5' },
                additional_children: { NULL: true },
              },
              NewImage: {
                pk_ppn_id: { S: '8' },
                parent_ppn_id: { S: '5' },
                additional_children: { L: [{ S: '7' }] },
              },
              StreamViewType: 'NEW_IMAGE',
            },
            eventSourceARN: 'dynamodb_arn',
          },
        ],
      };

      let node8 = bossOrgScanRes_withCycle.Items.find((item) => item.pk_ppn_id === '8');
      if (node8) {
        node8.additional_children = ['7'];
      }

      ddbMock.reset();
      ddbMock
        .on(ScanCommand, { TableName: coraOrgRelName })
        .resolves({ Items: initialCoraOrgRels })
        .on(ScanCommand, { TableName: bossOrgName })
        .resolves(bossOrgScanRes_withCycle)
        .on(QueryCommand, {
          TableName: coraOrgRelName,
          IndexName: undefined,
          KeyConditionExpression: 'pk_ppn_id = :value',
          ExpressionAttributeValues: {
            ':value': '8',
          },
        })
        .resolves({ Items: pk8QueryRes.Items })
        .on(QueryCommand, {
          TableName: coraOrgRelName,
          IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
          KeyConditionExpression: 'parent_ppn_id = :value',
          ExpressionAttributeValues: {
            ':value': '7',
          },
        })
        .resolves(sk7QueryRes)
        .on(BatchWriteCommand)
        .resolves({ UnprocessedItems: {} });

      await handler(addAdditionalChildEvent, mockContext);

      // Capture the state after adding additional child
      intermediateCoraOrgRels = [...initialCoraOrgRels];
      ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
        const requestItems = call.args[0].input.RequestItems;
        requestItems![coraOrgRelName].forEach((request) => {
          if (request.PutRequest) {
            const putOrgRel = request.PutRequest.Item as CoraOrgRelModel;
            const found = intermediateCoraOrgRels.find(
              (orgRel) => orgRel.pk_ppn_id === putOrgRel.pk_ppn_id && orgRel.parent_ppn_id === putOrgRel.parent_ppn_id,
            );
            if (!found) {
              intermediateCoraOrgRels.push(request.PutRequest.Item as CoraOrgRelModel);
            }
          }
        });
      });

      // Remove the additional child Node 7 from Node 8
      const removeAdditionalChildEvent: DynamoDBStreamEvent = {
        Records: [
          {
            eventID: '1',
            eventName: 'MODIFY',
            eventVersion: '1.0',
            eventSource: 'aws:dynamodb',
            awsRegion: 'eu-west-1',
            dynamodb: {
              ApproximateCreationDateTime: 1628655861,
              Keys: { pk_ppn_id: { S: '8' } },
              OldImage: {
                pk_ppn_id: { S: '8' },
                parent_ppn_id: { S: '5' },
                additional_children: { L: [{ S: '7' }] },
              },
              NewImage: {
                pk_ppn_id: { S: '8' },
                parent_ppn_id: { S: '5' },
                additional_children: { NULL: true },
              },
              StreamViewType: 'NEW_IMAGE',
            },
            eventSourceARN: 'dynamodb_arn',
          },
        ],
      };

      node8 = bossOrgScanRes_withCycle.Items.find((item) => item.pk_ppn_id === '8');
      if (node8) {
        node8.additional_children = [];
      }
      ddbMock.reset();
      ddbMock
        .on(ScanCommand, { TableName: coraOrgRelName })
        .resolves({ Items: intermediateCoraOrgRels })
        .on(ScanCommand, { TableName: bossOrgName })
        .resolves(bossOrgScanRes_withCycle) // Full calculation data
        .on(QueryCommand, {
          TableName: coraOrgRelName,
          IndexName: undefined,
          KeyConditionExpression: 'pk_ppn_id = :value',
          ExpressionAttributeValues: {
            ':value': '8',
          },
        })
        .resolves({ Items: pk8QueryRes.Items })
        .on(QueryCommand, {
          TableName: coraOrgRelName,
          IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
          KeyConditionExpression: 'parent_ppn_id = :value',
          ExpressionAttributeValues: {
            ':value': '7',
          },
        })
        .resolves(sk7QueryRes)
        .on(BatchWriteCommand)
        .resolves({ UnprocessedItems: {} });

      await handler(removeAdditionalChildEvent, mockContext);

      // Capture the final state after removing the additional child
      const finalCoraOrgRels: CoraOrgRelModel[] = [...intermediateCoraOrgRels];
      ddbMock.commandCalls(BatchWriteCommand).forEach((call) => {
        const requestItems = call.args[0].input.RequestItems;
        requestItems![coraOrgRelName].forEach((request) => {
          if (request.PutRequest) {
            finalCoraOrgRels.push(request.PutRequest.Item as CoraOrgRelModel);
          } else if (request.DeleteRequest) {
            const orgKeyRecord: Record<string, string> = request.DeleteRequest.Key!;
            const index = finalCoraOrgRels.findIndex(
              (orgRel) =>
                orgRel.pk_ppn_id === orgKeyRecord.pk_ppn_id && orgRel.parent_ppn_id === orgKeyRecord.parent_ppn_id,
            );
            if (index !== -1) {
              finalCoraOrgRels.splice(index, 1);
            }
          }
        });
      });
      const finalCount = finalCoraOrgRels.length;

      // Compare Counts
      expect(finalCount).toBe(initialCount);

      // Ensure relationships are the same as the initial state
      const initialRecords = convertToParentChildrenRecord(initialCoraOrgRels);
      const finalRecords = convertToParentChildrenRecord(finalCoraOrgRels);
      expect(finalRecords).toEqual(initialRecords);
    });
  });
});

function convertToParentChildrenRecord(arr: CoraOrgRelModel[]): Record<string, string> {
  const tempRecord: Record<string, string[]> = {};

  arr.forEach((item) => {
    const { parent_ppn_id, pk_ppn_id } = item;
    if (parent_ppn_id in tempRecord) {
      tempRecord[parent_ppn_id].push(pk_ppn_id);
    } else {
      tempRecord[parent_ppn_id] = [pk_ppn_id];
    }
  });

  const sortedRecord: Record<string, string> = {};
  Object.keys(tempRecord).forEach((key) => {
    sortedRecord[key] = tempRecord[key].sort().join(',');
  });

  return sortedRecord;
}
