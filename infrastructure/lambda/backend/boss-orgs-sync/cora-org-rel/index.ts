import { Context, DynamoDBRecord, DynamoDBStreamEvent } from 'aws-lambda';
import { GeneralError } from '../../../utils/errors';
import { BasisCOR, BossOrgModel, CoraOrgRelModel, CORModelKey } from '../../../../lib/types/boss-org-types';
import {
  BatchWriteCommand,
  BatchWriteCommandOutput,
  QueryCommand,
  ScanCommand,
  ScanCommandInput,
  ScanCommandOutput,
} from '@aws-sdk/lib-dynamodb';
import { AttributeValue, DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import { Constants } from '../../../../lib/utils/constants';
import { KafkaAdapter } from '../../../utils/kafka';
import { secretCache } from '../../../utils/secret-cache';
import { getEnvVarWithAssert, splitIntoBatches } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

const BOSS_ORG_TABLE_NAME = getEnvVarWithAssert('TABLE_NAME_BOSS_ORG');
const CORA_ORG_REL_TABLE_NAME = getEnvVarWithAssert('TABLE_NAME_CORA_ORG_REL');
const BOSS_ORG_GROUP_ID = getEnvVarWithAssert('GROUP_ID');
const KAFKA_SECRET_ARN = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_TOPIC = getEnvVarWithAssert('KAFKA_TOPIC');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];

const dbClient = new DynamoDBClient({});
const logger = new KasLambdaLogger('cora-org-rel', LogLevel.TRACE);

secretCache.initCache(KAFKA_SECRET_ARN);
const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: KAFKA_SECRET_ARN,
  logger,
});

/**
 *
 * @param event
 */
// eslint-disable-next-line  @typescript-eslint/no-unused-vars
export const handler = async (event: DynamoDBStreamEvent, context: Context): Promise<void> => {
  logger.setRequestContext(context);
  try {
    const notEmpty = await isCORTableNotEmpty();
    const result = await processStreamEvent(event, notEmpty);
    if (result.toPutCorModels.length > 0 || result.toDeleteCORModelKeys.length > 0) {
      await writeCoraOrgRelsToDynamoDB(result.toPutCorModels, result.toDeleteCORModelKeys);
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'An error occurred:', { data: error });
    throw new GeneralError({
      message: 'Failed to process DynamoDB Stream event',
      causedBy: error,
    });
  }
};

/**
 * Processes the DynamoDB stream event based on the scan result of the 'CORA_ORG_REL_TABLE_NAME'.
 *
 * 1. If the table is empty:
 *    - This indicates that the initial computation hasn't started yet.
 *    - If the offsetLag is not zero, there are still events awaiting processing. Skip and do nothing.
 *    - If the offsetLag is zero, it means the last event has been processed. The entire bossOrg table will be scanned to construct a flattened child-parent hierarchy.
 *
 *  2. If the table is not empty:
 *     - It indicates that the initial computation has been completed.
 *     - Individual CRUD operations on org will be addressed.
 *
 * @param event
 * @param notEmpty
 */
// eslint-disable-next-line  @typescript-eslint/explicit-function-return-type
async function processStreamEvent(
  event: DynamoDBStreamEvent,
  notEmpty: boolean,
): Promise<{ toPutCorModels: CoraOrgRelModel[]; toDeleteCORModelKeys: CORModelKey[] }> {
  const toPutCorModels: CoraOrgRelModel[] = [];
  const toDeleteCORModelKeys: CORModelKey[] = [];

  if (!notEmpty) {
    logger.log(LogLevel.INFO, `Found no data in ${CORA_ORG_REL_TABLE_NAME}`);
    const isOffsetLagZero = await kafkaAdapter.isConsumerOffsetLagZero(BOSS_ORG_GROUP_ID, KAFKA_TOPIC);
    if (isOffsetLagZero) {
      logger.log(LogLevel.INFO, `Begin the first complete calculation`);
      const allBossOrgs = await scanBossOrgTableWithPagination();
      const fullChildParents = buildCORChildParents(allBossOrgs);
      const coraOrgRels = buildCoraOrgRels(fullChildParents);
      toPutCorModels.push(...coraOrgRels);
    } else {
      logger.log(LogLevel.DEBUG, 'There are still unprocessed event', { data: event });
      return { toPutCorModels, toDeleteCORModelKeys };
    }
  } else {
    logger.log(LogLevel.DEBUG, 'Process the stream event', { data: event });
    //process the stream event
    for (const record of event.Records) {
      switch (record.eventName) {
        //A new org is added
        case 'INSERT':
          {
            const toPut = await handleInsert(record);
            const filteredToPut = removeDuplicateCORs(toPut);
            toPutCorModels.push(...filteredToPut);
          }
          break;
        case 'MODIFY':
          {
            const { toPut, toDelete } = await handleModify(record);
            const filteredToPut = removeDuplicateCORs(toPut);
            const filteredToDelete = removeDuplicateBasisCORs(toDelete);
            toPutCorModels.push(...filteredToPut);
            toDeleteCORModelKeys.push(...filteredToDelete);
          }
          break;
        case 'REMOVE':
        default:
          logger.log(LogLevel.ERROR, `Unexpected EventType: ${record.eventName}`);
          return { toPutCorModels, toDeleteCORModelKeys };
      }
    }
  }
  return { toPutCorModels, toDeleteCORModelKeys };
}

function removeDuplicateCORs(cors: CoraOrgRelModel[]): CoraOrgRelModel[] {
  const uniqueCors: Record<string, CoraOrgRelModel> = {};

  cors.forEach((cor) => {
    const compositeKey = `${cor.pk_ppn_id}|${cor.parent_ppn_id}`;

    if (!(compositeKey in uniqueCors)) {
      uniqueCors[compositeKey] = cor;
    }
  });

  return Object.values(uniqueCors);
}

function removeDuplicateBasisCORs(cors: CORModelKey[]): CORModelKey[] {
  const corsModelKeyRecord: Record<string, CORModelKey[]> = {};
  cors.forEach((cor) => {
    const ppnId = cor.ppn_id;
    if (!(ppnId in corsModelKeyRecord)) {
      corsModelKeyRecord[ppnId] = [cor];
    } else {
      const orgCors = corsModelKeyRecord[ppnId];
      const orgParentIds = orgCors.map((orgCor) => orgCor.parent_ppn_id);
      if (!orgParentIds.includes(cor.parent_ppn_id)) {
        corsModelKeyRecord[ppnId].push(cor);
      }
    }
  });

  const filteredCORModelKeys: CORModelKey[] = [];
  Object.keys(corsModelKeyRecord).forEach((key) => {
    filteredCORModelKeys.push(...corsModelKeyRecord[key]);
  });

  return filteredCORModelKeys;
}

async function handleInsert(record: DynamoDBRecord): Promise<CoraOrgRelModel[]> {
  logger.log(LogLevel.INFO, 'Handle an insert action', { data: record });
  const toPut: CoraOrgRelModel[] = [];
  const newData = record.dynamodb?.NewImage as Record<string, AttributeValue> | undefined;
  if (!newData) {
    return toPut;
  }
  const newBOModel = unmarshall(newData) as BossOrgModel;
  const newPpnId = newBOModel.pk_ppn_id;
  const newPpnParentId = newBOModel.parent_ppn_id;

  let newImporterNr = '';
  let newDealerNr = '';
  //the org is importer or dealer
  if (newBOModel.ppn_importer_partner_number) {
    newImporterNr = newBOModel.ppn_importer_partner_number;
    newDealerNr = newBOModel.ppn_porsche_partner_no ?? newBOModel.pk_ppn_id;
  }
  const newAddChildren = newBOModel.additional_children;
  const newOrgCORModels: CoraOrgRelModel[] = [];
  if (newPpnParentId && newPpnParentId.length > 0) {
    //save self
    newOrgCORModels.push({
      pk_ppn_id: newPpnId,
      parent_ppn_id: newPpnId,
      importer_number: newImporterNr,
      dealer_number: newDealerNr,
      is_deactivated: newBOModel.is_deactivated,
      display_name: newBOModel.display_name,
      ppn_status: newBOModel.ppn_status,
      is_relevant_for_order_create: String(newBOModel.is_relevant_for_order_create) === 'true',
    });
    const ancesors = await queryOnCORTableIndex(
      undefined,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk,
      newPpnParentId,
    );
    if (ancesors.length === 0) {
      throw new GeneralError({
        message: `Should not happen! Could not find any parents of the parent by parentPpnId:${newPpnParentId}`,
      });
    } else {
      //inherit all the parents of the parent as its own parents including the direct parent
      const inheritedCors: CoraOrgRelModel[] = ancesors.map((parentCOR) => ({
        pk_ppn_id: newPpnId,
        parent_ppn_id: parentCOR.parent_ppn_id,
        dealer_number: newDealerNr,
        importer_number: newImporterNr,
        is_deactivated: newBOModel.is_deactivated,
        display_name: newBOModel.display_name,
        ppn_status: newBOModel.ppn_status,
        is_relevant_for_order_create: String(newBOModel.is_relevant_for_order_create) === 'true', //The property should be a string, but during local debugging, it was somehow interpreted as a boolean.
      }));
      newOrgCORModels.push(...inheritedCors);
    }
    toPut.push(...newOrgCORModels);
  }

  if (newAddChildren && newAddChildren.length > 0) {
    for (const childPpnId of newAddChildren) {
      const childrenBasisCORs = await getAllChildrenBasisCORs(childPpnId);
      for (const childBasisCOR of childrenBasisCORs) {
        newOrgCORModels.forEach((parent) => {
          toPut.push({
            pk_ppn_id: childBasisCOR.pk_ppn_id,
            parent_ppn_id: parent.parent_ppn_id,
            dealer_number: childBasisCOR.dealer_number,
            importer_number: childBasisCOR.importer_number,
            is_deactivated: childBasisCOR.is_deactivated,
            display_name: childBasisCOR.display_name,
            ppn_status: childBasisCOR.ppn_status,
            is_relevant_for_order_create: childBasisCOR.is_relevant_for_order_create,
          });
        });
      }
    }
  }
  logger.log(LogLevel.INFO, 'ToPut list by modification action', {
    data: toPut.map((item) => JSON.stringify(item)).join(' ; '),
  });
  return toPut;
}

async function handleModify(record: DynamoDBRecord): Promise<{ toPut: CoraOrgRelModel[]; toDelete: CORModelKey[] }> {
  logger.log(LogLevel.INFO, 'Handle a modify action', { data: record });
  const toPut: CoraOrgRelModel[] = [];
  const toDelete: CORModelKey[] = [];

  const newData = record.dynamodb?.NewImage as Record<string, AttributeValue> | undefined;
  if (!newData) {
    return { toPut, toDelete };
  }
  const oldData = record.dynamodb?.OldImage as Record<string, AttributeValue> | undefined;
  if (!oldData) {
    return { toPut, toDelete };
  }

  const newBOModel = unmarshall(newData) as BossOrgModel;
  const newPpnId = newBOModel.pk_ppn_id;
  const newPpnParentId = newBOModel.parent_ppn_id;
  let newImporterNr = '';
  let newDealerNr = '';
  //the org is importer or dealer
  if (newBOModel.ppn_importer_partner_number) {
    newImporterNr = newBOModel.ppn_importer_partner_number;
    newDealerNr = newBOModel.ppn_porsche_partner_no ?? newBOModel.pk_ppn_id;
  }
  const newAddChildren = newBOModel.additional_children;

  const oldBOTModel = unmarshall(oldData) as BossOrgModel;
  const oldParentPpnId = oldBOTModel.parent_ppn_id;
  const oldAddChildren = oldBOTModel.additional_children;
  const newPPpnParentIds = new Set<string>();
  //Org is moved to a new position
  if (oldParentPpnId && oldParentPpnId !== newPpnParentId) {
    logger.log(LogLevel.INFO, 'Some organization is relocated to a new position', { data: newPpnId });
    //Find out all the parents of the old parent
    const oldAncesors = await queryOnCORTableIndex(
      undefined,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk,
      oldParentPpnId,
    );
    //Get all the ppnIds of the old parent's parents
    const oldParentsPpnIds: Set<string> = new Set<string>();
    oldAncesors.forEach((oldParent) => oldParentsPpnIds.add(oldParent.pk_ppn_id));
    //All children including additonal children
    const oldChildrenBaisCORs = await getAllChildrenBasisCORs(oldBOTModel.pk_ppn_id);

    //Get the full CoraOrgRels, which stands for the target state
    const allBossOrgs = await scanBossOrgTableWithPagination();
    const fullChildParents = buildCORChildParents(allBossOrgs);

    oldChildrenBaisCORs.forEach((oldChildBasisCOR) =>
      oldParentsPpnIds.forEach((oldPPpnId) => {
        if (!(oldChildBasisCOR.pk_ppn_id in fullChildParents)) {
          toDelete.push({
            ppn_id: oldChildBasisCOR.pk_ppn_id,
            parent_ppn_id: oldPPpnId,
          });
        } else {
          const fullParents = fullChildParents[oldChildBasisCOR.pk_ppn_id];
          if (!fullParents.some((cor) => cor.parent_ppn_id === oldPPpnId)) {
            toDelete.push({
              ppn_id: oldChildBasisCOR.pk_ppn_id,
              parent_ppn_id: oldPPpnId,
            });
          }
        }
      }),
    );

    if (newPpnParentId) {
      //Find all parents of the new parent
      const ancesors = await queryOnCORTableIndex(
        undefined,
        Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk,
        newPpnParentId,
      );

      ancesors.forEach((ancesor) => newPPpnParentIds.add(ancesor.parent_ppn_id));
      if (newPPpnParentIds.size > 0) {
        for (const newPPpnParentId of newPPpnParentIds) {
          oldChildrenBaisCORs.forEach((childBasisCOR) => {
            //ImporterNr and dealerId could be changed?
            if (childBasisCOR.pk_ppn_id === newPpnId) {
              toPut.push({
                pk_ppn_id: newPpnId,
                parent_ppn_id: newPPpnParentId,
                dealer_number: newDealerNr,
                importer_number: newImporterNr,
                is_deactivated: newBOModel.is_deactivated,
                display_name: newBOModel.display_name,
                ppn_status: newBOModel.ppn_status,
                is_relevant_for_order_create: String(newBOModel.is_relevant_for_order_create) === 'true',
              });
            } else {
              toPut.push({
                ...childBasisCOR,
                parent_ppn_id: newPPpnParentId,
              });
            }
          });
        }
      }
    }
    // Only additional_children are changed
  } else if (!arraysHaveSameElements(oldAddChildren, newAddChildren)) {
    logger.log(LogLevel.INFO, 'Additional_children are changed by some organization', { data: newPpnId });
    const toDeleteAddChildren: string[] = [];
    const toAddAddChildren: string[] = [];
    if (oldAddChildren && oldAddChildren.length > 0) {
      if (!newAddChildren) {
        toDeleteAddChildren.push(...oldAddChildren);
      } else {
        oldAddChildren.forEach((oldAddChild) => {
          if (!newAddChildren.includes(oldAddChild)) {
            toDeleteAddChildren.push(oldAddChild);
          }
        });

        newAddChildren.forEach((newAddChild) => {
          if (!oldAddChildren.includes(newAddChild)) {
            toAddAddChildren.push(newAddChild);
          }
        });
      }
    } else {
      if (newAddChildren && newAddChildren.length > 0) {
        toAddAddChildren.push(...newAddChildren);
      }
    }

    //Find all parents
    const ancesors = await queryOnCORTableIndex(undefined, Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk, newPpnId);
    //Get the full CoraOrgRels, which stands for the target state
    const allBossOrgs = await scanBossOrgTableWithPagination();
    const fullChildParents = buildCORChildParents(allBossOrgs);

    if (toDeleteAddChildren.length > 0) {
      for (const toDeleteAddChild of toDeleteAddChildren) {
        const allDelChildrenBasisCORs = await getAllChildrenBasisCORs(toDeleteAddChild);
        allDelChildrenBasisCORs.forEach((childBasiCor) => {
          ancesors.forEach((parent) => {
            if (!(childBasiCor.pk_ppn_id in fullChildParents)) {
              toDelete.push({
                ppn_id: childBasiCor.pk_ppn_id,
                parent_ppn_id: parent.parent_ppn_id,
              });
            } else {
              const fullParents = fullChildParents[childBasiCor.pk_ppn_id];
              if (!fullParents.some((cor) => cor.parent_ppn_id === parent.parent_ppn_id)) {
                toDelete.push({
                  ppn_id: childBasiCor.pk_ppn_id,
                  parent_ppn_id: parent.parent_ppn_id,
                });
              }
            }
          });
        });
      }
    }

    if (toAddAddChildren.length > 0) {
      for (const toAddAddChild of toAddAddChildren) {
        const allAddChildrenBasisCORs = await getAllChildrenBasisCORs(toAddAddChild);

        allAddChildrenBasisCORs.forEach((childBasisCor) => {
          ancesors.forEach((parent) => {
            if (childBasisCor.pk_ppn_id === newPpnId) {
              toPut.push({
                pk_ppn_id: childBasisCor.pk_ppn_id,
                parent_ppn_id: parent.parent_ppn_id,
                dealer_number: newDealerNr,
                importer_number: newImporterNr,
                is_deactivated: newBOModel.is_deactivated,
                display_name: newBOModel.display_name,
                ppn_status: newBOModel.ppn_status,
                is_relevant_for_order_create: String(newBOModel.is_relevant_for_order_create) === 'true',
              });
            } else {
              toPut.push({
                ...childBasisCor,
                parent_ppn_id: parent.parent_ppn_id,
              });
            }
          });
        });
      }
    }
    //There could be changes to other properties
  } else {
    logger.log(LogLevel.INFO, 'Some other properties other than additional_children and parent_ppn_id are changed', {
      data: newPpnId,
    });
    const existingCORModels = await queryOnCORTableIndex(
      undefined,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pk,
      newBOModel.pk_ppn_id,
    );
    const updatedCORModels = existingCORModels.map((cor) => ({
      ...cor,
      is_deactivated: newBOModel.is_deactivated,
      importer_number: newImporterNr,
      dealer_number: newDealerNr,
      display_name: newBOModel.display_name,
      ppn_status: newBOModel.ppn_status,
      is_relevant_for_order_create: String(newBOModel.is_relevant_for_order_create) === 'true',
    }));
    toPut.push(...updatedCORModels);
  }

  logger.log(LogLevel.INFO, 'ToPut list by modification action', {
    data: toPut.map((item) => JSON.stringify(item)).join(' ; '),
  });
  logger.log(LogLevel.INFO, 'ToDelete list has a length', { data: toDelete.length });
  logger.log(LogLevel.INFO, 'ToDelete list by modification action', {
    data: toDelete.map((item) => JSON.stringify(item)).join(' ; '),
  });
  return { toPut, toDelete };
}

async function getAllChildrenBasisCORs(parentId: string): Promise<BasisCOR[]> {
  //find all direct children and children's children
  const childrenCORModels = await queryOnCORTableIndex(
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk,
    parentId,
  );
  //keep only the unduplicated basis info
  const childrenBasisCORs = childrenCORModels.map((childCORModel) => ({
    pk_ppn_id: childCORModel.pk_ppn_id,
    dealer_number: childCORModel.dealer_number,
    importer_number: childCORModel.importer_number,
    is_deactivated: childCORModel.is_deactivated,
    display_name: childCORModel.display_name,
    ppn_status: childCORModel.ppn_status,
    is_relevant_for_order_create: childCORModel.is_relevant_for_order_create,
  })) as BasisCOR[];

  return removeDuplicateds(childrenBasisCORs);
}

function removeDuplicateds<T>(arr: T[]): T[] {
  const uniqueArray: T[] = [];

  for (const obj of arr) {
    const serializedObj = JSON.stringify(obj);
    if (!uniqueArray.some((item) => JSON.stringify(item) === serializedObj)) {
      uniqueArray.push(obj);
    }
  }
  return uniqueArray;
}

async function queryOnCORTableIndex(
  indexName: string | undefined,
  gsiKey: string,
  value: string,
): Promise<CoraOrgRelModel[]> {
  const cmd = new QueryCommand({
    TableName: CORA_ORG_REL_TABLE_NAME,
    IndexName: indexName,
    KeyConditionExpression: `${gsiKey} = :value`,
    ExpressionAttributeValues: { ':value': value },
  });

  try {
    const result = await dbClient.send(cmd);
    if (result.Items) {
      return result.Items as CoraOrgRelModel[];
    } else {
      logger.log(
        LogLevel.INFO,
        `No match by querying table ${CORA_ORG_REL_TABLE_NAME} with parameters: indexName=${indexName}; gsiKey=${gsiKey}; value=${value}`,
      );
      return [];
    }
  } catch (error) {
    logger.log(
      LogLevel.ERROR,
      `Failed to query table ${CORA_ORG_REL_TABLE_NAME} with parameters: indexName=${indexName}; gsiKey=${gsiKey}; value=${value}`,
    );
    throw error;
  }
}

/**
 * Check if the table is empty
 */
async function isCORTableNotEmpty(): Promise<boolean> {
  const params = {
    TableName: CORA_ORG_REL_TABLE_NAME,
    Limit: 1,
  };

  try {
    const result = await dbClient.send(new ScanCommand(params));
    if (result.Items && result.Items.length > 0) {
      logger.log(LogLevel.DEBUG, `Found items in Table ${CORA_ORG_REL_TABLE_NAME}`);
      return true;
    } else {
      logger.log(LogLevel.DEBUG, `Found no item in Table ${CORA_ORG_REL_TABLE_NAME}`);
      return false;
    }
  } catch (error) {
    throw new GeneralError({
      message: `Failed to scan table ${CORA_ORG_REL_TABLE_NAME}`,
      causedBy: error,
    });
  }
}

async function scanBossOrgTableWithPagination(): Promise<BossOrgModel[]> {
  const result: BossOrgModel[] = [];
  let lastEvaluatedKey = undefined;

  do {
    const params: ScanCommandInput = {
      TableName: BOSS_ORG_TABLE_NAME,
      Limit: 2000,
      ExclusiveStartKey: lastEvaluatedKey,
    };

    const scanCommand = new ScanCommand(params);

    try {
      const scanResponse: ScanCommandOutput = await dbClient.send(scanCommand);
      const bossOrgs = scanResponse.Items! as BossOrgModel[];
      result.push(...bossOrgs);
      lastEvaluatedKey = scanResponse.LastEvaluatedKey;
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Failed to scan table ');
      throw error;
    }
  } while (lastEvaluatedKey);
  logger.log(LogLevel.DEBUG, `Retrieved ${result.length} BossOrgModel records by scanning`);
  return result;
}

function existsSameParentPpnId(
  orgChildPars: Record<string, CoraOrgRelModel[]>,
  pkPpnId: string,
  parentPpnId: string,
): boolean {
  let hasParentPpnId = false;
  for (const toCheckCor of orgChildPars[pkPpnId]) {
    if (parentPpnId === toCheckCor.parent_ppn_id) {
      hasParentPpnId = true;
      break;
    }
  }
  return hasParentPpnId;
}

/**
 * Flatten the complete boss orgs to child-parent mappings, including additional children
 * @param allBossOrgs
 */
function buildCORChildParents(allBossOrgs: BossOrgModel[]): Record<string, CoraOrgRelModel[]> {
  const allPpnIdBossOrgsRecord: Record<string, BossOrgModel> = {};
  const allParentIdBossOrgsRecord: Record<string, BossOrgModel[]> = {};
  const ppnIdAddChildrenRecord: Record<string, string[]> = {};
  const allAddParentRecord: Record<string, Set<string>> = {};
  allBossOrgs.forEach((bossOrg) => {
    allPpnIdBossOrgsRecord[bossOrg.pk_ppn_id] = bossOrg;
    let parentId = bossOrg.parent_ppn_id ?? '';
    if (bossOrg.additional_children && bossOrg.additional_children.length > 0) {
      ppnIdAddChildrenRecord[bossOrg.pk_ppn_id] = bossOrg.additional_children;
      if (!parentId || parentId.trim() === '') {
        parentId = 'root';
      }
    }
    if (parentId in allParentIdBossOrgsRecord) {
      allParentIdBossOrgsRecord[parentId].push(bossOrg);
    } else {
      allParentIdBossOrgsRecord[parentId] = [bossOrg];
    }
  });

  Object.keys(ppnIdAddChildrenRecord).forEach((ppnId) => {
    const addChildren = ppnIdAddChildrenRecord[ppnId];
    addChildren.forEach((addChildId) => {
      if (!(addChildId in allAddParentRecord)) {
        allAddParentRecord[addChildId] = new Set<string>();
      }
      allAddParentRecord[addChildId].add(ppnId);
    });
  });

  const ppnIdCORsRecord: Record<string, CoraOrgRelModel[]> = {};
  allBossOrgs.forEach((bossOrg) => {
    const pkPpnId = bossOrg.pk_ppn_id;
    let dealerNr = '';
    let importerNr = '';
    //Org is importer or dealer
    if (bossOrg.ppn_importer_partner_number) {
      dealerNr = bossOrg.ppn_porsche_partner_no ?? pkPpnId;
      importerNr = bossOrg.ppn_importer_partner_number;
    }
    const additionalChildren = bossOrg.additional_children;

    const parentPpnIds: Set<string> = new Set<string>();
    //Recursively find all the direct and indirect parents
    const allParentBOs = findAllParentBOs(allPpnIdBossOrgsRecord, allAddParentRecord, pkPpnId);
    allParentBOs.forEach((parentBo) => {
      parentPpnIds.add(parentBo.pk_ppn_id);
    });
    // eslint-disable-next-line  @typescript-eslint/no-unnecessary-condition
    if (!ppnIdCORsRecord[pkPpnId]) {
      ppnIdCORsRecord[pkPpnId] = [];
      //save self
      ppnIdCORsRecord[pkPpnId].push({
        pk_ppn_id: pkPpnId,
        parent_ppn_id: pkPpnId,
        importer_number: importerNr,
        dealer_number: dealerNr,
        is_deactivated: bossOrg.is_deactivated,
        display_name: bossOrg.display_name,
        ppn_status: bossOrg.ppn_status,
        is_relevant_for_order_create: String(bossOrg.is_relevant_for_order_create) === 'true',
      });
    }
    parentPpnIds.forEach((parentPpnId) => {
      const existsParentPpnId = existsSameParentPpnId(ppnIdCORsRecord, pkPpnId, parentPpnId);
      if (!existsParentPpnId) {
        ppnIdCORsRecord[pkPpnId].push({
          pk_ppn_id: pkPpnId,
          parent_ppn_id: parentPpnId,
          importer_number: importerNr,
          dealer_number: dealerNr,
          is_deactivated: bossOrg.is_deactivated,
          display_name: bossOrg.display_name,
          ppn_status: bossOrg.ppn_status,
          is_relevant_for_order_create: String(bossOrg.is_relevant_for_order_create) === 'true',
        });
      }
    });

    if (additionalChildren && additionalChildren.length > 0) {
      //find all the direct and addtional children and children's direct and additional children
      const childrenBOTModels: BossOrgModel[] = [];
      additionalChildren.forEach((addtionalChildId) => {
        const addChildChildren = findAllChildrenBOs(
          allPpnIdBossOrgsRecord,
          allParentIdBossOrgsRecord,
          addtionalChildId,
        );
        logger.log(
          LogLevel.DEBUG,
          `Found ${addChildChildren.length} children for the additionalChildId ${addtionalChildId}`,
          { data: bossOrg },
        );
        childrenBOTModels.push(...addChildChildren);
      });

      childrenBOTModels.forEach((childOrg) => {
        const childId = childOrg.pk_ppn_id;
        let childDealerNr = '';
        let childImporterNr = '';
        if (childOrg.ppn_importer_partner_number) {
          childDealerNr = childOrg.ppn_porsche_partner_no ?? childOrg.pk_ppn_id;
          childImporterNr = childOrg.ppn_importer_partner_number;
        }
        // eslint-disable-next-line  @typescript-eslint/no-unnecessary-condition
        if (!ppnIdCORsRecord[childId]) {
          ppnIdCORsRecord[childId] = [];
          ppnIdCORsRecord[childId].push({
            pk_ppn_id: childId,
            parent_ppn_id: childId,
            importer_number: childImporterNr,
            dealer_number: childDealerNr,
            is_deactivated: childOrg.is_deactivated,
            display_name: childOrg.display_name,
            ppn_status: childOrg.ppn_status,
            is_relevant_for_order_create: String(childOrg.is_relevant_for_order_create) === 'true',
          });
        }
        //The org is the parent for all the additional children and children's children
        parentPpnIds.add(pkPpnId);
        parentPpnIds.forEach((parentPpnId) => {
          const hasParentPpnId = existsSameParentPpnId(ppnIdCORsRecord, childId, parentPpnId);
          if (!hasParentPpnId) {
            ppnIdCORsRecord[childId].push({
              pk_ppn_id: childId,
              parent_ppn_id: parentPpnId,
              importer_number: childImporterNr,
              dealer_number: childDealerNr,
              is_deactivated: childOrg.is_deactivated,
              display_name: childOrg.display_name,
              ppn_status: childOrg.ppn_status,
              is_relevant_for_order_create: String(childOrg.is_relevant_for_order_create) === 'true',
            });
          }
        });
      });
    }
  });
  return ppnIdCORsRecord;
}

function findAllParentBOs(
  allBossOrgsRecord: Record<string, BossOrgModel>,
  allAddParentsRecord: Record<string, Set<string>>,
  ppnId: string,
): BossOrgModel[] {
  let allParents: BossOrgModel[] = [];
  const toExplorePpnIds: string[] = [];
  toExplorePpnIds.push(ppnId);

  let currentAllParentsSize = 0;

  while (toExplorePpnIds.length > 0) {
    const currentPpnId = toExplorePpnIds[0];
    const bo = allBossOrgsRecord[currentPpnId];
    //Save only unduplicate parent
    allParents = saveUnduplicateBo(allParents, bo);

    const parentPpnId = bo.parent_ppn_id;
    if (parentPpnId && parentPpnId.trim() !== '') {
      if (!toExplorePpnIds.includes(parentPpnId)) {
        if (currentAllParentsSize !== allParents.length) {
          currentAllParentsSize = allParents.length;
          //Direct parent has priority
          toExplorePpnIds[0] = parentPpnId;
        } else {
          //The path has been traversed
          toExplorePpnIds.shift();
        }
      } else {
        toExplorePpnIds.shift();
      }
    } else {
      toExplorePpnIds.shift();
    }

    if (currentPpnId in allAddParentsRecord) {
      const addParentIds = allAddParentsRecord[currentPpnId];
      addParentIds.forEach((addParentId) => {
        if (!toExplorePpnIds.includes(addParentId)) {
          toExplorePpnIds.push(addParentId);
        }
      });
    }
  }
  return allParents;
}

function saveUnduplicateBo(boArr: BossOrgModel[], toAdd: BossOrgModel): BossOrgModel[] {
  if (boArr.length > 0) {
    const duplicateBos: BossOrgModel[] = boArr.filter(
      (bo) => bo.pk_ppn_id === toAdd.pk_ppn_id && bo.parent_ppn_id === toAdd.parent_ppn_id,
    );
    if (duplicateBos.length === 0) {
      boArr.push(toAdd);
    }
  } else {
    boArr.push(toAdd);
  }
  return boArr;
}

function findAllChildrenBOs(
  allPpnIdBossOrgsRecord: Record<string, BossOrgModel>,
  allParentIdBossOrgsRecord: Record<string, BossOrgModel[]>,
  ppnParentId: string,
): BossOrgModel[] {
  const allChildren: BossOrgModel[] = [];
  const toExplorePpnIds: string[] = [];
  const traversedIds: string[] = [];
  toExplorePpnIds.push(ppnParentId);

  saveUnduplicateBo(allChildren, allPpnIdBossOrgsRecord[ppnParentId]);
  while (toExplorePpnIds.length > 0) {
    const currentParentPpnId = toExplorePpnIds[0];
    if (currentParentPpnId in allParentIdBossOrgsRecord) {
      const childBos = allParentIdBossOrgsRecord[currentParentPpnId];
      childBos.forEach((childBo) => {
        if (!toExplorePpnIds.includes(childBo.pk_ppn_id) && !traversedIds.includes(childBo.pk_ppn_id)) {
          toExplorePpnIds.push(childBo.pk_ppn_id);
          saveUnduplicateBo(allChildren, childBo);

          if ((childBo.additional_children?.length ?? -1) > 0) {
            childBo.additional_children?.forEach((addChildId) => {
              if (!toExplorePpnIds.includes(addChildId) && !traversedIds.includes(addChildId)) {
                toExplorePpnIds.push(addChildId);
                const addChild = allPpnIdBossOrgsRecord[addChildId];
                saveUnduplicateBo(allChildren, addChild);
              }
            });
          }
        }
      });
      traversedIds.push(currentParentPpnId);
      toExplorePpnIds.shift();
    } else {
      toExplorePpnIds.shift();
    }
  }
  return allChildren;
}

/**
 * Collect all the child-parent mappings
 * @param orgChildPars
 */
function buildCoraOrgRels(orgChildPars: Record<string, CoraOrgRelModel[]>): CoraOrgRelModel[] {
  const toPutCoraOrgRels: CoraOrgRelModel[] = [];

  Object.keys(orgChildPars).forEach((ppnId) => {
    const coraOrgRelModels = orgChildPars[ppnId];
    toPutCoraOrgRels.push(...coraOrgRelModels);
  });
  return toPutCoraOrgRels;
}

async function writeCoraOrgRelsToDynamoDB(
  toPutCoraOrgRels: CoraOrgRelModel[],
  toDeleteCoraOrgRels: CORModelKey[],
): Promise<void> {
  logger.log(
    LogLevel.DEBUG,
    `The number of CoraOrgRels to insert is ${toPutCoraOrgRels.length}, and the number to delete is ${toDeleteCoraOrgRels.length}`,
  );
  if (toDeleteCoraOrgRels.length > 0) {
    logger.log(LogLevel.INFO, 'Deleting CoraOrgRels from DynamoDB');
    await batchWriteToDynamoDBWithRetry(toDeleteCoraOrgRels, 'DeleteRequest', (item: CORModelKey) => ({
      pk_ppn_id: item.ppn_id,
      parent_ppn_id: item.parent_ppn_id,
    }));
    logger.log(LogLevel.INFO, 'Completed deleting CoraOrgRels from DynamoDB');
  }

  if (toPutCoraOrgRels.length > 0) {
    logger.log(LogLevel.INFO, 'Inserting CoraOrgRels into DynamoDB');
    await batchWriteToDynamoDBWithRetry(toPutCoraOrgRels, 'PutRequest', () => ({}));
    logger.log(LogLevel.INFO, 'Completed inserting CoraOrgRels into DynamoDB');
  }
}

async function batchWriteToDynamoDBWithRetry<T>(
  items: T[],
  operationType: 'PutRequest' | 'DeleteRequest',
  keyMapper: (item: T) => Record<string, string>,
): Promise<void> {
  const MAX_RETRIES = 5;
  const BASE_DELAY_MS = 200;
  let batchCount = 0;

  const itemBatches = splitIntoBatches(items);

  for (const batch of itemBatches) {
    const batchParams = {
      RequestItems: {
        [CORA_ORG_REL_TABLE_NAME]: batch.map((item) => ({
          [operationType]: operationType === 'PutRequest' ? { Item: item } : { Key: keyMapper(item) },
        })),
      },
    };
    logger.log(LogLevel.DEBUG, `Executing the ${++batchCount} batch ${operationType} with ${batch.length} items`);
    let retries = 0;
    const response = await dbClient.send(new BatchWriteCommand(batchParams));

    let unprocessedItems = response.UnprocessedItems;
    if (response.UnprocessedItems && Object.keys(response.UnprocessedItems).length > 0) {
      while (retries <= MAX_RETRIES && unprocessedItems && Object.keys(unprocessedItems).length > 0) {
        let unprocessItemsNum = 0;
        if (CORA_ORG_REL_TABLE_NAME in unprocessedItems) {
          unprocessItemsNum = unprocessedItems[CORA_ORG_REL_TABLE_NAME].length;
          logger.log(
            LogLevel.INFO,
            `By the ${batchCount} batch ${operationType} there exist ${unprocessItemsNum} unprocessed items`,
          );
        }
        try {
          const retryResponse: BatchWriteCommandOutput = await dbClient.send(
            new BatchWriteCommand({ RequestItems: unprocessedItems }),
          );

          if (retryResponse.UnprocessedItems && Object.keys(retryResponse.UnprocessedItems).length > 0) {
            unprocessedItems = retryResponse.UnprocessedItems;
            retries++;
            const delay = BASE_DELAY_MS * 2 ** retries;
            logger.log(
              LogLevel.WARN,
              `Retrying the ${batchCount} batch ${operationType}, attempt ${retries}. Delaying for ${delay} ms.`,
            );
            await new Promise((resolve) => setTimeout(resolve, delay));
          } else {
            logger.log(LogLevel.DEBUG, `Retry has worked for the ${batchCount} batch ${operationType}`);
            break;
          }
        } catch (error) {
          logger.log(LogLevel.ERROR, `Failed to execute the ${batchCount} batch ${operationType}`, { data: error });
          throw new GeneralError({
            message: `Failed to execute batch ${operationType} for ${JSON.stringify(batchParams)}`,
            causedBy: error,
          });
        }
      }

      if (retries > MAX_RETRIES && unprocessedItems && Object.keys(unprocessedItems).length > 0) {
        logger.log(LogLevel.ERROR, `Max retries reached, some items were not processed`, { data: unprocessedItems });
        throw new GeneralError({
          message: `Max retries reached, some items ${JSON.stringify(batchParams)} were not processed for ${operationType}`,
        });
      } else {
        logger.log(
          LogLevel.DEBUG,
          ` After ${retries} retries the ${batchCount} batch ${operationType} has been successfully executed`,
        );
      }
    } else {
      logger.log(
        LogLevel.DEBUG,
        `Successfully completed the ${batchCount} batch ${operationType} without unprocessed items`,
      );
    }
  }
}

function arraysHaveSameElements(array1: string[] | null, array2: string[] | null): boolean {
  if (array1 === null && array2 === null) {
    return true;
  }

  if (array1 === null || array2 === null) {
    return false;
  }

  // Check if the arrays have the same length
  if (array1.length !== array2.length) {
    return false;
  }

  // Sort both arrays to ensure elements are in the same order
  const sortedArray1 = array1.slice().sort();
  const sortedArray2 = array2.slice().sort();

  // Compare the sorted arrays element by element
  for (let i = 0; i < sortedArray1.length; i++) {
    if (sortedArray1[i] !== sortedArray2[i]) {
      return false;
    }
  }

  // If all elements match, the arrays have the same elements
  return true;
}
