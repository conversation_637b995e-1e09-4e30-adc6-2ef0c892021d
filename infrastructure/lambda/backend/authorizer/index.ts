import { APIGatewayRequestAuthorizerEvent, APIGatewayAuthorizerResult, Statement, Context } from 'aws-lambda';
import * as jwt from 'jsonwebtoken';
import { JwksClient } from 'jwks-rsa';
import { EXISTING_PERMISSIONS, PermissionEndpointMapping } from '../../../lib/utils/constants';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import assert from 'assert';
import { KasAuthEndpointResponse } from '../../../lib/types/kas-auth-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from '../../utils/utils';

const publicKeyEndpoint = getEnvVarWithAssert('PUBLIC_KEY_ENDPOINT');
const issuer = getEnvVarWithAssert('ISSUER');
const clientId = getEnvVarWithAssert('CLIENT_ID');
const kasAuthEndpointUrl = getEnvVarWithAssert('KAS_AUTH_ENDPOINT_URL');
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const permissionsDynamoDbTableName = getEnvVarWithAssert('PERMISSIONS_DYNAMODB_TABLE_NAME');
const stage = getEnvVarWithAssert('STAGE');
const dbClient = new DynamoDBClient({ region: process.env.AWS_REGION });

const logger = new KasLambdaLogger('backendAuthorizer', LogLevel.TRACE);

let publicKeyCache: string | undefined = undefined;

// Create a new JWKS (JSON Web Key Set) client instance with the public key endpoint
const client = new JwksClient({
  jwksUri: publicKeyEndpoint,
});

// Asynchronously fetch the public key for a given Key ID (kid)
const fetchPublicKey = async (kid: string): Promise<string> => {
  const key = await client.getSigningKey(kid);
  return key.getPublicKey();
};

// Asynchronously get the public key for a given Key ID (kid) and cache it for future use
const getPublicKey = async (kid: string): Promise<string> => {
  if (!publicKeyCache) {
    publicKeyCache = await fetchPublicKey(kid);
  }
  return publicKeyCache;
};

// Asynchronously validate a JWT token using the public key, issuer
const validateToken = async (token: string): Promise<boolean> => {
  const options = {
    issuer,
  };

  // Decode the JWT token to access the header
  const decodedToken = jwt.decode(token, { complete: true });

  // Check if the decodedToken is valid and has a header with a kid property
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (!decodedToken?.header?.kid) {
    logger.log(LogLevel.WARN, 'Invalid token or missing kid in header');
    return false;
  }
  const kid = decodedToken.header.kid;

  const payload = decodedToken.payload as jwt.JwtPayload;
  if (payload.client_id !== clientId) {
    logger.log(LogLevel.WARN, 'Invalid client_id in payload', { data: payload });
    return false;
  }

  try {
    const publicKey = await getPublicKey(kid);
    logger.log(LogLevel.INFO, 'Validating token from publicKey', { data: publicKey });

    jwt.verify(token, publicKey, options);
    return true;
  } catch (error: unknown) {
    if (error instanceof Error) {
      logger.log(LogLevel.WARN, 'Token validation failed', { data: String(error.message) });
    } else {
      logger.log(LogLevel.FATAL, 'Token validation failed: Unknown error');
    }

    // Refresh the public key and retry validation
    publicKeyCache = await fetchPublicKey(kid);
    try {
      jwt.verify(token, publicKeyCache, options);
      return true;
    } catch (innerError: unknown) {
      if (innerError instanceof Error) {
        logger.log(LogLevel.WARN, 'Token validation failed', { data: String(innerError.message) });
      } else {
        logger.log(LogLevel.FATAL, 'Token validation failed: Unknown error');
      }
      return false;
    }
  }
};

// Generate an AWS IAM policy with the specified principalId, effect, and resource

const generateDenyPolicy = (
  principalId: string,
  resourcePathWithoutMethod: string,
  errorMessage?: string,
): APIGatewayAuthorizerResult => {
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Deny',
          Resource: `${resourcePathWithoutMethod}*`,
        },
      ],
    },
    context: {
      errorMessage: errorMessage,
    },
  };
};

const getPermissionsForGroup = async (group: string): Promise<string[]> => {
  logger.log(LogLevel.DEBUG, 'getting permissions for role', { data: group });
  const cmd = new GetCommand({
    TableName: permissionsDynamoDbTableName,
    Key: {
      applicationRole: group,
      applicationName: applicationNameToAuthorize,
    },
  });

  try {
    const res = await dbClient.send(cmd);
    logger.log(LogLevel.DEBUG, 'Db get result', { data: res });
    if (!res.Item) {
      logger.log(LogLevel.WARN, 'No permissions found for role', { data: group });
      return [];
    } else if (res.Item.is_deactivated) {
      logger.log(LogLevel.WARN, 'Permissions for role are deactivated', { data: group });
      return [];
    } else {
      return (res.Item.app_permissions as string[] | undefined) ?? [];
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to query table ${permissionsDynamoDbTableName}`, {
      data: { error: error, cmd: cmd },
    });
    throw error;
  }
};

function generateAllowPolicy(
  principalId: string,
  resourcePathWithoutMethod: string,
  permissionsForUser: string[],
): APIGatewayAuthorizerResult {
  const permissionMapping = PermissionEndpointMapping.PERMISSIONS;

  const Statements: Statement[] = [];
  logger.log(LogLevel.DEBUG, 'permissions', {
    data: { user_permissions: permissionsForUser, mapping: { permissionMapping } },
  });

  for (const permission of permissionMapping) {
    if (permissionsForUser.includes(permission.permissionName)) {
      permission.endpoints.forEach((endpoint) => {
        Statements.push({
          Action: 'execute-api:Invoke',
          Effect: 'Allow',
          Resource: `${resourcePathWithoutMethod}${endpoint.method}${endpoint.endpointRegex}`,
        });
      });
    }
    if (!permissionsForUser.includes(EXISTING_PERMISSIONS.NCO_CANCEL)) {
      Statements.push({
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: `${resourcePathWithoutMethod}PATCH/new-car-order/cancel`,
      });
    }
    if (!permissionsForUser.includes(EXISTING_PERMISSIONS.NCO_DEALLOCATE_QUOTA)) {
      Statements.push({
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: `${resourcePathWithoutMethod}PATCH/new-car-order/deallocate-quota`,
      });
    }
    if (!permissionsForUser.includes(EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY)) {
      Statements.push({
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: `${resourcePathWithoutMethod}PATCH/new-car-order/${EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY}`,
      });
    }
    if (!permissionsForUser.includes(EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY)) {
      Statements.push({
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: `${resourcePathWithoutMethod}PATCH/new-car-order/${EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY}`,
      });
    }
  }

  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: Statements,
    },
  };
}

// Extract the 'Authorization' cookie value from the cookieHeader string
const getAuthorizationFromCookie = (cookieHeader: string | undefined): string | undefined => {
  logger.log(LogLevel.TRACE, 'getAuthorizationFromCookie input', { data: cookieHeader });
  if (!cookieHeader) {
    const res = undefined;
    logger.log(LogLevel.TRACE, 'getAuthorizationFromCookie output (undefined)', { data: res });
    return res;
  }
  const authCookieName = 'KasAuthorization-' + stage;
  logger.log(LogLevel.TRACE, 'authCookieName', { data: authCookieName });

  // Split the cookieHeader into separate cookies and search for 'Authorization' cookie
  const cookies = cookieHeader.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.split('=');
    if (name.trim() === authCookieName) {
      const res = value.trim();
      logger.log(LogLevel.TRACE, 'getAuthorizationFromCookie output', { data: res });
      return res;
    }
  }
  // Return undefined if the 'Authorization' cookie is not found
  const res = undefined;
  logger.log(LogLevel.TRACE, 'getAuthorizationFromCookie output (undefined)', { data: res });
  return res;
};

function checkIfUserHasAccess(data: KasAuthEndpointResponse): boolean {
  if (!data.kasApplications[applicationNameToAuthorize]) {
    logger.log(LogLevel.INFO, 'user has no access', {
      data: { apps: data.kasApplications, needed_application: applicationNameToAuthorize },
    });
    return false;
  }
  if (data.kasApplications[applicationNameToAuthorize]) {
    logger.log(LogLevel.DEBUG, 'user has access');
    return true;
  }
  return false;
}

async function getKasAuthUserData(token: string): Promise<KasAuthEndpointResponse> {
  logger.log(LogLevel.TRACE, 'getKasAuthUserData input', { data: token });
  logger.log(LogLevel.DEBUG, 'starting call to auth service', { data: { url: kasAuthEndpointUrl } });
  const response = await fetch(kasAuthEndpointUrl, {
    method: 'GET',
    headers: {
      Authorization: token,
    },
  });
  if (!response.ok) {
    if (response.status === 406) {
      const data = await response.json();
      logger.log(LogLevel.WARN, 'Too many groups', { data: data });
      throw new Error('too many groups');
    }
    logger.log(LogLevel.ERROR, 'error response from kathAuthEndpoint', {
      data: { status: response.status, text: response.statusText, url: response.url },
    });
    throw new Error('Unauthorized, error from kas auth');
  }
  const data = (await response.json()) as KasAuthEndpointResponse | undefined;
  if (!data) {
    throw new Error('Got invalid/empty answer from KAS Auth Endpoint');
  }
  logger.log(LogLevel.TRACE, 'getKasAuthUserData output', { data: data });
  return data;
}

// Lambda function handler for AWS API Gateway custom authorizer
export const handler = async (
  event: APIGatewayRequestAuthorizerEvent,
  context: Context,
): Promise<APIGatewayAuthorizerResult> => {
  logger.setRequestContext(context);
  logger.log(LogLevel.TRACE, 'event', { data: event });

  const principalId = 'user';
  // Get the 'cookie' header from the event
  const cookie = event.headers?.cookie ?? event.headers?.Cookie;
  // Get the authorizationHeader based on the event type and the 'cookie' header
  const authorizationHeader = getAuthorizationFromCookie(cookie);

  // Parse the event to get the resource path for the whole API
  const httpMethod = event.requestContext.httpMethod;
  const resourcePathWithoutMethod = `${event.methodArn.split(httpMethod)[0]}`; //arn:aws:execute-api:eu-west-1:897501187430:83pfuyq8vc/prod/ <----     GET   /new-car-order/DEVZHW0U

  // If there is no authorizationHeader, generate a deny policy
  if (!authorizationHeader) {
    logger.log(LogLevel.WARN, 'Missing authorization cookie');
    throw new Error('Unauthorized');
  }

  // If the authorizationHeader is present, validate the token
  const isValid = await validateToken(authorizationHeader);

  //if token is not valid, generate a deny policy
  if (!isValid) {
    logger.log(LogLevel.WARN, 'Invalid token');
    throw new Error('Unauthorized');
  }

  try {
    // If the token is valid, check if the user is in the access group
    const userDataFromAuthEndpoint = await getKasAuthUserData(authorizationHeader);

    const userHasAccess = checkIfUserHasAccess(userDataFromAuthEndpoint);

    // If user is in access group, generate an allow policy; otherwise, generate a deny policy
    if (!userHasAccess) {
      return generateDenyPolicy(principalId, resourcePathWithoutMethod);
    } else {
      // Needed because eslint is stupid and doesn't understand assert/if for objects
      const _groups = userDataFromAuthEndpoint.kasApplications[applicationNameToAuthorize];
      assert(_groups, `UserDataFromAuthEndpoint does not contain application name: ${applicationNameToAuthorize}`);
      const permissionforUser = await getPermissionsForGroup(_groups[0].role);
      if (permissionforUser.length === 0) {
        logger.log(LogLevel.DEBUG, 'deny, no permissions for user');
        return generateDenyPolicy(principalId, resourcePathWithoutMethod, 'no permissions for user');
      }
      const policy = generateAllowPolicy(principalId, resourcePathWithoutMethod, permissionforUser);
      logger.log(LogLevel.INFO, 'Policy', { data: policy });
      policy.context = {
        userAttributes: JSON.stringify(userDataFromAuthEndpoint),
      };
      return policy;
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'error', { data: error });
    if (error instanceof Error) {
      return generateDenyPolicy(principalId, resourcePathWithoutMethod, error.message);
    }
  }

  return generateDenyPolicy(principalId, resourcePathWithoutMethod);
};
