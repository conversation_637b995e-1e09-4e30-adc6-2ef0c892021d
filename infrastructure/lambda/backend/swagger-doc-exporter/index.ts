import { APIGatewayProxyResult } from 'aws-lambda';
import { APIGateway } from '@aws-sdk/client-api-gateway';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from '../../utils/utils';

const restApiId = getEnvVarWithAssert('REST_API_ID');
const stage = process.env.STAGE ?? 'prod';

const logger = new KasLambdaLogger('swagger-doc-exporter', LogLevel.TRACE);

// Lambda function handler
export const handler = async (): Promise<APIGatewayProxyResult> => {
  const apiGateway = new APIGateway({ region: process.env.AWS_REGION });

  try {
    const response = await apiGateway
      .getExport({
        restApiId,
        stageName: stage,
        exportType: 'swagger',
        parameters: {
          extensions: 'apigateway',
        },
        accepts: 'application/json', // Specifying the response content type
      })
      .catch((error) => {
        logger.log(LogLevel.ERROR, 'error:', { data: error as unknown });
        throw error;
      });

    const buffer = Buffer.from(response.body!);
    const swaggerFile = buffer.toString('utf-8'); // Convert it back to string
    logger.log(LogLevel.DEBUG, 'file', { data: swaggerFile });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: swaggerFile,
    };
  } catch (error) {
    logger.log(LogLevel.ERROR, 'error: ', { data: error });
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Could not export OpenAPI specification' }),
    };
  }
};
