import { Kas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Brackets, SelectQueryBuilder, WhereExpressionBuilder } from 'typeorm';
import { CoraPurchaseIntentionModel } from '../../../lib/entities/purchase-intention-model';
import { FilterModel, SetFilterModel, TextFilterModel } from '../../../lib/types/ag-grid-ssrm-types';
import { FilterModelRequest } from '../../../lib/types/new-car-order-types';

export function agGridSsrmCreateSetFilterSqlForPI(
  queryBuilder: SelectQueryBuilder<CoraPurchaseIntentionModel>,
  key: keyof CoraPurchaseIntentionModel,
  filter: SetFilterModel,
): void {
  queryBuilder.andWhere(`pi.${key} IN (:...${key}s)`, {
    [`${key}s`]: filter.values,
  });
}

export function agGridSsrmCreateTextFilterSqlForPI(
  queryBuilder: SelectQueryBuilder<CoraPurchaseIntentionModel> | WhereExpressionBuilder,
  key: keyof CoraPurchaseIntentionModel,
  filter: TextFilterModel,
  useOrWhere: boolean = false,
  index: number = 0,
): void {
  const field = `pi.${key}`;
  const paramName = `${String(key)}${index}`;
  const paramValue = filter.filter;

  switch (filter.type) {
    case 'equals':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} = :${paramName}`, { [paramName]: paramValue });
      break;
    case 'notEqual':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} != :${paramName}`, { [paramName]: paramValue });
      break;
    case 'contains':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} ILIKE :${paramName}`, {
        [paramName]: `%${paramValue}%`,
      });
      break;
    case 'notContains':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} NOT ILIKE :${paramName}`, {
        [paramName]: `%${paramValue}%`,
      });
      break;
    case 'startsWith':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} ILIKE :${paramName}`, {
        [paramName]: `${paramValue}%`,
      });
      break;
    case 'endsWith':
      queryBuilder[useOrWhere ? 'orWhere' : 'andWhere'](`${field} ILIKE :${paramName}`, {
        [paramName]: `%${paramValue}`,
      });
      break;
  }
}

export function agGridSsrmCreateWhereSqlForPurchaseIntentions(
  queryBuilder: SelectQueryBuilder<CoraPurchaseIntentionModel>,
  filterModel: FilterModelRequest,
  logger: KasLambdaLogger,
): void {
  const keySet = Object.entries(filterModel) as [keyof CoraPurchaseIntentionModel, FilterModel][];

  for (const [k, v] of keySet) {
    if ('operator' in v) {
      // JoinFilterModel ( AND/OR group of conditions)
      queryBuilder.andWhere(
        new Brackets((qb) => {
          let index = 0;
          for (const condition of v.conditions) {
            agGridSsrmCreateTextFilterSqlForPI(qb, k, condition, v.operator === 'OR', index);
            index++;
          }
        }),
      );
    } else if (v.filterType === 'text') {
      // Simple text filtering
      agGridSsrmCreateTextFilterSqlForPI(queryBuilder, k, v);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } else if (v.filterType === 'set') {
      // Set filter (e.g., dropdown with multi-select)
      agGridSsrmCreateSetFilterSqlForPI(queryBuilder, k, v);
    } else {
      logger.log(LogLevel.ERROR, 'Unknown filter type in AgGrid SSRM filterModel', { data: v });
    }
  }
}
