import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import coraOrgRelData from '../../../../test/data/cora_org_rel.json';
import masterdataPortCodes from '../../../../test/data/masterdata-portcodes.json';
import { handler } from '.';
import { callback, context, event } from '../../../../test/test-types';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { CoraMdDealer, CoraMdImporter, CoraMdPortCodeResponseItem } from '../../../../lib/types/masterdata-types';
import { getMdDealerImporter } from '../../../utils/utils';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';

const ddbMock = mockClient(DynamoDBClient);
jest.mock('../../../utils/utils', () => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const originalUtilsModule = jest.requireActual('../../../utils/utils');
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return {
    ...originalUtilsModule,
    getMdDealerImporter: jest.fn(() => ({ dealer: {}, importer: {} })),
  };
});

const mdDealer: CoraMdDealer = {
  pk_importer_number: '2020001',
  sk_dealer_number: '3030001',
  standard_port_code: 'S_01',
  alternative_port_codes: ['A_01', 'A_02'],
  display_name: 'IntegrationTest - Dealer 1',
  modified_at: '2023-08-01T12:13:06.880Z',
  modified_by: 'unit_test',
};

const mdImporter: CoraMdImporter = {
  pk_importer_number: '2020001',
  code: 'IT',
  port_codes: ['PC_01', 'PC_02'],
  display_name: 'IntegrationTest - Importer',
  modified_at: '2023-10-04T14:16:30.079Z',
  modified_by: 'unit_test',
};

beforeEach(() => {
  ddbMock.reset();
});

it('should return 400, missing parameter', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: 'ppn_id-IMPORTER_01' }) },
    },
    queryStringParameters: {},
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(400);
});

it('should return 401, missing ppnId', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    queryStringParameters: { dealer_number: '2020001' },
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(401);
});

it('should return 401, could not determine visibility level', async () => {
  const ppnId = 'ppn_id-IMPORTER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: {
        userAttributes: JSON.stringify({
          organizationId: ppnId,
          kasApplications: {
            cora: [
              {
                role: 'ppn_approle_kas_importer_dev',
              },
            ],
          },
        }),
      },
    },
    queryStringParameters: { dealer_number: '2020001' },
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(401);
});

it('should return 3 PortCodes, Importer01', async () => {
  const ppnId = 'ppn_id-IMPORTER_01';
  (getMdDealerImporter as jest.Mock).mockImplementation(() => ({ dealer: mdDealer, importer: mdImporter }));
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: {
        userAttributes: JSON.stringify({
          organizationId: ppnId,
          kasApplications: {
            cora: [
              {
                role: 'ppn_approle_kas_importer_dev',
                modelTypeVisibility: 'IMP',
              },
            ],
          },
        }),
      },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataPortCodes as CoraMdPortCodeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdPortCodeResponseItem[];
  expect(resBody.length).toBe(3);
  expect(!!resBody.find((pC) => pC.pk_port_code === 'S_01')).toBe(true);
  expect(!!resBody.find((pC) => pC.pk_port_code === 'A_01')).toBe(true);
  expect(!!resBody.find((pC) => pC.pk_port_code === 'PC_01')).toBe(true);
  expect(resBody.every((pC) => pC.editable)).toBe(true);
});

it('should return 1 PortCodes, Dealer01', async () => {
  const ppnId = 'ppn_id-DEALER_01';
  (getMdDealerImporter as jest.Mock).mockImplementation(() => ({ dealer: mdDealer, importer: mdImporter }));
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: {
        userAttributes: JSON.stringify({
          organizationId: ppnId,
          kasApplications: {
            cora: [
              {
                role: 'ppn_approle_kas_dealer_dev',
                modelTypeVisibility: 'DLR',
              },
            ],
          },
        }),
      },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataPortCodes as CoraMdPortCodeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdPortCodeResponseItem[];
  expect(resBody.length).toBe(1);
  expect(!!resBody.find((pC) => pC.pk_port_code === 'S_01')).toBe(true);
  expect(resBody.every((pC) => pC.editable)).toBe(false);
});
