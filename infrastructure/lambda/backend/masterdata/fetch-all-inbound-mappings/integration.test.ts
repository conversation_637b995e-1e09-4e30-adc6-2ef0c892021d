/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DataSource } from 'typeorm';
import { OneVmsEventHandlerKey } from '../../../../lib/utils/constants';
import {
  buildLambdaArn,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
} from '../../../utils/integration-test-helpers';
import { v4 as uuidv4 } from 'uuid';
import { InboundProcessMappingModel } from '../../../../lib/entities/inbound-mapping-model';
import { OneVmsEventKey, OneVmsSourceSystemKey, SpecialStatusCode } from '../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn('fetch-all-inbound-mappings');

//inbound event mapping test object
const inboundEventMapping: InboundProcessMappingModel = {
  id: uuidv4(),
  target_event_handler: OneVmsEventHandlerKey.UPDATE_NCO_CORE,
  event: OneVmsEventKey.UPDATE_CORE_DATA,
  source_system: OneVmsSourceSystemKey.CORA_USER,
  order_status_code: 'IntegrationTestOrderStatus',
  error_status_code: SpecialStatusCode.NONE,
  invoice_status_code: SpecialStatusCode.ALL,
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

let dataSource: DataSource;

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([InboundProcessMappingModel]);

  //save inbound status mapping
  await dataSource.getRepository(InboundProcessMappingModel).save(inboundEventMapping);
}, 120000);

afterAll(async () => {
  //delete inbound status mapping
  await dataSource.getRepository(InboundProcessMappingModel).delete({
    id: inboundEventMapping.id,
  });

  await dataSource.destroy();
}, 120000);

it('should return 200 with the list of inbound status mappings', async () => {
  const event = createApiGwEvent({});
  const res = await invokeApiGwLambda(lambdaArn, event);
  expect(res.statusCode).toStrictEqual(200);
  const body = JSON.parse(res.body);
  expect(body.message).toBeUndefined();
  expect(Array.isArray(body)).toBe(true);
  expect(body.length).toBeGreaterThanOrEqual(1);

  //check that test mapping is in response and has all fields
  const testMapping = (body as InboundProcessMappingModel[]).find((mapping) => mapping.id === inboundEventMapping.id);
  expect(testMapping).toBeDefined();
  expect(testMapping?.id).toBe(inboundEventMapping.id);
  expect(testMapping?.target_event_handler).toBe(inboundEventMapping.target_event_handler);
  expect(testMapping?.event).toBe(inboundEventMapping.event);
  expect(testMapping?.order_status_code).toBe(inboundEventMapping.order_status_code);
  expect(testMapping?.error_status_code).toBe(inboundEventMapping.error_status_code);
  expect(testMapping?.invoice_status_code).toBe(inboundEventMapping.invoice_status_code);
  expect(testMapping?.created_by).toBe(inboundEventMapping.created_by);
  expect(testMapping?.modified_by).toBe(inboundEventMapping.modified_by);
});
