import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import coraOrgRelData from '../../../../test/data/cora_org_rel.json';
import masterdataShippingCodes from '../../../../test/data/masterdata-shippingcodes.json';
import { handler } from '.';
import { callback, context, event } from '../../../../test/test-types';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { CoraMdShippingCodeResponseItem } from '../../../../lib/types/masterdata-types';

const ddbMock = mockClient(DynamoDBClient);

beforeEach(() => {
  ddbMock.reset();
});

it('should return 400, missing parameter', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: 'ppn_id-IMPORTER_01' }) },
    },
    queryStringParameters: {},
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(400);
});

it('should return 401, missing ppnId', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    queryStringParameters: { dealer_number: '2020001' },
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(401);
});

it('should return 6 Shipping Codes, Importer01', async () => {
  const ppnId = 'ppn_id-IMPORTER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataShippingCodes as CoraMdShippingCodeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdShippingCodeResponseItem[];
  expect(resBody.length).toBe(6);
  expect(resBody.some((sC) => sC.pk_shipping_code === '2' && sC.editable)).toBe(true);
  expect(resBody.some((sC) => sC.pk_shipping_code === '3' && sC.editable)).toBe(true);
  expect(resBody.some((sC) => sC.pk_shipping_code === '4' && sC.editable)).toBe(true);
});

it('should return 6 Shipping Codes, Importer02', async () => {
  const ppnId = 'ppn_id-IMPORTER_02';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '2020002' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataShippingCodes as CoraMdShippingCodeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdShippingCodeResponseItem[];
  expect(resBody.length).toBe(6);
  expect(resBody.some((sC) => sC.pk_shipping_code === '1' && sC.editable)).toBe(true);
  expect(resBody.some((sC) => sC.pk_shipping_code === '4' && sC.editable)).toBe(true);
});

it('should return 6 Shipping Codes, Dealer01', async () => {
  const ppnId = 'ppn_id-DEALER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataShippingCodes as CoraMdShippingCodeResponseItem[] });
  console.log(_event);
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdShippingCodeResponseItem[];
  expect(resBody.length).toBe(6);
  expect(resBody.some((sC) => sC.pk_shipping_code === '2' && sC.editable)).toBe(true);
});

it('should return 6 Shipping Codes of which 1 is editable, Dealer05', async () => {
  const ppnId = 'ppn_id-DEALER_05';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030005' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataShippingCodes as CoraMdShippingCodeResponseItem[] });
  console.log(_event);
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdShippingCodeResponseItem[];
  expect(resBody.length).toBe(6);
  expect(resBody.some((sC) => sC.pk_shipping_code === '6' && sC.editable)).toBe(true);
});
