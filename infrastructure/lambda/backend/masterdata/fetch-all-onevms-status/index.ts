import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert, scanAllFromTable } from '../../../utils/utils';
import { CoraMdOneVmsStatus } from '../../../../lib/types/masterdata-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const statusMappingsTableName = getEnvVarWithAssert('TABLE_NAME_STATUS_MAPPINGS');

const fetchAllOneVMSStatusFuc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const onevsmStatus = await scanAllFromTable<CoraMdOneVmsStatus>(
      { tableName: statusMappingsTableName, dynamoDb },
      logger,
    );
    return sendSuccess({ body: onevsmStatus, reqHeaders: event.headers }, logger);
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Error getting onevms status', { data: err });
    return sendFail({ message: 'Error loading onevms status', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-onevms-status', LogLevel.TRACE)(event, context, fetchAllOneVMSStatusFuc);
