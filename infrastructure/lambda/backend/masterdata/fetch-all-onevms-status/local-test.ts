/* eslint-disable */
/**
 * This local-test.ts file appears to be a script for local testing of a specific AWS Lambda function handler, defined in the same directory.
 *
 * Steps to run this script:
 * 1. Open a terminal window in the directory containing this file.
 * 2. Run the command `npm install` to install the dependencies for this script.
 * 3. Get AWS credentials for the account you want to test with.
 * 4. Run npx ts-node lambda/backend/shipping-code/get-all-shipping-codes/local-test.ts
 *
 * Note: The environment variables used in this script are mocked and need to be updated to match the environment variables used for your test.
 */
import { Context } from 'aws-lambda';
import { handler } from '.';
import { Constants } from '../../../../lib/utils/constants';
import { createApiGwEvent } from '../../../utils/integration-test-helpers';

(async () => {
  process.env.AWS_REGION = 'eu-west-1';
  process.env.TABLE_NAME = `${Constants.APPLICATION_SHORT_NAME}-dev-onevms-status`;

  const event = createApiGwEvent({});

  const context: Context = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: '',
    functionVersion: '',
    invokedFunctionArn: '',
    memoryLimitInMB: '',
    awsRequestId: '',
    logGroupName: '',
    logStreamName: '',
    getRemainingTimeInMillis: function (): number {
      throw new Error('Function not implemented.');
    },
    done: function (error?: Error | undefined, result?: any): void {
      throw new Error('Function not implemented.');
    },
    fail: function (error: string | Error): void {
      throw new Error('Function not implemented.');
    },
    succeed: function (messageOrObject: any): void {
      throw new Error('Function not implemented.');
    },
  };

  const callback = (error: any, result: any) => {
    if (error) {
      console.error(error);
    } else {
      console.log(result);
    }
  };

  try {
    const result = await handler(event, context, callback);
    console.log(result);
  } catch (error) {
    console.error(error);
  }
})();
