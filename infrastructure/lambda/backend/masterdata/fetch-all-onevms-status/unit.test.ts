import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { ScanCommand } from '@aws-sdk/lib-dynamodb';
import masterdataOnevmsStatus from '../../../../test/data/masterdata-onevms-status.json';
import { handler } from '.';
import { callback, context, event } from '../../../../test/test-types';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { CoraMdOneVmsStatus } from '../../../../lib/types/masterdata-types';

const ddbMock = mockClient(DynamoDBClient);

beforeEach(() => {
  ddbMock.reset();
});

it('should return all 2 onevms status', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
  };
  ddbMock.on(ScanCommand).resolves({ Items: masterdataOnevmsStatus as CoraMdOneVmsStatus[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdOneVmsStatus[];
  expect(resBody.length).toBe(masterdataOnevmsStatus.length);
  expect(resBody.some((os) => os.one_vms_status === 'PP1301' && os.order_changeable)).toBe(true);
  expect(resBody.some((os) => os.one_vms_status === 'PP1302' && os.order_changeable)).toBe(false);
});
