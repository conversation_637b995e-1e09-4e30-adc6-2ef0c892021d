import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getPpnId, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { QueryCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { Constants } from '../../../../lib/utils/constants';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { getEnvVarWithAssert } from '../../../utils/utils';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const dlrTableName = getEnvVarWithAssert('TABLE_NAME_DEALER');
const impTableName = getEnvVarWithAssert('TABLE_NAME_IMPORTER');
const orgRelsTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');

const fetchDealerImporterFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const dealerNumber = event.queryStringParameters ? event.queryStringParameters['dealer_number'] : undefined;
  //get the org id of the user from auth context
  const ppnId = getPpnId({ event }, logger);

  if (!dealerNumber) {
    logger.log(LogLevel.WARN, 'dealer_number query param is required but missing', {
      data: sanitizeApiGwEvent({ event }, logger),
    });
    return sendFail(
      { message: 'Missing dealer number query parameter', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  if (!ppnId) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event }, logger) });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  //get all Orgs the user has access to
  const cmd = new QueryCommand({
    TableName: orgRelsTableName,
    IndexName: Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex,
    KeyConditionExpression: `${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.sk} = :value`,
    ExpressionAttributeValues: { ':value': ppnId },
  });

  try {
    const dynamoResult = await dynamoDb.send(cmd);
    if (dynamoResult.Items) {
      const activeUserOrgs = (dynamoResult.Items as CoraOrgRelModel[]).filter((org) => !org.is_deactivated);
      //find org that corresponds to the provided dealer number
      const correspondingDealerOrg = activeUserOrgs.find(
        (org) => org.dealer_number === dealerNumber && org.importer_number,
      );
      if (correspondingDealerOrg) {
        //load dealer and importer from masterdata tables and return result
        const batchGetCmd = new BatchGetCommand({
          RequestItems: {
            [dlrTableName]: {
              Keys: [
                {
                  pk_importer_number: correspondingDealerOrg.importer_number,
                  sk_dealer_number: correspondingDealerOrg.dealer_number,
                },
              ],
            },
            [impTableName]: {
              Keys: [{ pk_importer_number: correspondingDealerOrg.importer_number }],
            },
          },
        });
        try {
          const impDlrResult = await dynamoDb.send(batchGetCmd);
          if (!impDlrResult.Responses?.[dlrTableName] || !(impDlrResult.Responses[dlrTableName].length > 0)) {
            return sendFail(
              { message: 'Could not load dealer from masterdata', status: 404, reqHeaders: event.headers },
              logger,
            );
          }
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          else if (!impDlrResult.Responses[impTableName] || !(impDlrResult.Responses[impTableName].length > 0)) {
            return sendFail(
              { message: 'Could not load importer from masterdata', status: 404, reqHeaders: event.headers },
              logger,
            );
          } else {
            const mdDlr = impDlrResult.Responses[dlrTableName][0];
            const mdImp = impDlrResult.Responses[impTableName][0];
            return sendSuccess({ body: { dealer: mdDlr, importer: mdImp }, reqHeaders: event.headers }, logger);
          }
        } catch (error) {
          logger.log(LogLevel.ERROR, 'Failed BatGetCommand', { data: { error: error, cmd: batchGetCmd } });
          throw error;
        }
      } else {
        return sendFail(
          {
            message: 'User is not authorized to access dealer information for: ' + dealerNumber,
            status: 403,
            reqHeaders: event.headers,
          },
          logger,
        );
      }
    } else {
      return sendFail(
        { message: 'Could not find any org information for user', status: 404, reqHeaders: event.headers },
        logger,
      );
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed QueryCommand', { data: { error: error, cmd: cmd } });
    throw error;
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-dealer-importer', LogLevel.TRACE)(event, context, fetchDealerImporterFunc);
