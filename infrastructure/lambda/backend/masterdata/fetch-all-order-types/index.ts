import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getPpnId, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert, scanAllFromTable } from '../../../utils/utils';
import { getAllAuthorizedOrgs, getPermissionForDealer } from '../../../utils/validation-helpers';
import { CoraMdOrderType, CoraMdOrderTypeResponseItem } from '../../../../lib/types/masterdata-types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const orderTypesTableName = getEnvVarWithAssert('TABLE_NAME_ORDER_TYPES');
const orgRelsTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');

const fetchAllOrderTypesFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const dealerNumber = event.queryStringParameters ? event.queryStringParameters['dealer_number'] : undefined;
    if (!dealerNumber) {
      return sendFail({ message: 'Missing DealerNumber', status: 400, reqHeaders: event.headers }, logger);
    }
    //get the org id of the user from auth context
    const ppnId = getPpnId({ event }, logger);
    if (!ppnId) {
      logger.log(LogLevel.WARN, 'Failed to get the ppnId', { data: sanitizeApiGwEvent({ event }, logger) });
      return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
    }
    const authorizedOrgs = await getAllAuthorizedOrgs(
      { dynamoDb: dynamoDb, orgRelsTableName: orgRelsTableName, ppnId: ppnId },
      logger,
    );
    const correspondingOrg = authorizedOrgs.find((org) => org.importer_number && org.dealer_number === dealerNumber);
    if (!correspondingOrg) {
      return sendFail(
        { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
        logger,
      );
    }
    let allowed_order_types: CoraMdOrderTypeResponseItem[] = [];
    const orderTypes = await scanAllFromTable<CoraMdOrderType>(
      { tableName: orderTypesTableName, dynamoDb: dynamoDb },
      logger,
    );
    switch (getPermissionForDealer({ dealerNumber: dealerNumber, authorizedOrgs: authorizedOrgs }, logger)) {
      case 'Dealer':
        allowed_order_types = orderTypes.map((orderType) => {
          if (orderType.is_deactivated) {
            return { ...orderType, editable: false };
          }
          const importerIsIncluded = orderType.importers.includes(correspondingOrg.importer_number!);
          if (orderType.imp_is_blacklist && importerIsIncluded) {
            return { ...orderType, editable: false };
          }
          if (!orderType.imp_is_blacklist && !importerIsIncluded) {
            return { ...orderType, editable: false };
          }
          if (!orderType.dlr_is_visible) {
            return { ...orderType, editable: false };
          }
          return { ...orderType, editable: true };
        });
        break;
      case 'Importer':
        allowed_order_types = orderTypes.map((orderType) => {
          if (orderType.is_deactivated) {
            return { ...orderType, editable: false };
          }
          const importerIsIncluded = orderType.importers.includes(correspondingOrg.importer_number!);
          if (orderType.imp_is_blacklist && importerIsIncluded) {
            return { ...orderType, editable: false };
          }
          if (!orderType.imp_is_blacklist && !importerIsIncluded) {
            return { ...orderType, editable: false };
          }
          return { ...orderType, editable: true };
        });
        break;
      default:
        logger.log(LogLevel.INFO, `No Permissions or Org does not exists: ${dealerNumber}`);
        return sendFail(
          { message: `No Permissions or Org does not exists: ${dealerNumber}`, status: 400, reqHeaders: event.headers },
          logger,
        );
    }
    return sendSuccess({ body: allowed_order_types, reqHeaders: event.headers }, logger);
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Error getting orderTypes', { data: err });
    return sendFail({ message: 'Error loading orderTypes', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-order-types', LogLevel.TRACE)(event, context, fetchAllOrderTypesFunc);
