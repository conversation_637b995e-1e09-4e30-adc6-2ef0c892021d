import { mockClient } from 'aws-sdk-client-mock';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import coraOrgRelData from '../../../../test/data/cora_org_rel.json';
import masterdataOrderTypes from '../../../../test/data/masterdata-ordertypes.json';
import { handler } from '.';
import { callback, context, event } from '../../../../test/test-types';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { CoraMdOrderTypeResponseItem } from '../../../../lib/types/masterdata-types';

const ddbMock = mockClient(DynamoDBClient);

beforeEach(() => {
  ddbMock.reset();
});

it('should return 400, missing parameter', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: 'ppn_id-IMPORTER_01' }) },
    },
    queryStringParameters: {},
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(400);
});

it('should return 401, missing ppnId', async () => {
  const _event: APIGatewayProxyEvent = {
    ...event,
    queryStringParameters: { dealer_number: '3030001' },
  };
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(401);
});

it('should return 3 OrderTypes with 2 editable, Importer01', async () => {
  const ppnId = 'ppn_id-IMPORTER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataOrderTypes as CoraMdOrderTypeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdOrderTypeResponseItem[];
  expect(resBody.length).toBe(3);
  expect(!!resBody.find((oT) => oT.pk_order_type === 'A1' && oT.editable)).toBe(true);
  expect(!!resBody.find((oT) => oT.pk_order_type === 'A0' && oT.editable)).toBe(true);
});

it('should return 3 OrderTypes with 1 editable, Importer02', async () => {
  const ppnId = 'ppn_id-IMPORTER_02';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030004' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataOrderTypes as CoraMdOrderTypeResponseItem[] });
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdOrderTypeResponseItem[];
  expect(resBody.length).toBe(3);
  expect(!!resBody.find((oT) => oT.pk_order_type === 'VF' && oT.editable)).toBe(true);
});

it('should return 3 OrderTypes with 2 editable, Dealer01', async () => {
  const ppnId = 'ppn_id-DEALER_01';
  const _event: APIGatewayProxyEvent = {
    ...event,
    requestContext: {
      ...event.requestContext,
      authorizer: { userAttributes: JSON.stringify({ organizationId: ppnId }) },
    },
    queryStringParameters: { dealer_number: '3030001' },
  };
  ddbMock
    .on(QueryCommand)
    .resolves({ Items: (coraOrgRelData as CoraOrgRelModel[]).filter((o) => o.parent_ppn_id === ppnId) });
  ddbMock.on(ScanCommand).resolves({ Items: masterdataOrderTypes as CoraMdOrderTypeResponseItem[] });
  console.log(_event);
  const res = await handler(_event, context, callback);
  expect(res?.statusCode).toStrictEqual(200);
  const resBody = JSON.parse(res?.body ?? '[]') as CoraMdOrderTypeResponseItem[];
  expect(resBody.length).toBe(3);
  expect(!!resBody.find((oT) => oT.pk_order_type === 'A0' && oT.editable)).toBe(true);
});
