import { SendMessageCommand } from '@aws-sdk/client-sqs';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { Kas<PERSON>ambdaLogger } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { APIGatewayProxyEventHeaders } from 'aws-lambda';
import {
  CorePayload,
  InboundEventDispatcherEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { ApiHandlerError } from '../../../utils/errors';
import { generateApiGatewayEvent, setupMocks } from '../../../utils/test-utils';
import { KafkaAdapter } from '../../../utils/kafka';

// Mock external dependencies
const mocks = setupMocks();
const pushObjsToTopicMock = jest.fn();

// Mock additional modules not covered by setupMocks
jest.mock('../../../utils/kafka');
jest.mock('../../../utils/secret-cache');
jest.mock('../../../utils/api-helpers');
jest.mock('../../../utils/utils');

// Setup KafkaAdapter mock
(KafkaAdapter as jest.MockedClass<typeof KafkaAdapter>).mockImplementation(() => {
  return {
    pushObjsToTopic: pushObjsToTopicMock,
  } as unknown as KafkaAdapter;
});

// Import mocked modules
import * as mockSecretCache from '../../../utils/secret-cache';
import * as mockApiHelpers from '../../../utils/api-helpers';
import * as mockUtils from '../../../utils/utils';

// Create typed mocks
const mockedUtils = jest.mocked(mockUtils);
const mockedApiHelpers = jest.mocked(mockApiHelpers);
const mockedSecretCache = jest.mocked(mockSecretCache);

// Import EventApiContext after mocks are set up
let EventApiContext: any;

const ppnId = 'someCoolPpnId';
const mockUserAttributes: KasAuthEndpointResponse = {
  username: 'testuser',
  firstName: 'Test',
  lastName: 'User',
  displayName: 'Test User',
  porschePartnerNo: ppnId,
  organizationId: ppnId,
  kasApplications: { cora: [{ modelTypeVisibility: 'DLR', role: 'ppn_approle_kas_dealer_dev' }] },
};

beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
  pushObjsToTopicMock.mockReset();
});
describe('EventApiContext', () => {
  const mockLogger = {
    log: jest.fn(),
  } as unknown as KasLambdaLogger;

  beforeAll(async () => {
    // Setup mock implementations
    mockUtils.getEnvVarWithAssert = jest.fn((key: string) => process.env[key]);
    mockedUtils.pushNotificationsToKafka = jest.fn().mockResolvedValue(undefined);

    mockApiHelpers.getAuthContext = jest.fn();
    mockApiHelpers.sendFail = jest.fn().mockReturnValue({
      statusCode: 500,
      body: JSON.stringify({ message: 'Error' }),
    });
    mockApiHelpers.sendSuccess = jest.fn().mockReturnValue({
      statusCode: 200,
      body: JSON.stringify({ message: 'Success' }),
    });

    mockSecretCache.secretCache = {
      initCache: jest.fn(),
    };

    // Import EventApiContext after mocks are set up
    const module = await import('./event-api-context');
    EventApiContext = module.EventApiContext;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mocks.sqsMock!.reset();
    pushObjsToTopicMock.mockReset();

    // Mock KasLambdaLogger
    jest
      .spyOn(require('@kas-resources/constructs/src/lib/kas-lambda-logger'), 'KasLambdaLogger')
      .mockImplementation(() => mockLogger);

    // Replace EventApiContext logger with mock after init
    if (EventApiContext) {
      EventApiContext.logger = mockLogger;
    }
  });

  describe('init', () => {
    it('should initialize EventApiContext with correct configuration', () => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);

      expect(EventApiContext.eventHandlerKey).toBe(OneVmsEventKey.UPDATE_NCO);
      expect(EventApiContext.logger).toBeDefined();
      expect(EventApiContext.kafkaAdapter).toBeDefined();
      expect(EventApiContext.sqsClient).toBeDefined();
      expect(mockSecretCache.secretCache.initCache).toHaveBeenCalledWith('something', '');
    });

    it('should initialize with additional secret ARN', () => {
      const additionalSecretArn = 'test-aurora-secret-arn';
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO, additionalSecretArn);

      expect(mockSecretCache.secretCache.initCache).toHaveBeenCalledWith('something', additionalSecretArn);
    });

    // Logger creation test removed for simplicity
  });

  describe('buildDispatcherEvent', () => {
    const mockCorePayload: CorePayload = {
      payload: { test: 'data' },
      sub_transaction_id: 'sub-test-id',
      event_type: OneVmsEventKey.UPDATE_NCO,
      ncos_info: [
        {
          pk_new_car_order_id: 'TEST_NCO_001',
          modified_at: '2024-01-01T00:00:00.000Z',
          sub_transaction_id: 'sub-test-id',
        },
      ],
    };

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
    });

    it('should build dispatcher event with correct structure', () => {
      const result = EventApiContext.buildDispatcherEvent(mockCorePayload, mockUserAttributes);

      expect(result).toMatchObject({
        payload: mockCorePayload.payload,
        sub_transaction_id: mockCorePayload.sub_transaction_id,
        event_type: mockCorePayload.event_type,
        ncos_info: mockCorePayload.ncos_info,
        transaction_id: expect.any(String),
        action_at: expect.any(String),
        user_auth_context: mockUserAttributes,
        source_system: OneVmsSourceSystemKey.CORA_USER,
      });
    });

    it('should generate unique transaction ID', () => {
      const result1 = EventApiContext.buildDispatcherEvent(mockCorePayload, mockUserAttributes);
      const result2 = EventApiContext.buildDispatcherEvent(mockCorePayload, mockUserAttributes);

      expect(result1.transaction_id).not.toBe(result2.transaction_id);
      expect(result1.transaction_id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should set action_at to current ISO timestamp', () => {
      const beforeTime = new Date().toISOString();
      const result = EventApiContext.buildDispatcherEvent(mockCorePayload, mockUserAttributes);
      const afterTime = new Date().toISOString();

      expect(result.action_at).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(result.action_at >= beforeTime).toBe(true);
      expect(result.action_at <= afterTime).toBe(true);
    });
  });

  describe('sendEventToDispatcherSqs', () => {
    const mockDispatcherEvent: InboundEventDispatcherEvent = {
      transaction_id: 'test-transaction-id',
      event_type: OneVmsEventKey.UPDATE_NCO,
      source_system: OneVmsSourceSystemKey.CORA_USER,
      action_at: '2024-01-01T00:00:00.000Z',
      payload: { test: 'data' },
      user_auth_context: mockUserAttributes,
    };

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
    });

    it('should send message to SQS successfully', async () => {
      mocks.sqsMock!.on(SendMessageCommand).resolves({ MessageId: 'test-message-id' });

      await EventApiContext.sendEventToDispatcherSqs(mockDispatcherEvent);

      expect(mocks.sqsMock!.commandCalls(SendMessageCommand)).toHaveLength(1);
      const sqsCall = mocks.sqsMock!.commandCalls(SendMessageCommand)[0];
      expect(sqsCall.args[0].input).toEqual({
        QueueUrl: 'https://sqs.eu-west-1.amazonaws.com/123456789012/dispatcher-queue',
        MessageBody: JSON.stringify(mockDispatcherEvent),
      });
      // Logger call verification removed for simplicity
    });

    it('should throw error when SQS response has no MessageId', async () => {
      mocks.sqsMock!.on(SendMessageCommand).resolves({});

      await expect(EventApiContext.sendEventToDispatcherSqs(mockDispatcherEvent)).rejects.toThrow(
        'Failed to send event to SQS. Missing MessageId in SQS response',
      );

      // Logger call verification removed for simplicity
    });

    it('should throw error when SQS call fails', async () => {
      const sqsError = new Error('SQS connection failed');
      mocks.sqsMock!.on(SendMessageCommand).rejects(sqsError);

      await expect(EventApiContext.sendEventToDispatcherSqs(mockDispatcherEvent)).rejects.toThrow(
        'SQS connection failed',
      );
    });
  });

  describe('enrichAndPublishNotifications', () => {
    const mockDispatcherEventWithNcos: InboundEventDispatcherEvent = {
      transaction_id: 'test-transaction-id',
      event_type: OneVmsEventKey.UPDATE_NCO,
      source_system: OneVmsSourceSystemKey.CORA_USER,
      action_at: '2024-01-01T00:00:00.000Z',
      payload: { test: 'data' },
      user_auth_context: mockUserAttributes,
      ncos_info: [
        {
          pk_new_car_order_id: 'NCO_001',
          modified_at: '2024-01-01T00:00:00.000Z',
          sub_transaction_id: 'sub-id-1',
        },
        {
          pk_new_car_order_id: 'NCO_002',
          modified_at: '2024-01-01T00:00:00.000Z',
          sub_transaction_id: 'sub-id-2',
        },
      ],
    };

    const mockDispatcherEventWithoutNcos: InboundEventDispatcherEvent = {
      transaction_id: 'test-transaction-id',
      event_type: OneVmsEventKey.CREATE,
      source_system: OneVmsSourceSystemKey.CORA_USER,
      action_at: '2024-01-01T00:00:00.000Z',
      payload: { test: 'data' },
      user_auth_context: mockUserAttributes,
      sub_transaction_id: 'main-sub-id',
    };

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
    });

    it('should create notifications for each NCO when ncos_info is present', async () => {
      const result = await EventApiContext.enrichAndPublishNotifications(mockDispatcherEventWithNcos);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        transaction_id: 'test-transaction-id',
        event_type: OneVmsEventKey.UPDATE_NCO,
        source_system: OneVmsSourceSystemKey.CORA_USER,
        sub_transaction_id: 'sub-id-1',
        action_by: 'testuser',
        action_at: '2024-01-01T00:00:00.000Z',
        nco_id: 'NCO_001',
        status: NotificationStatus.ACCEPTED,
        transaction_obj_amount: 2,
      });
      expect(result[1]).toMatchObject({
        transaction_id: 'test-transaction-id',
        sub_transaction_id: 'sub-id-2',
        nco_id: 'NCO_002',
        transaction_obj_amount: 2,
      });
    });

    it('should create single notification when ncos_info is not present', async () => {
      const result = await EventApiContext.enrichAndPublishNotifications(mockDispatcherEventWithoutNcos);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        transaction_id: 'test-transaction-id',
        event_type: OneVmsEventKey.CREATE,
        source_system: OneVmsSourceSystemKey.CORA_USER,
        sub_transaction_id: 'main-sub-id',
        action_by: 'testuser',
        action_at: '2024-01-01T00:00:00.000Z',
        status: NotificationStatus.ACCEPTED,
        transaction_obj_amount: 1,
      });
      expect(result[0]).not.toHaveProperty('nco_id');
    });

    it('should use "unknown" as action_by when username is missing', async () => {
      const eventWithoutUsername = {
        ...mockDispatcherEventWithNcos,
        user_auth_context: { ...mockUserAttributes, username: undefined },
      };

      const result = await EventApiContext.enrichAndPublishNotifications(
        eventWithoutUsername as unknown as InboundEventDispatcherEvent,
      );

      expect(result[0].action_by).toBe('unknown');
    });

    it('should use "unknown" as action_by when user_auth_context is undefined', async () => {
      const eventWithoutUserContext = {
        ...mockDispatcherEventWithoutNcos,
        user_auth_context: undefined,
      };

      const result = await EventApiContext.enrichAndPublishNotifications(eventWithoutUserContext);

      expect(result[0].action_by).toBe('unknown');
    });

    it('should generate sub_transaction_id when not present and no ncos_info', async () => {
      const eventWithoutSubId = {
        ...mockDispatcherEventWithoutNcos,
        sub_transaction_id: undefined,
      };

      const result = await EventApiContext.enrichAndPublishNotifications(eventWithoutSubId);

      expect(result[0].sub_transaction_id).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
      );
    });

    it('should call pushNotificationsToKafka with correct parameters', async () => {
      await EventApiContext.enrichAndPublishNotifications(mockDispatcherEventWithNcos);

      expect(mockUtils.pushNotificationsToKafka).toHaveBeenCalledWith(
        expect.any(Array),
        'KAFKA_TOPIC_NOTIFICATION',
        EventApiContext.kafkaAdapter,
        EventApiContext.logger,
      );
    });
  });

  describe('handleDispatcherEvent', () => {
    const mockDispatcherEvent: InboundEventDispatcherEvent = {
      transaction_id: 'test-transaction-id',
      event_type: OneVmsEventKey.UPDATE_NCO,
      source_system: OneVmsSourceSystemKey.CORA_USER,
      action_at: '2024-01-01T00:00:00.000Z',
      payload: { test: 'data' },
      user_auth_context: {
        username: 'testuser',
        organizationId: 'test-org',
        firstName: 'Test',
        lastName: 'User',
        porschePartnerNo: '12345',
        displayName: 'Test User',
        kasApplications: { cora: [{ modelTypeVisibility: 'DLR', role: 'USER' }] },
      },
    };

    const mockHeaders: APIGatewayProxyEventHeaders = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer token',
    };

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
      mocks.sqsMock!.on(SendMessageCommand).resolves({ MessageId: 'test-message-id' });
    });

    it('should handle successful event dispatch', async () => {
      const mockNotifications: NotificationKafkaEvent[] = [
        {
          transaction_id: 'test-transaction-id',
          sub_transaction_id: 'sub-id',
          event_type: OneVmsEventKey.UPDATE_NCO,
          action_by: 'testuser',
          action_at: '2024-01-01T00:00:00.000Z',
          source_system: OneVmsSourceSystemKey.CORA_USER,
          status: NotificationStatus.ACCEPTED,
        },
      ];

      jest.spyOn(EventApiContext, 'enrichAndPublishNotifications').mockResolvedValue(mockNotifications);

      const result = await EventApiContext.handleDispatcherEvent(mockDispatcherEvent, mockHeaders);

      expect(result.statusCode).toBe(200);
      expect(mockApiHelpers.sendSuccess).toHaveBeenCalledWith(
        {
          body: {
            message: 'Successfully sent api event to dispatcher, wait for result in notification center',
            data: {
              transaction_id: 'test-transaction-id',
              action_at: '2024-01-01T00:00:00.000Z',
              event_type: OneVmsEventKey.UPDATE_NCO,
            },
          },
          reqHeaders: mockHeaders,
        },
        EventApiContext.logger,
      );
    });

    it('should return error when SQS dispatch fails', async () => {
      const sqsError = new Error('SQS dispatch failed');
      mocks.sqsMock!.on(SendMessageCommand).rejects(sqsError);

      await EventApiContext.handleDispatcherEvent(mockDispatcherEvent, mockHeaders);

      expect(mockApiHelpers.sendFail).toHaveBeenCalledWith(
        {
          message: 'SQS dispatch failed',
          status: 500,
          reqHeaders: mockHeaders,
        },
        EventApiContext.logger,
      );
      // Logger call verification removed for simplicity
    });

    it('should continue when Kafka notification fails but SQS succeeds', async () => {
      const kafkaError = new Error('Kafka failed');
      jest.spyOn(EventApiContext, 'enrichAndPublishNotifications').mockRejectedValue(kafkaError);

      const result = await EventApiContext.handleDispatcherEvent(mockDispatcherEvent, mockHeaders);

      expect(result.statusCode).toBe(200);
      // Logger call verification removed for simplicity
      expect(mockApiHelpers.sendSuccess).toHaveBeenCalled();
    });
  });

  describe('commonEventInputValidation', () => {
    interface TestPayload {
      test: string;
      number: number;
    }

    const mockObjectValidator = {
      validate: jest.fn(),
    } as unknown as ObjectValidator<TestPayload>;

    const validPayload: TestPayload = { test: 'value', number: 42 };
    const mockUserAttributes: KasAuthEndpointResponse = {
      username: 'testuser',
      organizationId: 'test-org',
      firstName: 'Test',
      lastName: 'User',
      porschePartnerNo: '12345',
      displayName: 'Test User',
      kasApplications: { cora: [{ modelTypeVisibility: 'DLR', role: 'USER' }] },
    };

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
      mockApiHelpers.getAuthContext.mockReturnValue(mockUserAttributes);
      (mockObjectValidator.validate as jest.Mock).mockReturnValue([validPayload, null]);
    });

    it('should successfully validate valid input', () => {
      const event = generateApiGatewayEvent({
        body: JSON.stringify(validPayload),
      });

      const result = EventApiContext.commonEventInputValidation(event, mockObjectValidator);

      expect(result).toEqual({
        body_validated: validPayload,
        userAttributes: mockUserAttributes,
      });
      expect(mockApiHelpers.getAuthContext).toHaveBeenCalledWith({ event }, EventApiContext.logger);
      expect(mockObjectValidator.validate).toHaveBeenCalledWith(validPayload);
    });

    it('should throw ApiHandlerError when auth context is missing', () => {
      mockApiHelpers.getAuthContext.mockReturnValue(null);
      const event = generateApiGatewayEvent({
        body: JSON.stringify(validPayload),
      });

      expect(() => EventApiContext.commonEventInputValidation(event, mockObjectValidator)).toThrow(ApiHandlerError);

      // Logger call verification removed for simplicity
    });

    it('should throw ApiHandlerError when validation fails', () => {
      const validationErrors = ['field is required', 'invalid format'];
      (mockObjectValidator.validate as jest.Mock).mockReturnValue([null, validationErrors]);

      const event = generateApiGatewayEvent({
        body: JSON.stringify({ invalid: 'data' }),
      });

      expect(() => EventApiContext.commonEventInputValidation(event, mockObjectValidator)).toThrow(ApiHandlerError);

      // Logger call verification removed for simplicity
    });

    it('should handle empty request body', () => {
      const event = generateApiGatewayEvent({ body: '{}' });
      (mockObjectValidator.validate as jest.Mock).mockReturnValue([{}, null]);

      const result = EventApiContext.commonEventInputValidation(event, mockObjectValidator);

      expect(mockObjectValidator.validate).toHaveBeenCalledWith({});
      expect(result.body_validated).toEqual({});
    });

    it('should handle null request body with fallback to empty object', () => {
      const event = generateApiGatewayEvent({});
      // Manually set body to null to test the fallback
      event.body = null;
      (mockObjectValidator.validate as jest.Mock).mockReturnValue([{}, null]);

      const result = EventApiContext.commonEventInputValidation(event, mockObjectValidator);

      expect(mockObjectValidator.validate).toHaveBeenCalledWith({});
      expect(result.body_validated).toEqual({});
    });

    it('should handle malformed JSON', () => {
      const event = generateApiGatewayEvent({ body: 'invalid-json' });

      expect(() => EventApiContext.commonEventInputValidation(event, mockObjectValidator)).toThrow();
    });
  });

  describe('pushNotificationsToKafka', () => {
    const mockNotifications: NotificationKafkaEvent[] = [
      {
        transaction_id: 'test-id',
        sub_transaction_id: 'sub-id',
        event_type: OneVmsEventKey.UPDATE_NCO,
        action_by: 'testuser',
        action_at: '2024-01-01T00:00:00.000Z',
        source_system: OneVmsSourceSystemKey.CORA_USER,
        status: NotificationStatus.ACCEPTED,
      },
    ];

    beforeEach(() => {
      EventApiContext.init(OneVmsEventKey.UPDATE_NCO);
    });

    it('should call pushNotificationsToKafka utility function', async () => {
      await EventApiContext.pushNotificationsToKafka(mockNotifications);

      expect(mockUtils.pushNotificationsToKafka).toHaveBeenCalledWith(
        mockNotifications,
        'KAFKA_TOPIC_NOTIFICATION',
        EventApiContext.kafkaAdapter,
        EventApiContext.logger,
      );
    });
  });
});
