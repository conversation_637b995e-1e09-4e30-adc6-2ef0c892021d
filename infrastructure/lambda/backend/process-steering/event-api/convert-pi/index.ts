import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import {
  ConvertPiApiRequest,
  ConvertPiPayload,
  CorePayload,
  InboundEventDispatcherEvent,
  OneVmsEventKey,
} from '../../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { createApiGWHandlerWithInitLogger } from '../../../../utils/api-gw-handler';
import { sendFail } from '../../../../utils/api-helpers';
import { ApiHandlerError } from '../../../../utils/errors';
import { EventApiContext } from '../event-api-context';

const objectValidator = new ObjectValidator<ConvertPiApiRequest>('ConvertPiApiRequest');

EventApiContext.init(OneVmsEventKey.CONVERT_PI);

const convertPiFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  //validate path parameters
  const purchaseIntentionId = event.pathParameters ? event.pathParameters['purchaseIntentionId'] : undefined;
  if (!purchaseIntentionId) {
    const message = 'Missing purchaseIntentionId in path)';
    logger.log(LogLevel.WARN, message, { data: purchaseIntentionId });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }

  // Parse and validate request body
  let body_validated: ConvertPiApiRequest;
  let userAttributes: KasAuthEndpointResponse;
  try {
    const _res = EventApiContext.commonEventInputValidation(event, objectValidator);
    body_validated = _res.body_validated;
    userAttributes = _res.userAttributes;
  } catch (error) {
    if (error instanceof ApiHandlerError) {
      return sendFail(
        { message: error.message, status: error.statusCode, reqHeaders: event.headers },
        EventApiContext.logger,
      );
    } else {
      EventApiContext.logger.log(LogLevel.ERROR, 'Unexepected Error during validation', { data: error });
      throw error;
    }
  }

  // Create dispatcher event, send sqs and notification event
  const transformedRequestPayload: CorePayload = {
    payload: {
      ...body_validated,
      purchase_intention_id: purchaseIntentionId,
      cookie_header: event.headers.cookie ?? event.headers.Cookie,
    } satisfies ConvertPiPayload,
    event_type: OneVmsEventKey.CONVERT_PI,
    sub_transaction_id: uuidv4(),
  };

  const dispatcherEvent: InboundEventDispatcherEvent = EventApiContext.buildDispatcherEvent(
    transformedRequestPayload,
    userAttributes,
  );

  return await EventApiContext.handleDispatcherEvent(dispatcherEvent, event.headers);
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(EventApiContext.logger)(event, context, convertPiFunc);
