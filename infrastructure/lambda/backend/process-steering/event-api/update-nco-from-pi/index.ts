import { Kas<PERSON><PERSON><PERSON><PERSON>Log<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { KccNCOConfigWindowMessagePayload } from '../../../../../lib/types/new-car-order-types';
import {
  CorePayload,
  InboundEventDispatcherEvent,
  NcoInfo,
  OneVmsEventKey,
  UpdateNcoFromPiApiRequest,
} from '../../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { createApiGWHandlerWithInitLogger } from '../../../../utils/api-gw-handler';
import { sendFail } from '../../../../utils/api-helpers';
import { ApiHandlerError } from '../../../../utils/errors';
import { KccConfigSignatureVerifier } from '../../../../utils/kcc-config-signature-verifier/kcc-config-signature-verifier';
import { EventApiContext } from '../event-api-context';
import { v4 as uuidv4 } from 'uuid';

const objectValidator = new ObjectValidator<UpdateNcoFromPiApiRequest>('UpdateNcoFromPiApiRequest');

EventApiContext.init(OneVmsEventKey.UPDATE_NCO_FROM_PI);

const updateNcoFromPiFunc = async (
  event: APIGatewayProxyEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  //validate path parameters
  const purchaseIntentionId = event.pathParameters ? event.pathParameters['purchaseIntentionId'] : undefined;
  if (!purchaseIntentionId) {
    const message = 'Missing purchaseIntentionId in path)';
    logger.log(LogLevel.WARN, message, { data: purchaseIntentionId });
    return sendFail({ message: message, status: 400, reqHeaders: event.headers }, logger);
  }

  // Parse and validate request body
  let body_validated: UpdateNcoFromPiApiRequest;
  let userAttributes: KasAuthEndpointResponse;
  try {
    const _res = EventApiContext.commonEventInputValidation(event, objectValidator);
    body_validated = _res.body_validated;
    userAttributes = _res.userAttributes;
  } catch (error) {
    if (error instanceof ApiHandlerError) {
      return sendFail(
        { message: error.message, status: error.statusCode, reqHeaders: event.headers },
        EventApiContext.logger,
      );
    } else {
      EventApiContext.logger.log(LogLevel.ERROR, 'Unexpected Error during validation', { data: error });
      throw error;
    }
  }

  const { payload, nco_ids_with_modified_at } = body_validated;

  // Validate Config with signature and public key for all stages except dev
  if (EventApiContext.stage !== 'dev') {
    const cookieHeader = event.headers.cookie ?? event.headers.Cookie;
    const configurationSignature = payload.configuration_signature;

    const kccPayload: KccNCOConfigWindowMessagePayload = {
      kas: {
        model_info: {
          model_type: payload.model_type,
          model_year: parseInt(payload.model_year, 10),
          country_code: payload.cnr,
        },
        configuration: payload.configuration,
      },
      pvms: payload.configuration_expire,
    };

    const isValidSignature = await KccConfigSignatureVerifier.verifySignature(
      kccPayload,
      configurationSignature,
      cookieHeader,
      EventApiContext.stage,
      logger,
    );

    if (!isValidSignature) {
      const errMessage = 'KCC config signature validation was not successful';
      logger.log(LogLevel.WARN, errMessage, { data: kccPayload });
      return sendFail({ message: errMessage, status: 400, reqHeaders: event.headers }, logger);
    } else {
      logger.log(LogLevel.INFO, 'Great! KCC config has a valid signature and will be processed');
    }
  }

  const { pi_modified_at, ...payloadWithoutPiModifiedAt } = payload;
  const enhancedPayload = {
    ...payloadWithoutPiModifiedAt,
    piPayload: {
      purchase_intention_id: purchaseIntentionId,
      pi_modified_at: pi_modified_at,
    },
  };

  const ncosInfo: NcoInfo[] = nco_ids_with_modified_at.map((nco) => ({
    pk_new_car_order_id: nco.pk_new_car_order_id,
    modified_at: nco.modified_at,
    sub_transaction_id: uuidv4(),
  }));
  const transformedRequestPayload: CorePayload = {
    payload: enhancedPayload,
    ncos_info: ncosInfo,
    event_type: OneVmsEventKey.UPDATE_NCO,
  };

  const dispatcherEvent: InboundEventDispatcherEvent = EventApiContext.buildDispatcherEvent(
    transformedRequestPayload,
    userAttributes,
  );

  return await EventApiContext.handleDispatcherEvent(dispatcherEvent, event.headers);
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGWHandlerWithInitLogger(EventApiContext.logger)(event, context, updateNcoFromPiFunc);
