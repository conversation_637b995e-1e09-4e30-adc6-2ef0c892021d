import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getEnvVarWithAssert, scanAllFromTable } from '../../../../utils/utils';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { NcoExportActionType } from '../../../export-nco/types';
import {
  Constants,
  GRACE_PERIODS_QUOTA_DEALLOCATION_CUSTOMER_RELATED,
  GRACE_PERIODS_QUOTA_DEALLOCATION_NON_CUSTOMER_RELATED,
  OneVmsEventHandlerKey,
} from '../../../../../lib/utils/constants';
import {
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import {
  DefaultEventHandlerResult,
  InboundEventDispatcherEvent,
  InboundEventHandlerEventDeallocateQuota,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { CoraMdOrderType } from '../../../../../lib/types/masterdata-types';
import {
  ActionAfterCompletion,
  CreateScheduleCommand,
  CreateScheduleCommandInput,
  FlexibleTimeWindowMode,
  ListSchedulesCommand,
  ListSchedulesCommandInput,
  SchedulerClient,
  ScheduleSummary,
  UpdateScheduleCommand,
  UpdateScheduleCommandInput,
} from '@aws-sdk/client-scheduler';
import { EventHandlerContext } from '../event-handler-context';
import { EventHandlerEventSchedulerContext } from '../event-scheduler-context';
import { validateSqsEvent } from '../../../../utils/validation-helpers';
import { EventHandlerValidationContext } from '../validation-context';

type DeallocateQuotaWithMessageId = InboundEventHandlerEventDeallocateQuota & { messageId: string };
type DeallocateQuotaWithError = DeallocateQuotaWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventDeallocateQuota>(
  'InboundEventHandlerEventDeallocateQuota',
);

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const schedulerEBClient = new SchedulerClient({ region: process.env.AWS_REGION });
const dispatcherQueueArn = getEnvVarWithAssert('DISPATCHER_QUEUE_ARN');

// Table name environment variables
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');

const eventBridgeSchedulerRoleArn = getEnvVarWithAssert('EVENT_BRIDGE_SCHEDULER_ROLE_ARN');
const eventBridgeSchedulerKmsKeyArn = getEnvVarWithAssert('EVENT_BRIDGE_SCHEDULER_KMS_KEY_ARN');

EventHandlerContext.init(OneVmsEventHandlerKey.DEALLOCATE_QUOTA, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const deallocateQuotaFunc = async (
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  //Remember fails and successes
  const unParseableEvents: Partial<DeallocateQuotaWithError>[] = [];
  const expectedFailedEvents: DeallocateQuotaWithError[] = [];
  const unexpectedFailedEvents: DeallocateQuotaWithError[] = [];
  const successfulEvents: DeallocateQuotaWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  //Parse sqs events
  const deallocateQuotaEvents = event.Records.map((record) => {
    try {
      const deallocateQuotaEvent = JSON.parse(record.body) as InboundEventHandlerEventDeallocateQuota | undefined;

      //Check if sqs content is valid
      const isValid = validateSqsEvent<InboundEventHandlerEventDeallocateQuota>(
        deallocateQuotaEvent,
        sqsEventValidator,
        logger,
      );
      if (!isValid) {
        const message = 'SQS record is not valid, skipping';
        logger.log(LogLevel.ERROR, message, {
          data: deallocateQuotaEvent,
          correlationId: deallocateQuotaEvent?.transaction_id,
        });
        unParseableEvents.push({
          ...deallocateQuotaEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }
      return { ...deallocateQuotaEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventDeallocateQuota>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as DeallocateQuotaWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
    }
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      deallocateQuotaEvents.map((deallocateQuotaEvent) =>
        eventToNotification(deallocateQuotaEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: deallocateQuotaEvents.map((deallocateQuotaEvent) => ({
        itemIdentifier: deallocateQuotaEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      deallocateQuotaEvents.map((deallocateQuotaEvent) =>
        eventToNotification(deallocateQuotaEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: deallocateQuotaEvents.map((deallocateQuotaEvent) => ({
        itemIdentifier: deallocateQuotaEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  const updatedNcos: NewCarOrderModel[] = [];

  //Process all events one by one
  for (const deallocateQuotaEvent of deallocateQuotaEvents) {
    logger.setObjectId(deallocateQuotaEvent.nco_id);
    logger.setCorrelationId(deallocateQuotaEvent.transaction_id);
    const userAttributes = deallocateQuotaEvent.user_auth_context;

    // Extract necessary attributes from auth context
    const visibilityLevel =
      userAttributes.kasApplications[EventHandlerContext.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;

    if (!visibilityLevel) {
      const message = 'Failed to get the visibility level';
      logger.log(LogLevel.ERROR, message, { data: event });
      expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
      continue;
    }

    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: deallocateQuotaEvent.nco_id,
      });

      // Validate request according to business logic
      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + deallocateQuotaEvent.nco_id;
        logger.log(LogLevel.FATAL, message, { data: deallocateQuotaEvent });
        expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
        continue;
      }

      if (!sourceNco.quota_month) {
        const message = 'Order has no quota month/was deallocated already';
        logger.log(LogLevel.WARN, message, { data: sourceNco });
        expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
        continue;
      }

      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== deallocateQuotaEvent.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        EventHandlerContext.logger.log(LogLevel.WARN, message, { data: { sourceNco, deallocateQuotaEvent } });
        expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
        continue;
      }

      if (
        !(await EventHandlerValidationContext.authorizedForOrder(
          deallocateQuotaEvent.user_auth_context.organizationId,
          sourceNco,
        ))
      ) {
        const message = 'User is not authorized for provided dealer or importer number';
        EventHandlerContext.logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
        continue;
      }

      // Apply the deallocation with audit trail and kafka notification
      const res = await saveNcosWithAuditTrail(
        ncoDataSource,
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.DEALLOCATE_QUOTA,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              quota_month: null,
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: userAttributes.username,
              ...outboundStatusUpdateStatement,
            },
          );

          // Reload and return updated orders to ensure all orders were changed correctly
          return transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              modified_by: userAttributes.username,
            },
          });
        },
        logger,
        false,
      );

      logger.log(
        LogLevel.INFO,
        `Quota of NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was deallocated successfully.`,
      );
      updatedNcos.push(...res);
      successfulEvents.push(deallocateQuotaEvent);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during cancellation';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...deallocateQuotaEvent, errorMessage: message });
      continue;
    }
  }
  // Upsert cancellation schedules and enhance event with cancellation_date
  const enhancedSuccessfulEvents = await upsertCancellationSchedules(successfulEvents, updatedNcos, logger);

  //Export results into notification topic
  const successfulNotifications = enhancedSuccessfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(
      e,
      OneVmsEventKey.DEALLOCATE_QUOTA,
      NotificationStatus.EVENT_HANDLER_NIO,
      e.errorMessage,
    ),
  );

  // Unexpected fail events are not consumed and can be retried
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  // Alle Notifications zusammenführen
  const notificationEvents: NotificationKafkaEvent[] = [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

/**
 * Upserts cancellation schedules for the provided NCOs and returns the enhanced events
 * with an added cancellation_date in their payload.
 */
export async function upsertCancellationSchedules(
  events: DeallocateQuotaWithMessageId[],
  newCarOrders: NewCarOrderModel[],
  logger: KasLambdaLogger,
): Promise<DeallocateQuotaWithMessageId[]> {
  let orderTypes: CoraMdOrderType[];
  try {
    orderTypes = await scanAllFromTable<CoraMdOrderType>({ tableName: orderTypeTableName, dynamoDb }, logger);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error getting order types from db', { data: error });
    throw error;
  }

  const orderTypeMap: Record<string, string> = {};
  newCarOrders.forEach((nco) => {
    orderTypeMap[nco.pk_new_car_order_id] = nco.order_type;
  });

  let nextToken: string | undefined = undefined;
  const allSchedules: ScheduleSummary[] = [];
  do {
    const queryParams: ListSchedulesCommandInput = {
      NamePrefix: Constants.buildCancellationSchedulePrefix(EventHandlerContext.stage),
      NextToken: nextToken,
    };
    try {
      const schedules = await schedulerEBClient.send(new ListSchedulesCommand(queryParams));
      if (schedules.Schedules) allSchedules.push(...schedules.Schedules);
      nextToken = schedules.NextToken;
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Failed to query existing schedules', { data: error });
      throw error;
    }
  } while (nextToken);

  const allSchedulesLookup: Record<string, boolean> = {};
  allSchedules.forEach((schedule) => {
    if (schedule.Name) {
      allSchedulesLookup[schedule.Name] = true;
    }
  });
  const gracePeriodConfigCr = GRACE_PERIODS_QUOTA_DEALLOCATION_CUSTOMER_RELATED[EventHandlerContext.stage];
  const gracePeriodConfigNonCr = GRACE_PERIODS_QUOTA_DEALLOCATION_NON_CUSTOMER_RELATED[EventHandlerContext.stage];

  const schedulePromises = newCarOrders.map(async (nco) => {
    const corrOrderType = orderTypes.find((ot) => ot.pk_order_type === nco.order_type);
    if (!corrOrderType) {
      logger.log(LogLevel.ERROR, 'No order type found for nco', { data: nco });
      throw new Error('No order type found for nco');
    }
    const gracePeriodConfig = corrOrderType.is_customer_related ? gracePeriodConfigCr : gracePeriodConfigNonCr;
    const cancellationDate = EventHandlerEventSchedulerContext.getEventBridgeAtDate(gracePeriodConfig);

    const corrEvent = events.find((e) => e.nco_id === nco.pk_new_car_order_id);
    if (!corrEvent) {
      logger.log(LogLevel.ERROR, 'No corresponding event for nco; cannot create schedule', {
        data: nco,
      });
      throw new Error('No corresponding event for nco; cannot create schedule');
    }

    const scheduleName = Constants.buildCancellationScheduleName(EventHandlerContext.stage, nco.pk_new_car_order_id);
    const ncosInfo = [
      {
        pk_new_car_order_id: nco.pk_new_car_order_id,
        modified_at: nco.modified_at!,
        sub_transaction_id: uuidv4(),
      },
    ];

    const scheduleInput: UpdateScheduleCommandInput | CreateScheduleCommandInput = {
      Name: scheduleName,
      ScheduleExpression: `at(${cancellationDate})`,
      Target: {
        Arn: dispatcherQueueArn,
        RoleArn: eventBridgeSchedulerRoleArn,
        Input: JSON.stringify({
          event_type: OneVmsEventKey.CANCEL_SCHEDULED,
          transaction_id: corrEvent.transaction_id, // New Transaction id
          ncos_info: ncosInfo,
          action_at: cancellationDate,
          source_system: OneVmsSourceSystemKey.CORA_SYSTEM,
          payload: { cancellationDate },
        } satisfies InboundEventDispatcherEvent),
      },
      FlexibleTimeWindow: {
        Mode: gracePeriodConfig.flexible_window_min ? FlexibleTimeWindowMode.FLEXIBLE : FlexibleTimeWindowMode.OFF,
        MaximumWindowInMinutes: gracePeriodConfig.flexible_window_min,
      },
      KmsKeyArn: eventBridgeSchedulerKmsKeyArn,
      ActionAfterCompletion: ActionAfterCompletion.DELETE,
    };

    const isExistingSchedule = allSchedulesLookup[scheduleName];
    logger.log(LogLevel.DEBUG, `Schedule ${scheduleName} exists: ${!!isExistingSchedule}`);
    try {
      await schedulerEBClient.send(
        isExistingSchedule ? new UpdateScheduleCommand(scheduleInput) : new CreateScheduleCommand(scheduleInput),
      );
      logger.log(LogLevel.DEBUG, `Upserted schedule for nco ${nco.pk_new_car_order_id}`, {
        data: scheduleName,
      });
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Error upserting schedule', { data: error });
      throw error;
    }
  });

  await Promise.all(schedulePromises);

  const enhancedEvents = events.map((e) => {
    const corrOrderType = orderTypes.find((ot) => ot.pk_order_type === orderTypeMap[e.nco_id]);
    const gracePeriodConfig = corrOrderType?.is_customer_related ? gracePeriodConfigCr : gracePeriodConfigNonCr;
    return {
      ...e,
      payload: {
        cancellation_date: EventHandlerEventSchedulerContext.getEventBridgeAtDate(gracePeriodConfig),
      },
    };
  });

  return enhancedEvents;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, deallocateQuotaFunc);
