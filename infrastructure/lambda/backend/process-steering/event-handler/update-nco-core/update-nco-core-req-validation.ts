import { Ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { NewCarOrderModel } from '../../../../../lib/entities/new-car-order-model';
import {
  getAllAuthorizedOrgs,
  getPermissionForDealer,
  NcoValidationTables,
  validateOrderType,
  validateShippingCode,
} from '../../../../utils/validation-helpers';
import { BatchGetCommand, BatchGetCommandInput, GetCommand } from '@aws-sdk/lib-dynamodb';
import { UpdateNCOCoreDataValidationError } from '../../../../../lib/types/custom-error-types';
import { CoraMdOrderType, CoraMdShippingCode } from '../../../../../lib/types/masterdata-types';
import { CoraOrgRelModel } from '../../../../../lib/types/boss-org-types';
import { UpdateNcoCorePayload } from '../../../../../lib/types/process-steering-types';

export interface NcoUpdateCoreDataValidationParams {
  payload: UpdateNcoCorePayload;
  dynamoDb: DynamoDBClient;
  sourceNco: NewCarOrderModel;
  ppnId: string;
  tables: NcoValidationTables;
}

export const validateUpdateCoreDataRequest = async (
  props: NcoUpdateCoreDataValidationParams,
  logger: KasLambdaLogger,
): Promise<{ valid: boolean; error?: string; errorCode?: number }> => {
  try {
    //validate importer and dealer number and find out users role
    const authorizedOrgs = await getAllAuthorizedOrgs(
      {
        dynamoDb: props.dynamoDb,
        orgRelsTableName: props.tables.coraOrgRelationTableName,
        ppnId: props.ppnId,
      },
      logger,
    );

    logger.log(LogLevel.DEBUG, 'Validating update core data request according to business logic and rules');

    //Get Ordertype data
    let oldOrderType: CoraMdOrderType | undefined;
    let newOrderType: CoraMdOrderType | undefined;

    // if(props.payload.order_type === props.sourceNco.order_type) {
    //   const errMessage = `Old and new ordertypes are the same: ${props.sourceNco.order_type} -> ${props.payload.order_type}`;
    //   throw new UpdateNCOCoreDataValidationError(errMessage, 400);
    // }
    // Check needed because otherwise dynamodb will error here otherwise (no need to update anything)
    if (props.payload.order_type && props.payload.order_type !== props.sourceNco.order_type) {
      const batchGetParams: BatchGetCommandInput = {
        RequestItems: {
          [props.tables.orderTypeTableName]: {
            Keys: [{ pk_order_type: props.sourceNco.order_type }, { pk_order_type: props.payload.order_type }],
          },
        },
      };

      // Execute batch request and validate.
      const batchResponse = await props.dynamoDb.send(new BatchGetCommand(batchGetParams));
      const orderTypes = (batchResponse.Responses?.[props.tables.orderTypeTableName] ?? []) as CoraMdOrderType[];

      // Validate if old and new order types exist
      oldOrderType = orderTypes.find((ot) => ot.pk_order_type === props.sourceNco.order_type);
      newOrderType = orderTypes.find((ot) => ot.pk_order_type === props.payload.order_type);
      if (!newOrderType || !oldOrderType) {
        const errMessage = `Could not find ${oldOrderType ? 'old' : 'new'} ordertype: ${props.sourceNco.order_type} -> ${props.payload.order_type}`;
        throw new UpdateNCOCoreDataValidationError(errMessage, 404);
      }
    }

    // Get ShippingCode data
    let shippingCode: CoraMdShippingCode | undefined;
    if (props.payload.shipping_code) {
      shippingCode = (
        await props.dynamoDb.send(
          new GetCommand({
            Key: { pk_shipping_code: props.payload.shipping_code },
            TableName: props.tables.shippingCodeTableName,
          }),
        )
      ).Item as CoraMdShippingCode | undefined;

      if (!shippingCode) {
        const errMessage = `Shipping code not found: ${props.payload.shipping_code}`;
        throw new UpdateNCOCoreDataValidationError(errMessage, 404);
      }
    }

    //validate existence of corresponding masterdata
    const res = performValidation(
      {
        dynamoDb: props.dynamoDb,
        authorizedOrgs,
        tables: props.tables,
        sourceNco: props.sourceNco,
        oldOrderType,
        newOrderType,
        shippingCode,
      },
      logger,
    );
    logger.log(LogLevel.INFO, 'validateUpdateCoreDataRequest output', { data: res });
    return res;
  } catch (err) {
    const res = { valid: false, error: err as string };
    return res;
  }
};

export const performValidation = (
  props: {
    dynamoDb: DynamoDBClient;
    authorizedOrgs: CoraOrgRelModel[];
    tables: NcoValidationTables;
    shippingCode: CoraMdShippingCode | undefined;
    oldOrderType: CoraMdOrderType | undefined;
    newOrderType: CoraMdOrderType | undefined;
    sourceNco: NewCarOrderModel;
  },
  logger: KasLambdaLogger,
): { valid: boolean; error?: string; errorCode?: number } => {
  let errMessage: string;
  try {
    //find org that matches dealer and importer number to the provided order
    const correspondingDealerOrg = props.authorizedOrgs.find(
      (org) =>
        org.dealer_number === props.sourceNco.dealer_number && org.importer_number === props.sourceNco.importer_number,
    );
    if (!correspondingDealerOrg) {
      throw new UpdateNCOCoreDataValidationError('User is not authorized for provided dealer or importer number', 403);
    }

    // Validate order type change
    if (props.newOrderType && props.oldOrderType) {
      // using ?? does not work here for some reason!
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      if (props.oldOrderType.is_customer_related || props.newOrderType.is_customer_related) {
        errMessage = 'Operation not allowed: One of the order types is customer-related.';
        throw new UpdateNCOCoreDataValidationError(errMessage, 422);
      }

      //get role for user
      const isUserImp =
        getPermissionForDealer(
          {
            dealerNumber: props.sourceNco.dealer_number,
            authorizedOrgs: props.authorizedOrgs,
          },
          logger,
        ) === 'Importer';
      // If custom OT validation is successful, pass through to common OT validation
      const res = validateOrderType({
        isUserImp: isUserImp,
        skipOtDisabledCheck: false,
        orderType: props.newOrderType,
        ncoImporterNumber: props.sourceNco.importer_number,
        ncoDealerNumber: props.sourceNco.dealer_number,
      });

      if (!res.valid) {
        throw new UpdateNCOCoreDataValidationError(res.error ?? 'Order type validation failed', 400);
      }
    }

    // Here new validation logic for shipping codes, port codes etc. can be implemented
    if (props.shippingCode) {
      //get role for user
      const isUserImp =
        getPermissionForDealer(
          {
            dealerNumber: props.sourceNco.dealer_number,
            authorizedOrgs: props.authorizedOrgs,
          },
          logger,
        ) === 'Importer';
      // If custom SC validation is successful, pass through to common SC validation
      const res = validateShippingCode({
        isUserImp,
        skipScDisabledCheck: false,
        shippingCode: props.shippingCode,
        ncoImporterNumber: props.sourceNco.importer_number,
        ncoDealerNumber: props.sourceNco.dealer_number,
      });
      if (!res.valid) {
        throw new UpdateNCOCoreDataValidationError(res.error ?? 'Shipping code  validation failed', 400);
      }
    }

    return { valid: true };
  } catch (error) {
    if (error instanceof UpdateNCOCoreDataValidationError) {
      return { valid: false, error: error.message, errorCode: error.errorCode };
    }
    logger.log(LogLevel.ERROR, 'Unexpected error occured during validation', { data: error });
    throw error;
  }
};
