import {
  initDataSourceForIntTest,
  prepareDynamodb,
  cleanupDynamodb,
  buildLambdaArn,
  createStandartOrgRelPattern,
  createSqsEvent,
  invokeGenericLambda,
  pollNotifications,
  createInboundEvent,
} from '../../../../utils/integration-test-helpers';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { DataSource, In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraNCOBaseApiRequest } from '../../../../../lib/types/new-car-order-types';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { ncoApiToDbObj } from '../../../../utils/utils-typeorm';
import { CoraMdDealer, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import {
  InboundEventHandlerEventUpdateNcoCore,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
  SQSBatchResponseWithError,
  UpdateNcoCoreHandlerResult,
  UpdateNcoCorePayload,
} from '../../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.UPDATE_NCO_CORE}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('update-nco-core');

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;

//auth context apps test data
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

//existing nco test objects
const oldCarOrderObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITUpdDt01',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otNoCustomerRel.pk_order_type,
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scBlacklistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoUpdDataPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  order_status_onevms_timestamp_last_change: '2024-12-06T10:21:02.876Z',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: {
    created_by: 'IntegrationTester',
    modified_by: 'IntegrationTester',
    ordered_options: [
      {
        created_by: 'IntegrationTester',
        modified_by: 'IntegrationTester',
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            created_by: 'IntegrationTester',
            modified_by: 'IntegrationTester',
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
    technical_options: null,
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const oldCarOrderCustomerRelatedObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITUpdDt02',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdImporter.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoUpdPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  order_status_onevms_timestamp_last_change: '2024-12-06T10:21:02.876Z',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

//default update core data request obj
const defaultUpdateCoreDataReqObject: UpdateNcoCorePayload = { order_type: ots.otWhitelistAllow.pk_order_type };
//default update core data sqs event
const defaultUpdateCoreDataSqsEvent: InboundEventHandlerEventUpdateNcoCore = {
  event_type: OneVmsEventKey.UPDATE_CORE_DATA,
  transaction_id: uuidv4(),
  sub_transaction_id: uuidv4(),
  action_at: new Date().toISOString(),
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  nco_id: oldCarOrderObject.pk_new_car_order_id,
  modified_at: oldCarOrderObject.modified_at!,
  payload: defaultUpdateCoreDataReqObject,
  source_system: OneVmsSourceSystemKey.CORA_USER,
} satisfies InboundEventHandlerEventUpdateNcoCore;

let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;

function deepcopyNcoEntity(oldNco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(oldNco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', oldNco);
}

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    OutboundProcessMappingModel,
  ]);
  ncoRepo = dataSource.getRepository(NewCarOrderModel);

  //save nco test objects
  const oldCarOrderObjectRes = await ncoRepo.save(deepcopyNcoEntity(oldCarOrderObject));
  const oldCarOrderCustomerRelatedObjectRes = await ncoRepo.save(deepcopyNcoEntity(oldCarOrderCustomerRelatedObject));

  //update modified_at fields because valid values are needed in request body
  oldCarOrderObject.modified_at = oldCarOrderObjectRes.modified_at;
  defaultUpdateCoreDataSqsEvent.modified_at = oldCarOrderObjectRes.modified_at!;
  oldCarOrderCustomerRelatedObject.modified_at = oldCarOrderCustomerRelatedObjectRes.modified_at;

  //save outbound status mapping
  outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
    source_event_handler: OneVmsEventHandlerKey.UPDATE_NCO_CORE,
    event_result: UpdateNcoCoreHandlerResult.SUCCESS,
  });

  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    {
      tableName: mdDlrTableName,
      objs: [ids.dealer01.md_org],
    },
    { tableName: mdImpTableName, objs: [ids.importer.md_org] },
    { tableName: mdScTableName, objs: Object.values(scs) },
    { tableName: mdOtTableName, objs: Object.values(ots) },
  ]);
}, 120000);

afterAll(async () => {
  //delete nco test objects
  await ncoRepo.delete({
    pk_new_car_order_id: In([
      oldCarOrderObject.pk_new_car_order_id,
      oldCarOrderCustomerRelatedObject.pk_new_car_order_id,
    ]),
  });

  //delete audit trails
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: In([
      oldCarOrderObject.pk_new_car_order_id,
      oldCarOrderCustomerRelatedObject.pk_new_car_order_id,
    ]),
  });

  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDlrTableName,
      pks: [
        {
          pk_importer_number: mdDealer.pk_importer_number,
          sk_dealer_number: mdDealer.sk_dealer_number,
        },
      ],
    },
    {
      tableName: mdImpTableName,
      pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
    },
    {
      tableName: mdScTableName,
      pks: scPks,
    },
    {
      tableName: mdOtTableName,
      pks: otPks,
    },
  ]);
}, 120000);

it('Should NOT update nco and return fail if input is missing required props', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('SQS record is not valid');
});

it('Should NOT update nco and return fail if auth context is faulty', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      user_auth_context: {
        ...eventPayload.user_auth_context,
        kasApplications: appsWithNoRole,
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to get the visibility level');

  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update nco and return fail if nco cannot be found', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      nco_id: 'NOT_VALID_NCOID',
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to find order with id');

  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update nco and return fail if modified_at does not match', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('changed by someone else');

  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update nco and return fail if changing from customer related ot', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: oldCarOrderCustomerRelatedObject.modified_at!,
      nco_id: oldCarOrderCustomerRelatedObject.pk_new_car_order_id,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Operation not allowed: One of the order types is customer-related');

  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update nco and return fail if changing to customer related ot', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: { order_type: ots.otCustomerRel.pk_order_type },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Operation not allowed: One of the order types is customer-related');

  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should update nco and return no fails ', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  const event = createSqsEvent([eventPayload]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(outboundEventMapping!.order_status_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(SpecialStatusCode.NONE);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(defaultUpdateCoreDataSqsEvent.payload.order_type);
});

it('Should update nco while keeping status and return no fails if no outbound mapping is found', async () => {
  const eventPayload = createInboundEvent(defaultUpdateCoreDataSqsEvent);
  //delete outbound mapping first
  await dataSource.getRepository(OutboundProcessMappingModel).update(
    {
      id: outboundEventMapping!.id,
    },
    { event_result: 'SomeResultWhichIsNotSuccess' },
  );
  //reset nco to initial state
  await ncoRepo.delete({
    pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id,
  });
  const oldCarOrderObjectRes = await ncoRepo.save(deepcopyNcoEntity(oldCarOrderObject));
  oldCarOrderObject.modified_at = oldCarOrderObjectRes.modified_at;

  const event = createSqsEvent([eventPayload]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(defaultUpdateCoreDataSqsEvent.payload.order_type);
  await dataSource.getRepository(OutboundProcessMappingModel).update(
    {
      id: outboundEventMapping!.id,
    },
    { event_result: UpdateNcoCoreHandlerResult.SUCCESS },
  );
});
