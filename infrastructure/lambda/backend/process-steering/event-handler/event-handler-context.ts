import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Ka<PERSON><PERSON><PERSON>b<PERSON>Log<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Class } from 'type-fest';
import { DataSource } from 'typeorm';
import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
import { OutboundProcessMappingModel } from '../../../../lib/entities/outbound-mapping-model';
import {
  InboundEventHandlerEvent,
  InboundEventHandlerUnparsableEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
} from '../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../lib/utils/constants';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { KafkaAdapter } from '../../../utils/kafka';
import { secretCache } from '../../../utils/secret-cache';
import { getEnvVarWithAssert, pushNotificationsToKafka } from '../../../utils/utils';
import { KasStage } from '@kas-resources/constructs';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { SQSEvent } from 'aws-lambda';
import { getStatusUpdateStatementsFromOutboundMapping } from '../../../utils/utils-typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { eventToNotification, unparseableEventToNotification } from '../../../utils/process-steering-helpers';
import { getAllAuthorizedOrgs } from '../../../utils/validation-helpers';

export interface CommonBusinessValidationResult {
  valid: boolean;
  message?: string;
}

export class EventHandlerContext {
  public static readonly applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
  public static readonly auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
  public static readonly kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
  public static readonly KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
  public static readonly KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
  public static readonly stage = getEnvVarWithAssert('STAGE') as KasStage;

  public static dynamoDb: DynamoDBClient;
  public static eventHandlerKey: OneVmsEventHandlerKey;
  public static logger: KasLambdaLogger;
  public static kafkaAdapter: KafkaAdapter;
  public static dbModelEntities: Class[];

  private static dataSource: DataSource | undefined;
  private static outboundEventMappings: OutboundProcessMappingModel[] | undefined;

  // No instances (Max 1 per Lambda, so everything here is static)
  private constructor() {}
  /**
   *
   * @param dbModelEntities Add Entities like NewCarOrderModel etc.; OutboundStatusMapping is added by the Context automatically
   * @param additionalSecretArn Provide optional, additional secret ARN if needed in secret cache.
   */
  public static init(
    eventHandlerKey: OneVmsEventHandlerKey,
    dbModelEntities: Class[],
    additionalSecretArn?: string,
  ): void {
    this.logger = new KasLambdaLogger(eventHandlerKey + '-event-handler', LogLevel.TRACE);
    this.logger.log(LogLevel.TRACE, 'Initializing EventHandlerContext', { data: eventHandlerKey });
    this.eventHandlerKey = eventHandlerKey;
    secretCache.initCache(this.auroraSecretArn, this.kafkaSecretArn, additionalSecretArn ?? '');
    this.kafkaAdapter = new KafkaAdapter({
      kafka_brokers: this.KAFKA_BROKERS,
      kafka_secret_arn: this.kafkaSecretArn,
      logger: this.logger,
    });
    this.dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
    this.dbModelEntities = [...dbModelEntities, OutboundProcessMappingModel];
  }

  public static async pushNotificationsToKafka(events: NotificationKafkaEvent[]): Promise<void> {
    return pushNotificationsToKafka(events, this.KAFKA_TOPIC_NOTIFICATION, this.kafkaAdapter, this.logger);
  }

  public static async getDataSource(): Promise<DataSource> {
    if (!this.dataSource?.isInitialized) {
      this.dataSource = await createTypeORMDataSource(
        this.logger,
        this.auroraSecretArn,
        this.stage,
        this.dbModelEntities,
      );
      // createTypeORMDataSource already does initialize()
      // await this.dataSource.initialize();
    }
    return this.dataSource;
  }

  public static async reconnectDataSource(): Promise<DataSource> {
    if (this.dataSource?.isInitialized) {
      await this.dataSource.destroy();
    }
    return this.getDataSource();
  }

  public static async getOutboundEventMappings(refreshCache?: boolean): Promise<OutboundProcessMappingModel[]> {
    if (!this.outboundEventMappings || refreshCache) {
      const outboundEventMappingRepo = (await EventHandlerContext.getDataSource()).getRepository(
        OutboundProcessMappingModel,
      );

      this.outboundEventMappings = await outboundEventMappingRepo.findBy({
        source_event_handler: this.eventHandlerKey,
      });
    }
    this.logger.log(LogLevel.INFO, 'Outbound Event Mappings', { data: this.outboundEventMappings });
    return this.outboundEventMappings;
  }

  public static async getOutboundStatusUpdateStatement(
    eventResult: string,
    refreshCache?: boolean,
  ): Promise<QueryDeepPartialEntity<NewCarOrderModel>> {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings(refreshCache);
    const outboundEventMapping = outboundEventMappings.find((mapping) => mapping.event_result === eventResult);
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      return getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
    }
    this.logger.log(LogLevel.DEBUG, 'No outbound event mapping for', { data: eventResult });
    return {};
  }

  public static commonEventInputValidation<T extends InboundEventHandlerEvent>(
    event: SQSEvent,
    sqsEventValidator: ObjectValidator<T>,
  ): { unParseableEvents: InboundEventHandlerUnparsableEvent[]; parsedEvents: (T & { messageId: string })[] } {
    const unParseableEvents: InboundEventHandlerUnparsableEvent[] = [];
    const parsedEvents: (T & { messageId: string })[] = [];
    for (const record of event.Records) {
      let body: InboundEventHandlerEvent;
      try {
        body = JSON.parse(record.body) as InboundEventHandlerEvent;
      } catch (error) {
        this.logger.log(LogLevel.FATAL, 'Invalid JSON in Eventhandler Input, skipping record!', {
          data: { record: { body: record.body, messageId: record.messageId } },
        });
        continue;
      }
      this.logger.setCorrelationId(body.sub_transaction_id);
      const [validated_record, validation_errors] = sqsEventValidator.validate(body);
      if (validated_record === null) {
        this.logger.log(LogLevel.ERROR, 'ajv validation failed', {
          data: { validation_errors, record: { body: record.body, messageId: record.messageId } },
        });
        unParseableEvents.push({
          ...body,
          messageId: record.messageId,
          errorMessage: 'Parsing error: ajv validation failed',
        });
      } else {
        parsedEvents.push({ ...validated_record, messageId: record.messageId });
      }
    }
    return { unParseableEvents, parsedEvents };
  }

  public static async sendNotifications(props: {
    successfulEvents: InboundEventHandlerEvent[];
    expectedFailedEvents: InboundEventHandlerEvent[];
    unParseableEvents: InboundEventHandlerUnparsableEvent[];
    unexpectedFailedEvents: InboundEventHandlerUnparsableEvent[];
    eventKey: OneVmsEventKey;
    defaultActionBy?: string;
  }): Promise<SQSBatchResponseWithError> {
    const unparseableNotifications = props.unParseableEvents.map((e) =>
      unparseableEventToNotification(e, props.eventKey, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
    );

    // Unexpected fail events are not consumed and can be retried
    const unexpectedNotifications = props.unexpectedFailedEvents.map((e) => ({
      transaction_id: e.transaction_id,
      sub_transaction_id: e.sub_transaction_id,
      event_type: e.event_type,
      nco_id: e.nco_id,
      action_by: e.user_auth_context?.username ?? props.defaultActionBy ?? 'unknown',
      action_at: new Date().toISOString(),
      source_system: e.source_system,
      status: NotificationStatus.EVENT_HANDLER_NIO,
      details: e.errorMessage,
    }));

    // Unexpected fail events are not consumed and can be retried
    const expectedFailedNotifications = props.expectedFailedEvents.map((e) => ({
      transaction_id: e.transaction_id,
      sub_transaction_id: e.sub_transaction_id,
      event_type: e.event_type,
      nco_id: e.nco_id,
      action_by: e.user_auth_context?.username ?? props.defaultActionBy ?? 'unknown',
      action_at: new Date().toISOString(),
      source_system: e.source_system,
      status: NotificationStatus.EVENT_HANDLER_NIO,
      details: e.payload,
    }));

    // Unexpected fail events are not consumed and can be retried
    const successfulNotifications = props.successfulEvents.map((e) => ({
      transaction_id: e.transaction_id,
      sub_transaction_id: e.sub_transaction_id,
      event_type: e.event_type,
      nco_id: e.nco_id,
      action_by: e.user_auth_context?.username ?? props.defaultActionBy ?? 'unknown',
      action_at: new Date().toISOString(),
      source_system: e.source_system,
      status: NotificationStatus.EVENT_HANDLER_IO,
      details: e.payload,
    }));

    //Push notifications to kafka
    try {
      await EventHandlerContext.pushNotificationsToKafka([
        ...successfulNotifications,
        ...expectedFailedNotifications,
        ...unparseableNotifications,
        ...unexpectedNotifications,
      ]);
    } catch (e) {
      this.logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
    }

    //Return fails so that events are put into dlq
    return {
      batchItemFailures: props.unexpectedFailedEvents.map((e) => ({
        itemIdentifier: e.messageId,
        errorMessage: e.errorMessage,
      })),
    } satisfies SQSBatchResponseWithError;
  }
  public static async commonBusinessLogicValidation(
    eventParams: { nco_id: string; modified_at: string },
    sourceNco: NewCarOrderModel,
    ppnId?: string,
  ): Promise<CommonBusinessValidationResult> {
    if (new Date(sourceNco.modified_at ?? 0).toISOString() !== eventParams.modified_at) {
      const message = 'Nco was changed by someone else since the event was created';
      this.logger.log(LogLevel.WARN, message, { data: { sourceNco, eventParams } });
      return { valid: false, message };
    }

    if (ppnId) {
      const authorizedOrgs = await getAllAuthorizedOrgs(
        {
          dynamoDb: this.dynamoDb,
          orgRelsTableName: getEnvVarWithAssert('TABLE_NAME_ORG_RELS'),
          ppnId,
        },
        this.logger,
      );

      const correspondingDealerOrg = authorizedOrgs.find(
        (org) => org.dealer_number === sourceNco.dealer_number && org.importer_number === sourceNco.importer_number,
      );
      if (!correspondingDealerOrg) {
        const message = 'User is not authorized for provided dealer or importer number';
        this.logger.log(LogLevel.WARN, message);
        return { valid: false, message };
      }
    }

    return { valid: true };
  }
}
