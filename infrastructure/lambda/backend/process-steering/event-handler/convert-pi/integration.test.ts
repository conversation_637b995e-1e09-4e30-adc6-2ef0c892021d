import { DataSource, In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import {
  DefaultEventHandlerResult,
  InboundEventHandlerEventConvertPi,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createInboundEvent,
  createSqsEvent,
  createStandartOrgRelPattern,
  initDataSourceForIntTest,
  invokeGenericLambda,
  pollNotifications,
  prepareDynamodb,
} from '../../../../utils/integration-test-helpers';
import { NcoExportActionType } from '../../../export-nco/types';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.CONVERT_PI}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('convert-pi');

// Create MTV test data
const mtvs = [
  {
    importer_number: ids.importer.md_org.pk_importer_number,
    cnr: 'C23',
    model_type: 'MT0001',
    my4: '2030',
    role: 'IMP',
    valid_from: '2017-03-14',
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
  {
    importer_number: ids.importer.md_org.pk_importer_number,
    cnr: 'C23',
    model_type: 'MT0002',
    my4: '2030',
    role: 'IMP',
    valid_from: '5000-03-14', // Future date for invalid test
    created_by: 'IntTest',
    modified_by: 'IntTest',
  },
];

const purchaseIntentionPvms: CoraPurchaseIntentionModel = {
  purchase_intention_id: 'ITConvPI01',
  dealer_number: ids.dealer01.md_org.sk_dealer_number,
  importer_code: ids.importer.md_org.code,
  importer_number: ids.importer.md_org.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  quota_month: '2023-11',
  shipping_code: scs.scBlacklistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc1',
  created_by: OneVmsSourceSystemKey.PVMS,
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  vehicle_status_code: 'V070',
  vehicle_configuration_pvmsnext: { testProp: 'Test' },
  vehicle_configuration_onevms: null,
  seller: '123456',
  business_partner_id: '0020000866',
};

const purchaseIntentionCss: CoraPurchaseIntentionModel = {
  purchase_intention_id: 'ITConvPI02',
  dealer_number: ids.dealer01.md_org.sk_dealer_number,
  importer_code: ids.importer.md_org.code,
  importer_number: ids.importer.md_org.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  quota_month: '2023-11',
  shipping_code: null,
  receiving_port_code: 'ItNcoPc1',
  created_by: OneVmsSourceSystemKey.CSS,
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  vehicle_status_code: 'V070',
  vehicle_configuration_pvmsnext: null,
  vehicle_configuration_onevms: { ordered_options: [{ option_id: 'option1' }] },
  seller: '123456',
  business_partner_id: '0020000866',
};

// Auth context test data
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

// Default convert PI SQS event
const defaultConvertPiSqsEvent: InboundEventHandlerEventConvertPi = {
  event_type: OneVmsEventKey.CONVERT_PI,
  transaction_id: uuidv4(),
  sub_transaction_id: uuidv4(),
  action_at: new Date().toISOString(),
  source_system: OneVmsSourceSystemKey.CORA_USER,
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  payload: {
    purchase_intention_id: purchaseIntentionPvms.purchase_intention_id,
    model_type: purchaseIntentionPvms.model_type,
    model_year: purchaseIntentionPvms.model_year,
    cnr: purchaseIntentionPvms.cnr,
    quota_month: purchaseIntentionPvms.quota_month,
    pk_new_car_order_id: purchaseIntentionPvms.purchase_intention_id,
    dealer_number: purchaseIntentionPvms.dealer_number,
    importer_code: purchaseIntentionPvms.importer_code,
    importer_number: purchaseIntentionPvms.importer_number,
    order_type: purchaseIntentionPvms.order_type,
    shipping_code: purchaseIntentionPvms.shipping_code!,
    receiving_port_code: purchaseIntentionPvms.receiving_port_code,
    deal_id: purchaseIntentionPvms.purchase_intention_id,
    configuration: { ordered_options: [] },
    configuration_expire: null,
    configuration_signature: 'mydummysignature',
    cookie_header: 'dummy-cookie',
    requested_dealer_delivery_date: '2024-01-01',
    modified_at: new Date().toISOString(),
  },
};

let dataSource: DataSource;
let piRepo: Repository<CoraPurchaseIntentionModel>;
let ncoRepo: Repository<NewCarOrderModel>;
let mtvRepo: Repository<ModelTypeVisibilityModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;
let savedMtvs: ModelTypeVisibilityModel[];
let cssPiModifiedAt: string;
const createdNcoIds = [purchaseIntentionPvms.purchase_intention_id];

describe('Convert PI Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
      OutboundProcessMappingModel,
      CoraPurchaseIntentionModel,
      ModelTypeVisibilityModel,
    ]);

    piRepo = dataSource.getRepository(CoraPurchaseIntentionModel);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);
    mtvRepo = dataSource.getRepository(ModelTypeVisibilityModel);

    // Save test data
    const piSaveRes = await piRepo.save([purchaseIntentionPvms, purchaseIntentionCss]);
    savedMtvs = await mtvRepo.save(mtvs);

    //update modified_at field because valid values are needed in request body
    defaultConvertPiSqsEvent.payload.modified_at = piSaveRes[0].modified_at!;
    cssPiModifiedAt = piSaveRes[1].modified_at!;

    // Get outbound mapping
    outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
      source_event_handler: OneVmsEventHandlerKey.CONVERT_PI,
      event_result: DefaultEventHandlerResult.SUCCESS,
    });

    await prepareDynamodb([
      { tableName: orgRelTableName, objs: orgRels },
      { tableName: mdDlrTableName, objs: [ids.dealer01.md_org] },
      { tableName: mdImpTableName, objs: [ids.importer.md_org] },
      { tableName: mdScTableName, objs: Object.values(scs) },
      { tableName: mdOtTableName, objs: Object.values(ots) },
    ]);
  }, 120000);

  afterAll(async () => {
    // Cleanup test data
    await piRepo.delete({
      purchase_intention_id: In([
        purchaseIntentionPvms.purchase_intention_id,
        purchaseIntentionCss.purchase_intention_id,
      ]),
    });
    await ncoRepo.delete({ pk_new_car_order_id: In(createdNcoIds) });
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
      pk_new_car_order_id: In(createdNcoIds),
    });
    await mtvRepo.remove(savedMtvs);
    await dataSource.destroy();

    await cleanupDynamodb([
      {
        tableName: orgRelTableName,
        pks: orgRels.map((org) => ({ pk_ppn_id: org.pk_ppn_id, parent_ppn_id: org.parent_ppn_id })),
      },
      {
        tableName: mdDlrTableName,
        pks: [
          {
            pk_importer_number: ids.dealer01.md_org.pk_importer_number,
            sk_dealer_number: ids.dealer01.md_org.sk_dealer_number,
          },
        ],
      },
      { tableName: mdImpTableName, pks: [{ pk_importer_number: ids.importer.md_org.pk_importer_number }] },
      { tableName: mdScTableName, pks: Object.values(scs).map((sc) => ({ pk_shipping_code: sc.pk_shipping_code })) },
      { tableName: mdOtTableName, pks: Object.values(ots).map((ot) => ({ pk_order_type: ot.pk_order_type })) },
    ]);
  }, 120000);

  it('Should NOT convert PI and return fail if input is missing required props', async () => {
    const eventPayload = createInboundEvent(defaultConvertPiSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        payload: {
          ...defaultConvertPiSqsEvent.payload,
          configuration: undefined,
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('SQS record is not valid');
  }, 30000);

  it('Should NOT convert PI and return fail if auth context is faulty', async () => {
    const eventPayload = createInboundEvent(defaultConvertPiSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        user_auth_context: {
          ...eventPayload.user_auth_context,
          kasApplications: appsWithNoRole,
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to get visibility level or ppnId from auth context');
  }, 30000);

  it('Should NOT convert PI and return fail if PI does not exist', async () => {
    const eventPayload = createInboundEvent(defaultConvertPiSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        payload: {
          ...defaultConvertPiSqsEvent.payload,
          purchase_intention_id: 'NON_EXISTENT_PI',
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Purchase intention not found');
  }, 30000);

  it('Should NOT convert PI and return fail if supplied modified_at does not match pi in db', async () => {
    const eventPayload = createInboundEvent(defaultConvertPiSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        payload: {
          ...defaultConvertPiSqsEvent.payload,
          modified_at: new Date().toISOString(),
        },
      },
    ]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Pi was changed by someone else since the event was created');
  }, 30000);

  it('Should successfully convert PVMS PI to NCO', async () => {
    const eventPayload = createInboundEvent(defaultConvertPiSqsEvent);
    const event = createSqsEvent([eventPayload]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Check NCO was created
    const nco = await ncoRepo.findOneBy({ pk_new_car_order_id: purchaseIntentionPvms.purchase_intention_id });
    expect(nco).not.toBeNull();
    expect(nco?.order_status_onevms_code).toEqual(outboundEventMapping!.order_status_code);
    expect(nco?.order_status_onevms_error_code).toEqual(outboundEventMapping!.error_status_code);
    expect(nco?.order_invoice_onevms_code).toEqual(outboundEventMapping!.invoice_status_code);

    // Check PI was marked as converted
    const pi = await piRepo.findOneBy({ purchase_intention_id: purchaseIntentionPvms.purchase_intention_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toEqual(true);

    // Check audit trail
    const auditTrails = await dataSource
      .getRepository(NewCarOrderAuditTrailModel)
      .findBy({ pk_new_car_order_id: purchaseIntentionPvms.purchase_intention_id });
    expect(auditTrails.length).toBe(1);
    expect(auditTrails[0].action_type).toEqual(NcoExportActionType.CONVERT);
  }, 30000);

  it('Should successfully convert CSS PI to NCO', async () => {
    const eventPayload = createInboundEvent({
      ...defaultConvertPiSqsEvent,
      payload: {
        ...defaultConvertPiSqsEvent.payload,
        purchase_intention_id: purchaseIntentionCss.purchase_intention_id,
        deal_id: purchaseIntentionCss.purchase_intention_id,
        modified_at: cssPiModifiedAt,
        configuration: { ordered_options: [{ option_id: 'option2' }] },
      },
    });
    const event = createSqsEvent([eventPayload]);

    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    // Check NCO was created (query for deal_id since nco id will be generated)
    const nco = await ncoRepo.findOne({
      where: { deal_id: purchaseIntentionCss.purchase_intention_id },
      relations: [
        'configuration',
        'configuration.ordered_options',
        'configuration.ordered_options.content',
        'configuration.ordered_options.content.content',
      ],
    });
    expect(nco).not.toBeNull();
    expect(nco?.order_status_onevms_code).toEqual(outboundEventMapping!.order_status_code);
    expect(nco?.order_status_onevms_error_code).toEqual(outboundEventMapping!.error_status_code);
    expect(nco?.order_invoice_onevms_code).toEqual(outboundEventMapping!.invoice_status_code);

    //check that configuration was taken from onevms config of PI request
    expect(nco?.configuration.ordered_options.length).toEqual(1);
    expect(nco?.configuration.ordered_options[0].option_id).toEqual('option2');

    //check that new NCO ID was generated
    expect(nco?.pk_new_car_order_id).not.toEqual(purchaseIntentionCss.purchase_intention_id);
    createdNcoIds.push(nco!.pk_new_car_order_id);

    // Check PI was marked as converted
    const pi = await piRepo.findOneBy({ purchase_intention_id: purchaseIntentionCss.purchase_intention_id });
    expect(pi).not.toBeNull();
    expect(pi?.is_converted).toEqual(true);

    // Check audit trail
    const auditTrails = await dataSource
      .getRepository(NewCarOrderAuditTrailModel)
      .findBy({ pk_new_car_order_id: nco!.pk_new_car_order_id });
    expect(auditTrails.length).toBe(1);
    expect(auditTrails[0].action_type).toEqual(NcoExportActionType.CONVERT);
  }, 30000);
});
