import { Ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource, EntityManager } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import {
  UnchangeableKeysCoraNCPurchaseIntentionToNCO,
  unchangeableKeysCoraNCPurchaseIntentionToNCO,
} from '../../../../../lib/types/new-car-order-types';
import {
  DefaultEventHandlerResult,
  InboundEventHandlerEventConvertPi,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { KccConfigSignatureVerifier } from '../../../../utils/kcc-config-signature-verifier/kcc-config-signature-verifier';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { QuotaApiAdapter } from '../../../../utils/quota-api';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import {
  createNewCarOrderId,
  createNewCarOrderObjectFromPurchaseIntention,
  getStatusUpdateStatementsFromOutboundMapping,
  ncoApiToDbObj,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
  validateNcoRequest,
} from '../../../../utils/utils-typeorm';
import {
  getValidQuotaMonthForConfig,
  isQuotaMonthInAllowedRange,
  updateKasConfigurationWithOptionAddedAtTimestamp,
} from '../../../../utils/validation-helpers';
import { NcoExportActionType } from '../../../export-nco/types';
import { EventHandlerContext } from '../event-handler-context';

type ConvertPiWithMessageId = InboundEventHandlerEventConvertPi & { messageId: string };
type ConvertPiWithError = ConvertPiWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventConvertPi>('InboundEventHandlerEventConvertPi');

// ENV vars and constants
const stage = getEnvVarWithAssert('STAGE');
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');
const allowMockQuotaApi = process.env.ALLOW_QUOTA_API_MOCK ?? 'false';

// Table names
const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');

const ncoValidationTables = {
  coraOrgRelationTableName,
  shippingCodeTableName,
  orderTypeTableName,
  importerTableName,
  dealerTableName,
};

EventHandlerContext.init(
  OneVmsEventHandlerKey.CONVERT_PI,
  [
    CoraPurchaseIntentionModel,
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    ModelTypeVisibilityModel,
    OutboundProcessMappingModel,
  ],
  quotaApiSecretArn,
);

const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: EventHandlerContext.logger,
});

const convertPiFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  // Track events status
  const unParseableEvents: Partial<ConvertPiWithError>[] = [];
  const expectedFailedEvents: ConvertPiWithError[] = [];
  const unexpectedFailedEvents: ConvertPiWithError[] = [];
  const successfulEvents: ConvertPiWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  // Parse SQS events
  const convertPiEvents = event.Records.map((record) => {
    try {
      const convertPiEvent = JSON.parse(record.body) as InboundEventHandlerEventConvertPi | undefined;
      const [body_validated, validation_errors] = sqsEventValidator.validate(convertPiEvent);

      if (body_validated === null) {
        const message = 'SQS record is not valid, validation failed';
        logger.log(LogLevel.WARN, message, { data: validation_errors });
        unParseableEvents.push({
          ...convertPiEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      return { ...convertPiEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventConvertPi>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as ConvertPiWithMessageId[];

  // Initialize datasource
  let dataSource: DataSource;

  try {
    dataSource = await EventHandlerContext.getDataSource();
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      convertPiEvents.map((convertPiEvent) =>
        eventToNotification(convertPiEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: convertPiEvents.map((convertPiEvent) => ({
        itemIdentifier: convertPiEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  // Get outbound mapping config
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );

    // If there is no mapping, the action is aborted because Convert PI needs an outbound mapping.
    if (!outboundEventMapping) {
      throw new Error(
        `No outbound mapping found for successful result of event handler ${EventHandlerContext.eventHandlerKey}.`,
      );
    }
    outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);

    //we need to catch the case where ALL is configured for convert PI, which is not possible
    if (
      outboundStatusUpdateStatement.order_status_onevms_code === undefined ||
      outboundStatusUpdateStatement.order_status_onevms_error_code === undefined ||
      outboundStatusUpdateStatement.order_invoice_onevms_code === undefined
    ) {
      throw new Error('Invalid outbound mapping config: need explicit values for all status fields.');
    }
  } catch (e) {
    const message = 'Failed to get outbound event mapping config';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      convertPiEvents.map((convertPiEvent) =>
        eventToNotification(convertPiEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );

    return {
      batchItemFailures: convertPiEvents.map((convertPiEvent) => ({
        itemIdentifier: convertPiEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  // Process events
  for (const convertPiEvent of convertPiEvents) {
    const userAttributes = convertPiEvent.user_auth_context;
    const convertPiRequest = convertPiEvent.payload;
    logger.setObjectId(convertPiRequest.purchase_intention_id);
    logger.setCorrelationId(convertPiEvent.transaction_id);

    // Extract auth context
    const visibilityLevel = userAttributes.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    const ppnId = userAttributes.organizationId;

    if (!visibilityLevel || !ppnId) {
      const message = 'Failed to get visibility level or ppnId from auth context';
      logger.log(LogLevel.ERROR, message, { data: convertPiEvent });
      expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
      continue;
    }

    try {
      // Verify config signature in non-dev environments
      if (stage !== 'dev') {
        const isValidSignature = await KccConfigSignatureVerifier.verifySignature(
          {
            kas: {
              model_info: {
                model_type: convertPiRequest.model_type,
                model_year: parseInt(convertPiRequest.model_year, 10),
                country_code: convertPiRequest.cnr,
              },
              configuration: convertPiRequest.configuration,
            },
            pvms: convertPiRequest.configuration_expire,
          },
          convertPiRequest.configuration_signature,
          convertPiRequest.cookie_header,
          stage,
          logger,
        );

        if (!isValidSignature) {
          const message = 'KCC config signature validation failed';
          logger.log(LogLevel.WARN, message);
          expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
          continue;
        }
      }

      // Get purchase intention (only if not converted yet)
      const purchaseIntention = await dataSource.getRepository(CoraPurchaseIntentionModel).findOneBy({
        purchase_intention_id: convertPiRequest.purchase_intention_id,
        is_converted: false,
      });

      if (!purchaseIntention) {
        const message = 'Purchase intention not found';
        logger.log(LogLevel.FATAL, message);
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
        continue;
      }

      //compare suplied modified_at with nco modified_at and reject the action if they do not match
      if (new Date(purchaseIntention.modified_at ?? 0).toISOString() !== convertPiRequest.modified_at) {
        const message = 'Pi was changed by someone else since the event was created';
        logger.log(LogLevel.ERROR, message, { data: { purchaseIntention, convertPiEvent } });
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
        continue;
      }

      // Generate nco ID if PI did not come from PVMS
      // (old PIs have different string in created_by, so check for CSS for now)
      let createdNcoid: string | null = purchaseIntention.purchase_intention_id;
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      if (purchaseIntention.created_by === OneVmsSourceSystemKey.CSS) {
        const ncoRepo = dataSource.getRepository(NewCarOrderModel);
        createdNcoid = await createNewCarOrderId(
          {
            importerCode: purchaseIntention.importer_code,
            ncoRepo: ncoRepo,
          },
          logger,
        );
        if (!createdNcoid) {
          const message = `Error creating new car order id`;
          logger.log(LogLevel.ERROR, message, { data: { convertPiEvent } });
          unexpectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
          continue;
        }
      }
      // Create NCO object
      const ncoObjectFromPi = createNewCarOrderObjectFromPurchaseIntention(
        { purchaseIntention, sourceSystem: OneVmsSourceSystemKey.CORA_USER, ncoId: createdNcoid },
        logger,
      );
      const backendPurchaseIntention_only_disallowed_keys = unchangeableKeysCoraNCPurchaseIntentionToNCO.reduce(
        (resObj: Partial<UnchangeableKeysCoraNCPurchaseIntentionToNCO>, key) => (
          (resObj[key] = ncoObjectFromPi[key]), resObj
        ),
        {},
      ) as UnchangeableKeysCoraNCPurchaseIntentionToNCO;
      const newCarOrder = {
        ...ncoObjectFromPi,
        ...convertPiRequest,
        ...backendPurchaseIntention_only_disallowed_keys,
      };

      // Validate NCO
      const validation = await validateNcoRequest(
        {
          mtvRespoitory: dataSource.getRepository(ModelTypeVisibilityModel),
          newCarOrder,
          dynamoDb: EventHandlerContext.dynamoDb,
          ppnId,
          visibilityLevel,
          customerRelatedOt: true,
          tables: ncoValidationTables,
        },
        logger,
      );

      if (!validation.valid) {
        const message = `Validation failed: ${validation.error}`;
        logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
        continue;
      }

      // Check quota month
      const quotaAllowedRange = getValidQuotaMonthForConfig({ config: newCarOrder.configuration }, logger);
      if (
        !isQuotaMonthInAllowedRange(
          {
            quotaMonth: newCarOrder.quota_month,
            from: quotaAllowedRange.from,
            until: quotaAllowedRange.until,
          },
          logger,
        )
      ) {
        const message = `Quota month not in valid range`;
        logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
        continue;
      }

      // Consume quota
      const mockQuotaApi = allowMockQuotaApi === 'true';
      const couldConsumeQuota = await quotaApiAdapter.consumeQuota(
        {
          nco: newCarOrder,
          mockApi: mockQuotaApi,
          correlationId: logger.getCorrelationId() ?? 'unknown',
        },
        logger,
      );

      if (!couldConsumeQuota) {
        const message = 'Could not consume quota';
        logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
        continue;
      }

      // Save NCO and mark PI as converted
      const newCarOrderObj = {
        ...ncoApiToDbObj(
          {
            ...newCarOrder,
            configuration: updateKasConfigurationWithOptionAddedAtTimestamp(
              { config: newCarOrder.configuration },
              logger,
            ),
          },
          userAttributes.username,
        ),
        ...outboundStatusUpdateStatement,
      };
      await saveNcosWithAuditTrail(
        dataSource,
        [newCarOrderObj.pk_new_car_order_id],
        NcoExportActionType.CONVERT,
        async (transactionManager: EntityManager) => {
          //create nco
          const createdNco = await transactionManager.save(NewCarOrderModel, newCarOrderObj);
          // mark PI as converted
          await transactionManager.update(
            CoraPurchaseIntentionModel,
            {
              purchase_intention_id: purchaseIntention.purchase_intention_id,
            },
            {
              is_converted: true,
            },
          );

          return [createdNco];
        },
        logger,
        false,
      );

      logger.log(LogLevel.INFO, 'Successfully converted PI to NCO');
      //need to have nco_id in notification event so that export considers it valid
      successfulEvents.push({ ...convertPiEvent, nco_id: convertPiRequest.purchase_intention_id });
    } catch (error) {
      const err = error as { message: string; name: string };
      if (err.name === 'QueryFailedError' && err.message.includes('could not serialize access')) {
        const message = 'Concurrent modification detected';
        expectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
      } else {
        const message = 'Unexpected error during conversion';
        logger.log(LogLevel.ERROR, message, { data: error });
        unexpectedFailedEvents.push({ ...convertPiEvent, errorMessage: message });
      }
      continue;
    }
  }

  // Handle notifications
  const notifications: NotificationKafkaEvent[] = [
    ...successfulEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO)),
    ...expectedFailedEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage)),
    ...unParseableEvents.map((e) =>
      unparseableEventToNotification(
        e,
        OneVmsEventKey.CONVERT_PI,
        NotificationStatus.EVENT_HANDLER_NIO,
        e.errorMessage,
      ),
    ),
    ...unexpectedFailedEvents.map((e) => {
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: e.messageId,
        errorMessage: e.errorMessage,
      });
      return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
    }),
  ];

  try {
    await EventHandlerContext.pushNotificationsToKafka(notifications);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, convertPiFunc);
