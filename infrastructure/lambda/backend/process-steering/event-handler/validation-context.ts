import { NewCarOrderModel } from '../../../../lib/entities/new-car-order-model';
import { CoraOrgRelModel } from '../../../../lib/types/boss-org-types';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { getAllAuthorizedOrgs } from '../../../utils/validation-helpers';
import { EventHandlerContext } from './event-handler-context';

export class EventHandlerValidationContext {
  public static readonly orgRelTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
  // In-memory cache to store authorized organizations by ppnOrgId
  private static readonly cache: Map<string, CoraOrgRelModel[]> = new Map();

  public static async authorizedForOrder(ppnOrgId: string, nco: NewCarOrderModel): Promise<boolean> {
    if (this.cache.has(ppnOrgId)) {
      const cachedAuthorizedOrgs = this.cache.get(ppnOrgId);
      const correspondingDealerOrg = cachedAuthorizedOrgs?.find(
        (org) => org.dealer_number === nco.dealer_number && org.importer_number === nco.importer_number,
      );
      return !!correspondingDealerOrg;
    }

    const authorizedOrgs = await getAllAuthorizedOrgs(
      {
        dynamoDb: EventHandlerContext.dynamoDb,
        orgRelsTableName: this.orgRelTableName,
        ppnId: ppnOrgId,
      },
      EventHandlerContext.logger,
    );
    this.cache.set(ppnOrgId, authorizedOrgs);

    const correspondingDealerOrg = authorizedOrgs.find(
      (org) => org.dealer_number === nco.dealer_number && org.importer_number === nco.importer_number,
    );
    return !!correspondingDealerOrg;
  }

  public static clearAuthorizedOrgsCache(): void {
    this.cache.clear();
  }
}
