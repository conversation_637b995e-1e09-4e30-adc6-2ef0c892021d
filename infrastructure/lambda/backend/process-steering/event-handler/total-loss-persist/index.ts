import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';

import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { EventHandlerContext } from '../event-handler-context';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  InboundEventHandlerEvent,
  InboundEventHandlerUnparsableEvent,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SystemOrderActionEventHandlerEvent,
  TotalLossHandlerResult,
} from '../../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { saveNcosWithAuditTrail } from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { EntityManager } from 'typeorm';

EventHandlerContext.init(OneVmsEventHandlerKey.TOTAL_LOSS_PERSIST, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const sqsEventValidator = new ObjectValidator<SystemOrderActionEventHandlerEvent>('SystemOrderActionEventHandlerEvent');

const totalLossPersistFunc = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  event: SQSEvent,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  context: Context,
  /**
   * @deprecated use EventHandlerContext.logger instead
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  logger: KasLambdaLogger,
  // eslint-disable-next-line @typescript-eslint/require-await
): Promise<SQSBatchResponse | void> => {
  const outboundStatusUpdateStatement = await EventHandlerContext.getOutboundStatusUpdateStatement(
    TotalLossHandlerResult.SUCCESS,
    true,
  );

  const { unParseableEvents, parsedEvents: parsedEvents } = EventHandlerContext.commonEventInputValidation(
    event,
    sqsEventValidator,
  );
  const expectedFailedEvents: InboundEventHandlerEvent[] = [];
  const unexpectedFailedEvents: InboundEventHandlerUnparsableEvent[] = [];
  const successfulEvents: (InboundEventHandlerEvent & { messageId: string })[] = [];

  /**
   * 1. Validate if orders are in correct status (Recheck Inbound Mapping)
   * 2. Change Status
   * 3. Send Result
   */

  const ncoRepo = (await EventHandlerContext.getDataSource()).getRepository(NewCarOrderModel);
  const updatedNcos: NewCarOrderModel[] = [];
  for (const reportRequest of parsedEvents) {
    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: reportRequest.nco_id,
      });

      // Validate Request
      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + reportRequest.nco_id;
        EventHandlerContext.logger.log(LogLevel.FATAL, message, { data: reportRequest });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== reportRequest.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        EventHandlerContext.logger.log(LogLevel.WARN, message, { data: { sourceNco, reportRequest } });
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      if (reportRequest.source_system !== OneVmsSourceSystemKey.CORA_SYSTEM) {
        const message = 'User/System is not authorized for provided dealer or importer number';
        EventHandlerContext.logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }

      // Save to DB
      const res = await saveNcosWithAuditTrail(
        await EventHandlerContext.getDataSource(),
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.REPORT_LOSS,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: OneVmsSourceSystemKey.CORA_SYSTEM,
              ...outboundStatusUpdateStatement,
            },
          );

          // Reload and return updated orders to ensure all orders were changed correctly
          return transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              modified_by: OneVmsSourceSystemKey.CORA_SYSTEM,
            },
          });
        },
        EventHandlerContext.logger,
        false,
      );

      EventHandlerContext.logger.log(
        LogLevel.INFO,
        `Action of NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was executed successfully.`,
      );
      updatedNcos.push(...res);
      successfulEvents.push(reportRequest);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...reportRequest, payload: message });
        continue;
      }
      const message = 'Unexpected database error occurred';
      EventHandlerContext.logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...reportRequest, errorMessage: message });
      continue;
    }
  }

  return await EventHandlerContext.sendNotifications({
    eventKey: OneVmsEventKey.TOTAL_LOSS_PERSIST,
    expectedFailedEvents,
    unexpectedFailedEvents,
    unParseableEvents,
    successfulEvents,
  });
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, totalLossPersistFunc);
