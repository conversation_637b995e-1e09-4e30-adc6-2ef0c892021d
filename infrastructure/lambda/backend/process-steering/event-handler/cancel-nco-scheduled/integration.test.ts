/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { DataSource, Repository, In } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../../../../lib/entities/new-car-order-model';
import {
  SystemOrderActionEventHandlerEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  DefaultEventHandlerResult,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  createInboundEvent,
  createSqsEvent,
  initDataSourceForIntTest,
  invokeGenericLambda,
  pollNotifications,
} from '../../../../utils/integration-test-helpers';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.CANCEL_NCO_SCHEDULED}`);
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

//existing nco test objects
const ncoSuccess: NewCarOrderModel = {
  pk_new_car_order_id: 'ITCANCELSCHEDULEDSUCCESS',
  modified_at: '2022-12-06T10:21:02.876Z',
  dealer_number: 'ItNcoCancelDlr1',
  importer_code: 'IT',
  importer_number: 'ItNcoCancelImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: null,
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED,
  order_status_onevms_error_code: Constants.CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const ncoQuotaSet: NewCarOrderModel = {
  pk_new_car_order_id: 'ITCANCELSCHEDULEDQUOTASET',
  modified_at: '2022-12-06T10:21:02.876Z',
  dealer_number: 'ItNcoCancelDlr1',
  importer_code: 'IT',
  importer_number: 'ItNcoCancelImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'OT1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED,
  order_status_onevms_error_code: Constants.CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

function createDefaultCancelScheduledEvent(
  nco: NewCarOrderModel,
): Omit<SystemOrderActionEventHandlerEvent, 'transaction_id' | 'sub_transaction_id' | 'action_at'> {
  return {
    event_type: OneVmsEventKey.CANCEL_SCHEDULED,
    nco_id: nco.pk_new_car_order_id,
    modified_at: nco.modified_at!,
    source_system: OneVmsSourceSystemKey.CORA_SYSTEM,
    payload: { cancellationDate: '2024-01-01' },
  };
}

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    OutboundProcessMappingModel,
  ]);
  repository = dataSource.getRepository(NewCarOrderModel);
  outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
    source_event_handler: OneVmsEventHandlerKey.CANCEL_NCO_SCHEDULED,
    event_result: DefaultEventHandlerResult.SUCCESS,
  });

  await repository.save(ncoSuccess);
  await repository.save(ncoQuotaSet);
});

afterAll(async () => {
  await repository.delete({
    pk_new_car_order_id: In([ncoSuccess.pk_new_car_order_id, ncoQuotaSet.pk_new_car_order_id]),
  });
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: In([ncoSuccess.pk_new_car_order_id, ncoQuotaSet.pk_new_car_order_id]),
  });
  await dataSource.destroy();
});

afterEach(async () => {
  //reset orders after every test
  await repository.save(ncoSuccess);
  await repository.save(ncoQuotaSet);
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: In([ncoSuccess.pk_new_car_order_id, ncoQuotaSet.pk_new_car_order_id]),
  });
});

it('Should NOT cancel nco and return fail if input is missing required props', async () => {
  const eventPayload = createInboundEvent(createDefaultCancelScheduledEvent(ncoSuccess));
  const event = createSqsEvent([{ ...eventPayload, nco_id: undefined }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);

  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('SQS record is not valid');

  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoSuccess.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(ncoSuccess.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(ncoSuccess.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(ncoSuccess.order_invoice_onevms_code);
});

it('Should NOT cancel nco if nco cannot be found', async () => {
  const eventPayload = createInboundEvent(createDefaultCancelScheduledEvent(ncoSuccess));
  const event = createSqsEvent([
    {
      ...eventPayload,
      nco_id: 'NOT_VALID_NCOID',
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to find order with id');

  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoSuccess.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).not.toEqual(outboundEventMapping?.order_status_code);
  expect(ncoDb?.order_status_onevms_error_code).not.toEqual(outboundEventMapping?.error_status_code);
});

it('Should NOT cancel nco if quota month is set', async () => {
  const eventPayload = createInboundEvent(createDefaultCancelScheduledEvent(ncoQuotaSet));
  const event = createSqsEvent([eventPayload]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);

  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Quota on NewCarOrder is set again');

  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoQuotaSet.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).not.toEqual(outboundEventMapping?.order_status_code);
  expect(ncoDb?.order_status_onevms_error_code).not.toEqual(outboundEventMapping?.error_status_code);
});

it('Should NOT cancel nco and return fail if modified_at does not match', async () => {
  const eventPayload = createInboundEvent(createDefaultCancelScheduledEvent(ncoSuccess));
  const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Nco was changed by someone else');

  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoSuccess.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).not.toEqual(outboundEventMapping?.order_status_code);
  expect(ncoDb?.order_status_onevms_error_code).not.toEqual(outboundEventMapping?.error_status_code);
});

it('Should cancel nco and return no fails ', async () => {
  const eventPayload = createInboundEvent(createDefaultCancelScheduledEvent(ncoSuccess));
  const event = createSqsEvent([eventPayload]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: ncoSuccess.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(outboundEventMapping?.order_status_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(outboundEventMapping?.error_status_code);
});
