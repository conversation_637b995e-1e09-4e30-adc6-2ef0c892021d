import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { DataSource, DeepPartial, EntityManager, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { NcoExportActionType } from '../../../export-nco/types';
import {
  createNewCarOrderId,
  getStatusUpdateStatementsFromOutboundMappingDeepPartial,
  ncoApiToDbObj,
  saveNcosWithAuditTrail,
  validateNcoRequest,
} from '../../../../utils/utils-typeorm';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import {
  DefaultEventHandlerResult,
  InboundEventHandlerEventCreateNco,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { QuotaApiAdapter } from '../../../../utils/quota-api';
import {
  getValidQuotaMonthForConfig,
  isQuotaMonthInAllowedRange,
  updateKasConfigurationWithOptionAddedAtTimestamp,
} from '../../../../utils/validation-helpers';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { CoraNCOBaseApiRequest } from '../../../../../lib/types/new-car-order-types';
import { EventHandlerContext } from '../event-handler-context';

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventCreateNco>('InboundEventHandlerEventCreateNco');

type CreateNcoWithMessageId = InboundEventHandlerEventCreateNco & { messageId: string; nco_id: string };
type CreateNcoWithError = CreateNcoWithMessageId & { errorMessage: string };

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

// Table name environment variables
const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');
const ncoValidationTables = {
  coraOrgRelationTableName,
  shippingCodeTableName,
  orderTypeTableName,
  importerTableName,
  dealerTableName,
};

const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');
const allowMockQuotaApi = getEnvVarWithAssert('ALLOW_QUOTA_API_MOCK');

EventHandlerContext.init(
  OneVmsEventHandlerKey.CREATE_NCO,
  [
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    ModelTypeVisibilityModel,
  ],
  quotaApiSecretArn,
);

// Initialize secret cache and quota api adapter
const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: EventHandlerContext.logger,
});

const createNewCarOrderFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  //Remember fails and successes
  const unParseableEvents: Partial<CreateNcoWithError>[] = [];
  const expectedFailedEvents: CreateNcoWithError[] = [];
  const unexpectedFailedEvents: CreateNcoWithError[] = [];
  const successfulEvents: CreateNcoWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  const mockQuotaApi = allowMockQuotaApi === 'true' ? true : false;

  //Parse sqs events
  const createNcoEvents = event.Records.map((record) => {
    try {
      const createNcoEvent = JSON.parse(record.body) as InboundEventHandlerEventCreateNco | undefined;

      //Check if sqs content is valid
      const isValid = validateSqsEvent(createNcoEvent, logger);
      if (!isValid) {
        const message = 'SQS record is not valid, skipping';
        logger.log(LogLevel.ERROR, message, {
          data: createNcoEvent,
          correlationId: createNcoEvent?.transaction_id,
        });
        unParseableEvents.push({
          ...createNcoEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      return { ...createNcoEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventCreateNco>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as CreateNcoWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusCreateStatement: DeepPartial<NewCarOrderModel> = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      outboundStatusCreateStatement = getStatusUpdateStatementsFromOutboundMappingDeepPartial(outboundEventMapping);
    }
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      createNcoEvents.map((createNcoEvent) =>
        eventToNotification(createNcoEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: createNcoEvents.map((createNcoEvent) => ({
        itemIdentifier: createNcoEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      createNcoEvents.map((createNcoEvent) =>
        eventToNotification(createNcoEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: createNcoEvents.map((createNcoEvent) => ({
        itemIdentifier: createNcoEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const createNcoEvent of createNcoEvents) {
    logger.setCorrelationId(createNcoEvent.transaction_id);
    const userAttributes = createNcoEvent.user_auth_context;

    // Extract necessary attributes from auth context
    const visibilityLevel =
      userAttributes.kasApplications[EventHandlerContext.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    const ppnId = userAttributes.organizationId;

    if (!visibilityLevel) {
      const message = 'Failed to get the visibility level';
      logger.log(LogLevel.ERROR, message, { data: createNcoEvent });
      expectedFailedEvents.push({ ...createNcoEvent, errorMessage: message });
      continue;
    }

    try {
      //validate all parameters based on users permissions
      const _res = await validateNcoRequest(
        {
          dynamoDb,
          newCarOrder: createNcoEvent.payload,
          ppnId,
          visibilityLevel,
          tables: ncoValidationTables,
          mtvRespoitory: ncoDataSource.getRepository(ModelTypeVisibilityModel),
          customerRelatedOt: false,
        },
        logger,
      );

      if (!_res.valid) {
        const message = 'Custom validation failed with: ' + _res.error;
        logger.log(LogLevel.ERROR, message);
        unParseableEvents.push({ ...createNcoEvent, errorMessage: message });
        continue;
      }

      //create new unique nco id
      const createdNcoid = await createNewCarOrderId(
        {
          importerCode: createNcoEvent.payload.importer_code,
          ncoRepo,
        },
        logger,
      );
      if (!createdNcoid) {
        const message = `Error creating new car order id`;
        logger.log(LogLevel.ERROR, message, { data: { createNcoEvent } });
        unexpectedFailedEvents.push({ ...createNcoEvent, errorMessage: message });
        continue;
      }

      // Check if quota is allowed
      const quotaAllowedRange = getValidQuotaMonthForConfig({ config: createNcoEvent.payload.configuration }, logger);
      if (
        !isQuotaMonthInAllowedRange(
          {
            quotaMonth: createNcoEvent.payload.quota_month,
            from: quotaAllowedRange.from,
            until: quotaAllowedRange.until,
          },
          logger,
        )
      ) {
        logger.log(LogLevel.WARN, 'quota_month not in valid range by config', { data: quotaAllowedRange });
        const message = `quota_month not in valid range by config ${JSON.stringify(quotaAllowedRange)})`;
        logger.log(LogLevel.ERROR, message, { data: { createNcoEvent } });
        expectedFailedEvents.push({ ...createNcoEvent, errorMessage: message });
        continue;
      }

      //try to consume the quota
      const newCarOrderWithId: CoraNCOBaseApiRequest = {
        ...createNcoEvent.payload,
        pk_new_car_order_id: createdNcoid,
        configuration: updateKasConfigurationWithOptionAddedAtTimestamp(
          {
            config: createNcoEvent.payload.configuration,
          },
          logger,
        ),
      };

      const couldConsumeQuota = await quotaApiAdapter.consumeQuota(
        {
          nco: newCarOrderWithId,
          mockApi: mockQuotaApi,
          correlationId: createNcoEvent.transaction_id,
        },
        logger,
      );

      if (couldConsumeQuota) {
        //create the order if quota was consumed
        const ncoDbObj = ncoApiToDbObj(newCarOrderWithId, userAttributes.username);
        await saveNcosWithAuditTrail(
          ncoDataSource,
          [ncoDbObj.pk_new_car_order_id],
          NcoExportActionType.CREATE,
          async (transactionManager: EntityManager) => {
            return [
              await transactionManager.getRepository(NewCarOrderModel).save({
                ...ncoDbObj,
                ...outboundStatusCreateStatement,
              }),
            ];
          },
          logger,
          false,
        );

        logger.log(LogLevel.INFO, `Nco was created successfully.`);
        successfulEvents.push({ ...createNcoEvent, nco_id: createdNcoid });
      } else {
        const message = 'No quota available. Failed to create order.';
        logger.log(LogLevel.ERROR, message);
        expectedFailedEvents.push({ ...createNcoEvent, errorMessage: message });
        continue;
      }
    } catch (error) {
      const message = 'Unexpected database error occurred during creation of new car order.';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...createNcoEvent, errorMessage: message });
      continue;
    }
  }

  //Export results into a notification topic
  const successfulNotifications = successfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(e, OneVmsEventKey.CREATE, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  // Unexpected fail events are not consumed and can be retried
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  // Alle Notifications zusammenführen
  const notificationEvents: NotificationKafkaEvent[] = [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

function validateSqsEvent(input: InboundEventHandlerEventCreateNco | undefined, logger: KasLambdaLogger): boolean {
  const [body_validated, validation_errors] = sqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, createNewCarOrderFunc);
