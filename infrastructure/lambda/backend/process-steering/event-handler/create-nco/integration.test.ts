import {
  initDataSourceForIntTest,
  prepareDynamodb,
  cleanupDynamodb,
  buildLambdaArn,
  createStandartOrgRelPattern,
  createSqsEvent,
  invokeGenericLambda,
  pollNotifications,
  createInboundEvent,
} from '../../../../utils/integration-test-helpers';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { DataSource, In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import {
  DefaultEventHandlerResult,
  CreateNcoPayload,
  InboundEventHandlerEventCreateNco,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.CREATE_NCO}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('create-nco');

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;

// Auth context apps test data
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

// Model type visibility test data
const mtvImpInPast = {
  // For IMP visible date in past
  importer_number: mdImporter.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

// Default create NCO request object
const defaultCreateNcoReqObject: CreateNcoPayload = {
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdImporter.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otNoCustomerRel.pk_order_type,
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scBlacklistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc1',
  cnr: 'C23',
  configuration: {
    ordered_options: [
      {
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
  },
  configuration_signature: 'mydummysignature',
  configuration_expire: { dummyProp: 'iAmDummy' },
};

// Default create NCO SQS event
const defaultCreateNcoSqsEvent: InboundEventHandlerEventCreateNco = {
  event_type: OneVmsEventKey.CREATE,
  transaction_id: uuidv4(),
  sub_transaction_id: uuidv4(),
  action_at: new Date().toISOString(),
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  payload: defaultCreateNcoReqObject,
  source_system: OneVmsSourceSystemKey.CORA,
  modified_at: new Date().toISOString(),
} satisfies InboundEventHandlerEventCreateNco;

let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;
let testMtvs: ModelTypeVisibilityModel[] = [];

// Collect created NCO IDs for deletion after tests
const createdNcoids: string[] = [];

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    OutboundProcessMappingModel,
    ModelTypeVisibilityModel,
  ]);
  ncoRepo = dataSource.getRepository(NewCarOrderModel);

  // Save model type visibility
  const mtvRepository = dataSource.getRepository(ModelTypeVisibilityModel);
  testMtvs = await mtvRepository.save([mtvImpInPast]);

  // Save outbound status mapping
  outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
    source_event_handler: OneVmsEventHandlerKey.CREATE_NCO,
    event_result: DefaultEventHandlerResult.SUCCESS,
  });

  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDlrTableName, objs: [mdDealer] },
    { tableName: mdImpTableName, objs: [mdImporter] },
    { tableName: mdScTableName, objs: Object.values(scs) },
    { tableName: mdOtTableName, objs: Object.values(ots) },
  ]);
}, 120000);

afterAll(async () => {
  // Delete created NCO objects
  if (createdNcoids.length > 0) {
    await ncoRepo.delete({
      pk_new_car_order_id: In(createdNcoids),
    });

    // Delete audit trails
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
      pk_new_car_order_id: In(createdNcoids),
    });
  }

  // Remove model type visibility
  await dataSource.getRepository(ModelTypeVisibilityModel).remove(testMtvs);

  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDlrTableName,
      pks: [
        {
          pk_importer_number: mdDealer.pk_importer_number,
          sk_dealer_number: mdDealer.sk_dealer_number,
        },
      ],
    },
    {
      tableName: mdImpTableName,
      pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
    },
    {
      tableName: mdScTableName,
      pks: scPks,
    },
    {
      tableName: mdOtTableName,
      pks: otPks,
    },
  ]);
}, 120000);

it('Should NOT create nco and return fail if input is missing required props', async () => {
  const eventPayload = createInboundEvent(defaultCreateNcoSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('SQS record is not valid');
});

it('Should NOT create nco and return fail if auth context is faulty', async () => {
  const eventPayload = createInboundEvent(defaultCreateNcoSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      user_auth_context: {
        ...eventPayload.user_auth_context,
        kasApplications: appsWithNoRole,
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to get the visibility level');
});

it('Should NOT create nco and return fail if order type is customer related', async () => {
  const eventPayload = createInboundEvent(defaultCreateNcoSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...defaultCreateNcoReqObject,
        order_type: ots.otCustomerRel.pk_order_type,
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Custom validation failed');
});

it('Should NOT create nco and return fail if quota month is invalid', async () => {
  const eventPayload = createInboundEvent(defaultCreateNcoSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...defaultCreateNcoReqObject,
        quota_month: '2999-01',
        requested_dealer_delivery_date: '2999-02-02',
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('No quota available');
});

it('Should create nco and return no fails', async () => {
  const eventPayload = createInboundEvent(defaultCreateNcoSqsEvent);
  const event = createSqsEvent([eventPayload]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);

  // Find the created NCO
  const ncos = await ncoRepo.find({
    where: {
      dealer_number: defaultCreateNcoReqObject.dealer_number,
      importer_number: defaultCreateNcoReqObject.importer_number,
      model_type: defaultCreateNcoReqObject.model_type,
    },
    order: { created_at: 'DESC' },
    take: 1,
  });

  expect(ncos.length).toBe(1);
  const ncoDb = ncos[0];

  // Remember the created NCO ID for cleanup
  createdNcoids.push(ncoDb.pk_new_car_order_id);

  // Check that the NCO was created with the correct values
  expect(ncoDb.dealer_number).toEqual(defaultCreateNcoReqObject.dealer_number);
  expect(ncoDb.importer_number).toEqual(defaultCreateNcoReqObject.importer_number);
  expect(ncoDb.model_type).toEqual(defaultCreateNcoReqObject.model_type);
  expect(ncoDb.model_year).toEqual(defaultCreateNcoReqObject.model_year);
  expect(ncoDb.order_type).toEqual(defaultCreateNcoReqObject.order_type);
  expect(ncoDb.quota_month).toEqual(defaultCreateNcoReqObject.quota_month);
  expect(ncoDb.shipping_code).toEqual(defaultCreateNcoReqObject.shipping_code);
  expect(ncoDb.receiving_port_code).toEqual(defaultCreateNcoReqObject.receiving_port_code);

  // Check status fields
  if (outboundEventMapping) {
    expect(ncoDb.order_status_onevms_code).toEqual(outboundEventMapping.order_status_code);
    expect(ncoDb.order_status_onevms_error_code).toEqual(SpecialStatusCode.NONE);
  }

  // Check that audit trail was created
  const auditTrails = await dataSource
    .getRepository(NewCarOrderAuditTrailModel)
    .findBy({ pk_new_car_order_id: ncoDb.pk_new_car_order_id });
  expect(auditTrails.length).toBeGreaterThan(0);
});
