import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { Kas<PERSON><PERSON><PERSON><PERSON><PERSON>ogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource, Repository } from 'typeorm';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { EventHandlerContext } from '../event-handler-context';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import {
  combineStatusMappingWithOutboundMapping,
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  NotificationStatus,
  SQSBatchResponseWithError,
  InboundEventHandlerEventP06UpdateNco,
  SpecialStatusCode,
  DefaultEventHandlerResult,
} from '../../../../../lib/types/process-steering-types';
import { P06NewCarOrderDataDTO } from '../../../../../lib/types/p06-types';
import p06ToOnevmsStatusMapping from '../../../../../data/status-mapping-p06/status_mapping_data.json';
import { MappingP06ToOneVmsStatus } from '../../../../../data/status-mapping-p06/types';

// Initialize EventHandlerContext
EventHandlerContext.init(OneVmsEventHandlerKey.P06_UPDATE_NCO, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

type P06WithMessageId = InboundEventHandlerEventP06UpdateNco & { messageId: string };
type P06WithError = P06WithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventP06UpdateNco>(
  'InboundEventHandlerEventP06UpdateNco',
);

const p06UpdateNcoFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse> => {
  const sqsBatchResponse: SQSBatchResponseWithError = { batchItemFailures: [] };
  const successfulEvents: P06WithMessageId[] = [];
  const expectedFailedEvents: P06WithError[] = [];
  const unexpectedFailedEvents: P06WithError[] = [];
  const unParseableEvents: Partial<P06WithError>[] = [];

  // Parse events from SQS
  const parsedEvents = event.Records.map((record) => {
    try {
      const parsed = JSON.parse(record.body) as InboundEventHandlerEventP06UpdateNco;
      const [body_validated, validation_errors] = sqsEventValidator.validate(parsed);
      if (body_validated === null) {
        const message = 'Failed to parse/validate event';
        logger.log(LogLevel.ERROR, message, { data: validation_errors });
        unParseableEvents.push({ ...parsed, errorMessage: message, messageId: record.messageId });
        return undefined;
      }
      return { ...parsed, messageId: record.messageId };
    } catch (error) {
      const message = 'Failed to parse SQS record';
      logger.log(LogLevel.ERROR, message, { data: error });
      unParseableEvents.push({ errorMessage: message, messageId: record.messageId });
      return undefined;
    }
  }).filter(Boolean) as P06WithMessageId[];

  // Get database connection
  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;
  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (error) {
    const message = 'Failed to initialize database connection';
    logger.log(LogLevel.ERROR, message, { data: error });
    await EventHandlerContext.pushNotificationsToKafka(
      parsedEvents.map((e) => eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, message)),
    );
    return {
      batchItemFailures: parsedEvents.map((e) => ({
        itemIdentifier: e.messageId,
        errorMessage: message,
      })),
    };
  }

  // Get outbound mapping config
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundSuccessMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );

    if (!outboundSuccessMapping) {
      throw new Error(
        `No outbound mapping found for successful result of event handler ${EventHandlerContext.eventHandlerKey}.`,
      );
    }
    outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundSuccessMapping);
  } catch (e) {
    const message = 'Failed to get outbound event mapping config';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      parsedEvents.map((parsedEvent) =>
        eventToNotification(parsedEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );

    return {
      batchItemFailures: parsedEvents.map((parsedEvent) => ({
        itemIdentifier: parsedEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  // Process each event
  for (const p06Event of parsedEvents) {
    try {
      const ncoId = p06Event.nco_id;
      logger.setCorrelationId(p06Event.transaction_id);
      logger.setObjectId(ncoId);

      const existingNco = await ncoRepo.findOneBy({ pk_new_car_order_id: ncoId });

      if (!existingNco) {
        const errorMessage = 'Referenced NCO not found';
        expectedFailedEvents.push({ ...p06Event, errorMessage });
        logger.log(LogLevel.ERROR, errorMessage, { data: ncoId });
        continue;
      }

      const validationResult = await EventHandlerContext.commonBusinessLogicValidation(p06Event, existingNco);
      if (!validationResult.valid) {
        logger.log(LogLevel.ERROR, validationResult.message!);
        expectedFailedEvents.push({ ...p06Event, errorMessage: validationResult.message! });
        continue;
      }

      // get status update statements from p06 status and mapping
      const statusUpdateStatement = getStatusUpdateStatementFromP06Status(
        p06Event.payload.value,
        outboundStatusUpdateStatement,
      );
      if (!statusUpdateStatement) {
        const errorMessage = 'No valid status mapping found for P06 status';
        expectedFailedEvents.push({ ...p06Event, errorMessage });
        logger.log(LogLevel.ERROR, errorMessage, { data: p06Event.payload.value });
        continue;
      }

      //check if status actually changed, to update status update timestamp
      let oneVMSStatusTimestamp = existingNco.order_status_onevms_timestamp_last_change;
      if (
        (statusUpdateStatement.order_status_onevms_code &&
          existingNco.order_status_onevms_code !== statusUpdateStatement.order_status_onevms_code) ??
        (statusUpdateStatement.order_status_onevms_error_code &&
          existingNco.order_status_onevms_error_code !== statusUpdateStatement.order_status_onevms_error_code)
      ) {
        oneVMSStatusTimestamp = new Date().toISOString();
      }

      await saveNcosWithAuditTrail(
        ncoDataSource,
        [existingNco.pk_new_car_order_id],
        NcoExportActionType.UPDATE,
        async (transactionManager) => {
          const entity = await transactionManager.preload(NewCarOrderModel, {
            ...existingNco,
            ...statusUpdateStatement,
            order_status_onevms_timestamp_last_change: oneVMSStatusTimestamp,
            modified_by: OneVmsSourceSystemKey.P06,
          });
          if (!entity) {
            throw new Error(`Entity with ID ${existingNco.pk_new_car_order_id} not found for update`);
          }
          return [await transactionManager.save(entity)];
        },
        logger,
        false,
      );

      logger.log(LogLevel.INFO, `P06 update for NCO ${ncoId} was successful`);
      successfulEvents.push(p06Event);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (err.name === 'QueryFailedError' && err.message.includes('concurrent update')) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...p06Event, errorMessage: message });
      } else {
        const message = 'Unexpected error during P06 update';
        logger.log(LogLevel.ERROR, message, { data: error });
        unexpectedFailedEvents.push({ ...p06Event, errorMessage: message });
      }
    }
  }

  // Handle notifications and batch responses
  const notificationEvents = EventHandlerContext.transformEventNotifications(
    { successfulEvents, expectedFailedEvents, unexpectedFailedEvents, unParseableEvents },
    OneVmsEventKey.P06_UPDATE_NCO,
  );

  // Unexpected fail events are not consumed and can be retried
  unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
  });

  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications', { data: error });
  }

  return sqsBatchResponse;
};

const getStatusUpdateStatementFromP06Status = (
  p06Data: P06NewCarOrderDataDTO,
  outboundStatusUpdateStatement: StatusUpdateStatement,
): StatusUpdateStatement | undefined => {
  const foundMapping = (p06ToOnevmsStatusMapping as MappingP06ToOneVmsStatus[]).find((mapping) => {
    return mapping.p06_status === p06Data.order_info.status_info.vehicle_status_piaom_code;
  });

  return combineStatusMappingWithOutboundMapping(
    outboundStatusUpdateStatement,
    foundMapping
      ? {
          order_status_onevms_code: foundMapping.one_vms_status,
          order_status_onevms_error_code: foundMapping.one_vms_error_status ?? SpecialStatusCode.NONE,
        }
      : undefined,
    false,
  );
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, p06UpdateNcoFunc);
