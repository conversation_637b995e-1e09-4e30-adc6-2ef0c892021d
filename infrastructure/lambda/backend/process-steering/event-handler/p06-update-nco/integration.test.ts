import { DataSource, Repository } from 'typeorm';
import {
  buildLambdaArn,
  createInboundEvent,
  createSqsEvent,
  initDataSourceForIntTest,
  invokeGenericLambda,
  pollNotifications,
} from '../../../../utils/integration-test-helpers';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import p06TestEvent from './p06_test_event.json';
import { P06NewCarOrderKafkaObject } from '../../../../../lib/types/p06-types';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.P06_UPDATE_NCO}`);
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const testNcoId = p06TestEvent.ids.new_car_order_id;

// Test NCO data
const testNco: NewCarOrderModel = {
  pk_new_car_order_id: testNcoId,
  dealer_number: '123456',
  importer_number: '12345',
  importer_code: 'DE1',
  model_type: 'MT001',
  model_year: '2024',
  cnr: 'C23',
  quota_month: '2024-01',
  order_type: 'OT1',
  shipping_code: 'SC1',
  receiving_port_code: 'PC1',
  created_by: 'IntegrationTest',
  modified_by: 'IntegrationTest',
  order_status_onevms_code: 'TEST_STATUS',
  order_status_onevms_error_code: 'TEST_STATUS_ERROR',
  order_invoice_onevms_code: 'TEST_STATUS_INVOICE',
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { ordered_options: [], created_by: 'IntegrationTest', modified_by: 'IntegrationTest' },
  configuration_expire: null,
};

const defaultSqsEvent = {
  event_type: OneVmsEventKey.P06_UPDATE_NCO,
  nco_id: testNcoId,
  modified_at: new Date().toISOString(),
  source_system: OneVmsSourceSystemKey.P06,
  payload: {
    key: 'whatever',
    value: p06TestEvent,
    timestamp: 878687868,
  },
};

let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;

describe('P06 Update NCO Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
    ]);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);
    const testNcoSaveRes = await ncoRepo.save(testNco);

    //update modified_at timestamps to avoid conflict errors
    testNco.modified_at = testNcoSaveRes.modified_at!;
    defaultSqsEvent.modified_at = testNcoSaveRes.modified_at!;
  });

  afterAll(async () => {
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({ pk_new_car_order_id: testNcoId });
    await ncoRepo.delete({ pk_new_car_order_id: testNcoId });
    await dataSource.destroy();
  });

  it('Should fail if SQS event is invalid', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions[0].status).toBe(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to parse/validate event');
  });

  it('Should fail if NCO does not exist', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, nco_id: 'NON_EXISTENT_NCO' }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions[0].status).toBe(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Referenced NCO not found');
  });

  it('Should fail if modified_at timestamp does not match', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions[0].status).toBe(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('changed by someone else');
  });

  it('Should fail if p06 status has no mapping to onevms status', async () => {
    //we need a deep copy here
    const eventPayload = createInboundEvent(JSON.parse(JSON.stringify(defaultSqsEvent)) as typeof defaultSqsEvent);
    (eventPayload.payload as P06NewCarOrderKafkaObject).value.order_info.status_info.vehicle_status_piaom_code =
      'invalid_status';

    const event = createSqsEvent([{ ...eventPayload }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions[0].status).toBe(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('No valid status mapping found');
  });

  it('Should successfully update NCO with the correct status', async () => {
    const eventPayload = createInboundEvent(defaultSqsEvent);
    const event = createSqsEvent([eventPayload]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);

    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const updatedNco = await ncoRepo.findOneBy({ pk_new_car_order_id: testNcoId });
    expect(updatedNco).toBeDefined();

    //check that FD05 maps to ID0000 and error = null and does not change invoice status
    expect(updatedNco?.order_status_onevms_code).toEqual('ID0000');
    expect(updatedNco?.order_status_onevms_error_code).toEqual('null');
    expect(updatedNco?.order_invoice_onevms_code).toEqual(testNco.order_invoice_onevms_code);

    expect(updatedNco?.modified_by).toBe(OneVmsSourceSystemKey.P06);
    expect(updatedNco?.order_status_onevms_timestamp_last_change).not.toEqual(
      testNco.order_status_onevms_timestamp_last_change,
    );

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions[0].status).toBe(NotificationStatus.EVENT_HANDLER_IO);
  });
});
