import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { EntityManager } from 'typeorm';
import { processNcoUpdate, NcoUpdateContext, NcoUpdateRequest } from './update-nco-shared';
import { NewCarOrderModel } from '../../../../../../lib/entities/new-car-order-model';
import { ModelTypeVisibilityModel } from '../../../../../../lib/entities/model-type-visibility-model';
import { QuotaApiAdapter } from '../../../../../utils/quota-api';
import { orderData } from '../../../../../test-data/order-data';
import { setupMocks } from '../../../../../test-utils/test-setup';

describe('NCO Update Shared Logic', () => {
  let mockLogger: KasLambdaLogger;
  let mockContext: NcoUpdateContext;
  let mockUserContext: NcoUpdateUserContext;
  let mockNcoRepo: any;
  let mockQuotaApiAdapter: jest.Mocked<QuotaApiAdapter>;
  let mockDataSource: any;
  let mockTransactionManager: jest.Mocked<EntityManager>;

  beforeEach(() => {
    setupMocks();
    
    mockLogger = new KasLambdaLogger('test', LogLevel.TRACE);
    
    // Mock repositories and data source
    mockNcoRepo = {
      findOneBy: jest.fn(),
      getRepository: jest.fn(),
    };
    
    mockTransactionManager = {
      delete: jest.fn(),
      preload: jest.fn(),
      save: jest.fn(),
    } as any;
    
    mockDataSource = {
      getRepository: jest.fn().mockReturnValue(mockNcoRepo),
    };
    
    mockQuotaApiAdapter = {
      consumeQuota: jest.fn(),
    } as any;
    
    mockContext = {
      ncoDataSource: mockDataSource,
      dynamoDb: {} as any,
      ncoValidationTables: {
        coraOrgRelationTableName: 'test-org-rel',
        shippingCodeTableName: 'test-sc',
        orderTypeTableName: 'test-ot',
        importerTableName: 'test-imp',
        dealerTableName: 'test-dlr',
      },
      orderTypeTableName: 'test-ot',
      quotaApiAdapter: mockQuotaApiAdapter,
      mockQuotaApi: true,
    };
    
    mockUserContext = {
      username: 'test-user',
      organizationId: 'test-org',
      visibilityLevel: 'test-visibility',
    };
  });

  describe('Successful NCO updates', () => {
    it('Should successfully update NCO with valid data', async () => {
      const testNco = {
        pk_new_car_order_id: 'test-nco-id',
        modified_at: '2024-01-01T00:00:00.000Z',
        model_year: '2024',
        model_type: 'MT001',
        configuration: {},
        ...orderData.baseNewCarOrder,
      };

      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: {
          ...orderData.updateNcoPayload,
          model_type: 'MT002', // Non-quota affecting change
        },
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      // Mock successful flow
      const mockGetExistingNco = jest.fn().mockResolvedValue(testNco);
      const mockGetStatusUpdateStatements = jest.fn().mockReturnValue({});
      const mockValidateNcoRequest = jest.fn().mockResolvedValue({ valid: true });
      const mockGetOrderType = jest.fn().mockResolvedValue({ is_customer_related: true });
      const mockSaveNcosWithAuditTrail = jest.fn().mockResolvedValue([testNco]);
      const mockNcoApiToDbObj = jest.fn().mockReturnValue(testNco);
      const mockNcoConfigDbToApiObj = jest.fn().mockReturnValue({});
      const mockUpdateKasConfiguration = jest.fn().mockReturnValue({});

      // Mock imports
      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
        getStatusUpdateStatementsFromOutboundMapping: mockGetStatusUpdateStatements,
        validateNcoRequest: mockValidateNcoRequest,
        saveNcosWithAuditTrail: mockSaveNcosWithAuditTrail,
        ncoApiToDbObj: mockNcoApiToDbObj,
        ncoConfigDbToApiObj: mockNcoConfigDbToApiObj,
      }));

      jest.doMock('../../../../utils/utils', () => ({
        getOrderType: mockGetOrderType,
      }));

      jest.doMock('../../../../utils/validation-helpers', () => ({
        getValidQuotaMonthForConfig: jest.fn().mockReturnValue({ from: '2024-01', until: '2024-12' }),
        isQuotaMonthInAllowedRange: jest.fn().mockReturnValue(true),
        updateKasConfigurationWithOptionAddedAtTimestamp: mockUpdateKasConfiguration,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(true);
      expect(mockGetExistingNco).toHaveBeenCalledWith(
        { ncoRepo: mockNcoRepo, ncoId: 'test-nco-id' },
        mockLogger
      );
      expect(mockValidateNcoRequest).toHaveBeenCalled();
      expect(mockSaveNcosWithAuditTrail).toHaveBeenCalled();
    });

    it('Should consume quota when relevant fields change', async () => {
      const testNco = {
        pk_new_car_order_id: 'test-nco-id',
        modified_at: '2024-01-01T00:00:00.000Z',
        model_year: '2024',
        model_type: 'MT001',
        configuration: {},
        ...orderData.baseNewCarOrder,
      };

      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: {
          ...orderData.updateNcoPayload,
          model_year: '2025', // Quota affecting change
        },
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      // Mock successful quota consumption
      mockQuotaApiAdapter.consumeQuota.mockResolvedValue(true);

      const mockGetExistingNco = jest.fn().mockResolvedValue(testNco);
      const mockGetStatusUpdateStatements = jest.fn().mockReturnValue({});
      const mockValidateNcoRequest = jest.fn().mockResolvedValue({ valid: true });
      const mockGetOrderType = jest.fn().mockResolvedValue({ is_customer_related: true });
      const mockSaveNcosWithAuditTrail = jest.fn().mockResolvedValue([testNco]);
      const mockNcoApiToDbObj = jest.fn().mockReturnValue(testNco);
      const mockNcoConfigDbToApiObj = jest.fn().mockReturnValue({});
      const mockUpdateKasConfiguration = jest.fn().mockReturnValue({});

      // Mock imports
      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
        getStatusUpdateStatementsFromOutboundMapping: mockGetStatusUpdateStatements,
        validateNcoRequest: mockValidateNcoRequest,
        saveNcosWithAuditTrail: mockSaveNcosWithAuditTrail,
        ncoApiToDbObj: mockNcoApiToDbObj,
        ncoConfigDbToApiObj: mockNcoConfigDbToApiObj,
      }));

      jest.doMock('../../../../utils/utils', () => ({
        getOrderType: mockGetOrderType,
      }));

      jest.doMock('../../../../utils/validation-helpers', () => ({
        getValidQuotaMonthForConfig: jest.fn().mockReturnValue({ from: '2024-01', until: '2025-12' }),
        isQuotaMonthInAllowedRange: jest.fn().mockReturnValue(true),
        updateKasConfigurationWithOptionAddedAtTimestamp: mockUpdateKasConfiguration,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(true);
      expect(mockQuotaApiAdapter.consumeQuota).toHaveBeenCalledWith(
        {
          nco: expect.objectContaining({ model_year: '2025' }),
          mockApi: true,
          correlationId: 'test-transaction',
        },
        mockLogger
      );
    });
  });

  describe('Expected failure scenarios', () => {
    it('Should fail when NCO is not found', async () => {
      const updateRequest: NcoUpdateRequest = {
        nco_id: 'non-existent-nco',
        payload: orderData.updateNcoPayload,
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      const mockGetExistingNco = jest.fn().mockResolvedValue(null);

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('expected');
      expect(result.errorMessage).toContain('Could not find old order with ncoid');
    });

    it('Should fail when NCO modified_at indicates racing condition', async () => {
      const testNco = {
        pk_new_car_order_id: 'test-nco-id',
        modified_at: '2024-01-01T01:00:00.000Z', // Different from request
        ...orderData.baseNewCarOrder,
      };

      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: orderData.updateNcoPayload,
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z', // Different from NCO
      };

      const mockGetExistingNco = jest.fn().mockResolvedValue(testNco);

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('expected');
      expect(result.errorMessage).toContain('Nco was changed by someone else');
    });

    it('Should fail when validation fails', async () => {
      const testNco = {
        pk_new_car_order_id: 'test-nco-id',
        modified_at: '2024-01-01T00:00:00.000Z',
        order_type: 'TEST_OT',
        ...orderData.baseNewCarOrder,
      };

      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: orderData.updateNcoPayload,
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      const mockGetExistingNco = jest.fn().mockResolvedValue(testNco);
      const mockGetStatusUpdateStatements = jest.fn().mockReturnValue({});
      const mockValidateNcoRequest = jest.fn().mockResolvedValue({ 
        valid: false, 
        error: 'Validation failed' 
      });
      const mockGetOrderType = jest.fn().mockResolvedValue({ is_customer_related: true });

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
        getStatusUpdateStatementsFromOutboundMapping: mockGetStatusUpdateStatements,
        validateNcoRequest: mockValidateNcoRequest,
      }));

      jest.doMock('../../../../utils/utils', () => ({
        getOrderType: mockGetOrderType,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('expected');
      expect(result.errorMessage).toContain('Custom validation failed with: Validation failed');
    });

    it('Should fail when quota is not available', async () => {
      const testNco = {
        pk_new_car_order_id: 'test-nco-id',
        modified_at: '2024-01-01T00:00:00.000Z',
        model_year: '2024',
        order_type: 'TEST_OT',
        configuration: {},
        ...orderData.baseNewCarOrder,
      };

      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: {
          ...orderData.updateNcoPayload,
          model_year: '2025', // Quota affecting change
        },
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      // Mock quota consumption failure
      mockQuotaApiAdapter.consumeQuota.mockResolvedValue(false);

      const mockGetExistingNco = jest.fn().mockResolvedValue(testNco);
      const mockGetStatusUpdateStatements = jest.fn().mockReturnValue({});
      const mockValidateNcoRequest = jest.fn().mockResolvedValue({ valid: true });
      const mockGetOrderType = jest.fn().mockResolvedValue({ is_customer_related: true });
      const mockNcoConfigDbToApiObj = jest.fn().mockReturnValue({});

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
        getStatusUpdateStatementsFromOutboundMapping: mockGetStatusUpdateStatements,
        validateNcoRequest: mockValidateNcoRequest,
        ncoConfigDbToApiObj: mockNcoConfigDbToApiObj,
      }));

      jest.doMock('../../../../utils/utils', () => ({
        getOrderType: mockGetOrderType,
      }));

      jest.doMock('../../../../utils/validation-helpers', () => ({
        getValidQuotaMonthForConfig: jest.fn().mockReturnValue({ from: '2024-01', until: '2025-12' }),
        isQuotaMonthInAllowedRange: jest.fn().mockReturnValue(true),
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('expected');
      expect(result.errorMessage).toContain('no quota available');
    });
  });

  describe('Unexpected failure scenarios', () => {
    it('Should handle database errors gracefully', async () => {
      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: orderData.updateNcoPayload,
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      const mockGetExistingNco = jest.fn().mockRejectedValue(new Error('Database connection failed'));

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('unexpected');
      expect(result.errorMessage).toContain('Unexpected database error');
    });

    it('Should handle concurrent update errors', async () => {
      const updateRequest: NcoUpdateRequest = {
        nco_id: 'test-nco-id',
        payload: orderData.updateNcoPayload,
        transaction_id: 'test-transaction',
        modified_at: '2024-01-01T00:00:00.000Z',
      };

      const concurrentUpdateError = new Error('could not serialize access due to concurrent update');
      (concurrentUpdateError as any).name = 'QueryFailedError';

      const mockGetExistingNco = jest.fn().mockRejectedValue(concurrentUpdateError);

      jest.doMock('../../../../utils/utils-typeorm', () => ({
        getExistingNco: mockGetExistingNco,
      }));

      const result = await processNcoUpdate(updateRequest, mockUserContext, mockContext, mockLogger);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('expected');
      expect(result.errorMessage).toContain('NCO was changed by someone else');
    });
  });
});
