import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { DataSource, EntityManager } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../../../../../lib/entities/model-type-visibility-model';
import { NcoConfigurationModel, NewCarOrderModel } from '../../../../../../lib/entities/new-car-order-model';
import { CoraMdOrderType } from '../../../../../../lib/types/masterdata-types';
import {
  InboundEventHandlerEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  UpdateNcoPayload,
} from '../../../../../../lib/types/process-steering-types';
import { QuotaApiAdapter } from '../../../../../utils/quota-api';
import {
  getExistingNco,
  ncoApiToDbObj,
  ncoConfigDbToApiObj,
  saveNcosWithAuditTrail,
  validateNcoRequest,
  StatusUpdateStatement,
} from '../../../../../utils/utils-typeorm';
import {
  getValidQuotaMonthForConfig,
  isQuotaMonthInAllowedRange,
  updateKasConfigurationWithOptionAddedAtTimestamp,
} from '../../../../../utils/validation-helpers';
import { NcoExportActionType } from '../../../../export-nco/types';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { KasAuthEndpointResponse } from '../../../../../../lib/types/kas-auth-types';
import { eventToNotification, unparseableEventToNotification } from '../../../../../utils/process-steering-helpers';
import { getEnvVarWithAssert, getOrderType } from '../../../../../utils/utils';
import {
  UnchangeableKeysNewCarOrderModel,
  unchangeableKeysNewCarOrderModel,
} from '../../../../../../lib/types/new-car-order-types';

export interface NcoUpdateContext {
  ncoDataSource: DataSource;
  dynamoDb: DynamoDBClient;
  quotaApiAdapter: QuotaApiAdapter;
  mockQuotaApi: boolean;
}

export interface NcoUpdateRequest {
  nco_id: string;
  payload: UpdateNcoPayload;
  transaction_id: string;
  modified_at: string;
}

export interface NcoUpdateResult {
  success: boolean;
  errorMessage?: string;
  errorType?: 'expected' | 'unexpected';
}

/**
 * Shared NCO update logic that can be used by both UPDATE_NCO and UPDATE_NCO_FROM_PI handlers
 */
export async function processNcoUpdate(
  updateRequest: NcoUpdateRequest,
  userContext: KasAuthEndpointResponse,
  context: NcoUpdateContext,
  logger: KasLambdaLogger,
  outboundStatusUpdateStatement: StatusUpdateStatement = {},
  applicationNameToAuthorize: string,
): Promise<NcoUpdateResult> {
  try {
    const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
    const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
    const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
    const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
    const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');
    const ncoValidationTables = {
      coraOrgRelationTableName,
      shippingCodeTableName,
      orderTypeTableName,
      importerTableName,
      dealerTableName,
    };
    const ncoRepo = context.ncoDataSource.getRepository(NewCarOrderModel);

    // Extract visibility level from user context
    const visibilityLevel = userContext.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    if (!visibilityLevel) {
      return {
        success: false,
        errorMessage: 'Failed to get the visibility level',
        errorType: 'expected',
      };
    }

    // Load the existing NCO
    const oldCarOrder = await getExistingNco({ ncoRepo, ncoId: updateRequest.nco_id }, logger);

    if (!oldCarOrder) {
      return {
        success: false,
        errorMessage: 'Could not find old order with ncoid',
        errorType: 'expected',
      };
    }

    // Compare supplied modified_at with nco modified_at and reject if they don't match
    if (new Date(oldCarOrder.modified_at ?? 0).toISOString() !== updateRequest.modified_at) {
      return {
        success: false,
        errorMessage: 'Nco was changed by someone else since the event was created',
        errorType: 'expected',
      };
    }

    // Note: outbound status update statements are handled by the calling event handler
    // This shared function focuses on the core NCO update logic

    // Prepare the new car order with unchanged keys preserved
    const oldCarOrder_only_disallowed_keys = unchangeableKeysNewCarOrderModel.reduce(
      (resObj: Partial<UnchangeableKeysNewCarOrderModel>, key) => {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        return ((resObj as Record<string, string | boolean | undefined | null>)[key] = oldCarOrder![key]), resObj;
      },
      {},
    ) as UnchangeableKeysNewCarOrderModel;

    const newCarOrder = {
      ...oldCarOrder, // Old Car Order as Base
      ...updateRequest.payload, // Overwrite with sent Values
      ...oldCarOrder_only_disallowed_keys, // disallow overwriting disallowed keys
    };

    // Validation logic
    const isScSame = oldCarOrder.shipping_code === newCarOrder.shipping_code;
    const isOtSame = oldCarOrder.order_type === newCarOrder.order_type;

    // Load old order type to prevent change from customer related OT to none customer related OT
    const oldOrderType: CoraMdOrderType | null = await getOrderType(
      context.dynamoDb,
      logger,
      orderTypeTableName,
      oldCarOrder.order_type,
    );

    if (!oldOrderType) {
      return {
        success: false,
        errorMessage: `Could not find old order type with code ${oldCarOrder.order_type}`,
        errorType: 'expected',
      };
    }

    // Validate all parameters based on user permissions
    const validationResult = await validateNcoRequest(
      {
        dynamoDb: context.dynamoDb,
        newCarOrder,
        ppnId: userContext.organizationId,
        visibilityLevel: visibilityLevel,
        tables: ncoValidationTables,
        customerRelatedOt: oldOrderType.is_customer_related ?? false,
        skipScDisabledCheck: isScSame,
        skipOtDisabledCheck: isOtSame,
        mtvRespoitory: context.ncoDataSource.getRepository(ModelTypeVisibilityModel),
      },
      logger,
    );

    if (!validationResult.valid) {
      return {
        success: false,
        errorMessage: `Custom validation failed with: ${validationResult.error}`,
        errorType: 'expected',
      };
    }

    // Check if we need to consume quota again because of relevant changes
    const consumeQuota =
      oldCarOrder.model_year !== newCarOrder.model_year ||
      oldCarOrder.model_type !== newCarOrder.model_type ||
      oldCarOrder.importer_number !== newCarOrder.importer_number ||
      oldCarOrder.dealer_number !== newCarOrder.dealer_number ||
      oldCarOrder.quota_month !== newCarOrder.quota_month;

    let couldConsumeQuota = true;
    if (consumeQuota) {
      // Check if quota is allowed
      const quotaAllowedRange = getValidQuotaMonthForConfig(
        { config: newCarOrder.configuration, old_config: ncoConfigDbToApiObj(oldCarOrder.configuration) },
        logger,
      );

      if (
        !isQuotaMonthInAllowedRange(
          { quotaMonth: newCarOrder.quota_month, from: quotaAllowedRange.from, until: quotaAllowedRange.until },
          logger,
        )
      ) {
        return {
          success: false,
          errorMessage: `quota_month not in valid range by config ${JSON.stringify(quotaAllowedRange)})`,
          errorType: 'expected',
        };
      }

      couldConsumeQuota = await context.quotaApiAdapter.consumeQuota(
        {
          nco: newCarOrder,
          mockApi: context.mockQuotaApi,
          correlationId: updateRequest.transaction_id,
        },
        logger,
      );
    }

    if (!couldConsumeQuota) {
      return {
        success: false,
        errorMessage: 'Error updating new car order, no quota available for the changes',
        errorType: 'expected',
      };
    }

    // Update the NCO
    const ncoInsertObj = ncoApiToDbObj(
      {
        ...newCarOrder,
        configuration: updateKasConfigurationWithOptionAddedAtTimestamp(
          { config: newCarOrder.configuration, old_config: ncoConfigDbToApiObj(oldCarOrder.configuration) },
          logger,
        ),
      },
      userContext.username,
      oldCarOrder,
    );

    await saveNcosWithAuditTrail(
      context.ncoDataSource,
      [updateRequest.nco_id],
      NcoExportActionType.UPDATE,
      async (transactionManager: EntityManager) => {
        await transactionManager.delete(NcoConfigurationModel, {
          fk_new_car_order_id: ncoInsertObj.pk_new_car_order_id,
        });

        const updatedNco = (await transactionManager.preload(NewCarOrderModel, {
          ...ncoInsertObj,
          ...outboundStatusUpdateStatement,
        })) as NewCarOrderModel;

        const ncoUpdateRes = await transactionManager.save(updatedNco);
        return [ncoUpdateRes];
      },
      logger,
      false,
    );

    logger.log(LogLevel.INFO, `NCO was updated successfully.`);
    return { success: true };
  } catch (error) {
    const err = error as { message: string; name: string };
    if (
      err.name === 'QueryFailedError' &&
      err.message.includes('could not serialize access due to concurrent update')
    ) {
      return {
        success: false,
        errorMessage: 'NCO was changed by someone else since the event was created',
        errorType: 'expected',
      };
    }

    logger.log(LogLevel.ERROR, 'Unexpected database error occurred during update', { data: error });
    return {
      success: false,
      errorMessage: 'Unexpected database error occurred during update',
      errorType: 'unexpected',
    };
  }
}

export async function transformEventNotifications<
  T extends InboundEventHandlerEvent & { messageId: string; errorMessage?: string },
>(
  events: {
    successfulEvents: T[];
    expectedFailedEvents: T[];
    unexpectedFailedEvents: T[];
    unParseableEvents: Partial<T>[];
  },
  eventType: OneVmsEventKey,
  sqsBatchResponse?: { batchItemFailures: Array<{ itemIdentifier: string; errorMessage?: string }> },
): Promise<NotificationKafkaEvent[]> {
  const { successfulEvents, expectedFailedEvents, unexpectedFailedEvents, unParseableEvents } = events;
  const successfulNotifications = successfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(e, eventType, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  // Handle unexpected failures - add to batch failures for retry and create notifications
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    if (sqsBatchResponse) {
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: e.messageId,
        errorMessage: e.errorMessage || 'Unknown error',
      });
    }
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  return [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];
}
