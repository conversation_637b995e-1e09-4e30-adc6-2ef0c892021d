import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Repository } from 'typeorm';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';

export interface PiValidationRequest {
  purchase_intention_id: string;
  pi_modified_at: string;
}

export interface PiValidationResult {
  success: boolean;
  errorMessage?: string;
  purchaseIntention?: CoraPurchaseIntentionModel;
}

/**
 * Validates a Purchase Intention for NCO update operations
 * - Checks if PI exists and is converted (is_converted: true)
 * - Validates racing conditions by comparing PI modified_at with event pi_modified_at
 */
export async function validatePurchaseIntention(
  request: PiValidationRequest,
  piRepository: Repository<CoraPurchaseIntentionModel>,
  logger: KasLambdaLogger,
): Promise<PiValidationResult> {
  try {
    // Fetch the Purchase Intention by ID and ensure it's converted
    const purchaseIntention = await piRepository.findOneBy({
      purchase_intention_id: request.purchase_intention_id,
      is_converted: true,
    });

    if (!purchaseIntention) {
      const message = `Could not find purchase intention with ID ${request.purchase_intention_id} or it is not converted`;
      logger.log(LogLevel.ERROR, message, { data: request });
      return {
        success: false,
        errorMessage: message,
      };
    }

    // Check for racing conditions by comparing modified_at timestamps
    if (purchaseIntention.modified_at && request.pi_modified_at) {
      const piModifiedAt = new Date(purchaseIntention.modified_at);
      const eventPiModifiedAt = new Date(request.pi_modified_at);

      if (piModifiedAt > eventPiModifiedAt) {
        const message = `Purchase intention was changed by someone else since the event was created. PI modified: ${piModifiedAt.toISOString()}, Event PI modified: ${eventPiModifiedAt.toISOString()}`;
        logger.log(LogLevel.ERROR, message, { data: request });
        return {
          success: false,
          errorMessage: message,
        };
      }
    }

    logger.log(LogLevel.INFO, `Purchase intention validation successful for ID: ${request.purchase_intention_id}`);
    return {
      success: true,
      purchaseIntention,
    };
  } catch (error) {
    const message = `Unexpected error during purchase intention validation: ${(error as Error).message}`;
    logger.log(LogLevel.ERROR, message, { data: { request, error } });
    return {
      success: false,
      errorMessage: message,
    };
  }
}
