import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSEvent, SQSHandler, SQSBatchResponse } from 'aws-lambda';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NcoConfigOrderedOptionsModel, NcoConfigurationModel, NewCarOrderModel } from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import { SOURCE_OBJ_TYPES } from '../../../../../lib/types/new-car-order-types';
import {
  InboundEventHandlerEventUpdateNcoFromPi,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
  UpdateNcoHandlerResult,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { QuotaApiAdapter } from '../../../../utils/quota-api';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import { processNcoUpdate as processSharedNcoUpdate, NcoUpdateContext, NcoUpdateRequest } from '../shared/update-nco-shared/update-nco-shared';
import { getStatusUpdateStatementsFromOutboundMapping } from '../../../../utils/utils-typeorm';

import { EventHandlerContext } from '../event-handler-context';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';

type UpdateNcoFromPiWithMessageId = InboundEventHandlerEventUpdateNcoFromPi & { messageId: string };
type UpdateNcoFromPiWithError = UpdateNcoFromPiWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventUpdateNcoFromPi>('InboundEventHandlerEventUpdateNcoFromPi');
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');

EventHandlerContext.init(
  OneVmsEventHandlerKey.UPDATE_NCO_FROM_PI,
  [
    NewCarOrderModel,
    NewCarOrderAuditTrailModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    ModelTypeVisibilityModel,
    CoraPurchaseIntentionModel,
  ],
  quotaApiSecretArn,
);

const allowMockQuotaApi = process.env.ALLOW_QUOTA_API_MOCK ?? 'false';

const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: EventHandlerContext.logger,
});

const updateNcoFromPiFunc = async (
  event: SQSEvent,
  _context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const successfulEvents: UpdateNcoFromPiWithMessageId[] = [];
  const unParseableEvents: Partial<UpdateNcoFromPiWithError>[] = [];
  const expectedFailedEvents: UpdateNcoFromPiWithError[] = [];
  const unexpectedFailedEvents: UpdateNcoFromPiWithError[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  const mockQuotaApi = allowMockQuotaApi === 'true' ? true : false;

  for (const record of event.Records) {
    let updateNcoFromPiEvent: UpdateNcoFromPiWithMessageId;
    try {
      const body = JSON.parse(record.body);
      const isValidSqsEvent = validateSqsEvent(body, logger);
      if (!isValidSqsEvent) {
        unParseableEvents.push({ messageId: record.messageId, errorMessage: 'Invalid SQS event structure' });
        continue;
      }
      updateNcoFromPiEvent = { ...body, messageId: record.messageId };
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Error parsing SQS record body', { data: { error, record } });
      unParseableEvents.push({ messageId: record.messageId, errorMessage: 'Failed to parse SQS record body' });
      continue;
    }

    try {
      // PI-specific validation: Check if PI exists and is converted
      const dataSource = await EventHandlerContext.ncoDataSource;
      const piRepo = dataSource.getRepository(CoraPurchaseIntentionModel);
      const purchaseIntention = await piRepo.findOneBy({
        purchase_intention_id: updateNcoFromPiEvent.payload.purchase_intention_id
      });

      if (!purchaseIntention) {
        const message = `Purchase Intention not found: ${updateNcoFromPiEvent.payload.purchase_intention_id}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }

      if (!purchaseIntention.is_converted) {
        const message = `Purchase Intention is not converted to NCO: ${updateNcoFromPiEvent.payload.purchase_intention_id}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }

      // Check if PI was modified since the event was created (racing condition check)
      if (purchaseIntention.modified_at && updateNcoFromPiEvent.payload.pi_modified_at) {
        const piModifiedAt = new Date(purchaseIntention.modified_at);
        const eventPiModifiedAt = new Date(updateNcoFromPiEvent.payload.pi_modified_at);

        if (piModifiedAt > eventPiModifiedAt) {
          const message = `Purchase Intention was modified after event creation. PI modified: ${piModifiedAt.toISOString()}, Event PI modified: ${eventPiModifiedAt.toISOString()}`;
          logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
          expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
          continue;
        }
      }

      // Find the corresponding NCO using source_obj_id and source_obj_type
      const ncoRepo = dataSource.getRepository(NewCarOrderModel);
      const nco = await ncoRepo.findOneBy({
        source_obj_id: updateNcoFromPiEvent.payload.purchase_intention_id,
        source_obj_type: SOURCE_OBJ_TYPES.PURCHASE_INTENTION
      });

      if (!nco) {
        const message = `NCO not found for Purchase Intention: ${updateNcoFromPiEvent.payload.purchase_intention_id}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }

      // Get outbound mappings from database
      const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
      const outboundEventMapping = outboundEventMappings.find(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
        (mapping) => mapping.event_result === UpdateNcoHandlerResult.SUCCESS,
      );
      // If there is no mapping, the status update statement will be empty but action is allowed
      let outboundStatusUpdateStatement = {};
      if (outboundEventMapping) {
        outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
      }

      // Prepare request for shared NCO update logic
      const ncoUpdateRequest: NcoUpdateRequest = {
        nco_id: nco.pk_new_car_order_id,
        payload: {
          ...updateNcoFromPiEvent.payload,
          // Remove PI-specific fields for core NCO processing
          purchase_intention_id: undefined,
          pi_modified_at: undefined,
        } as any,
        transaction_id: updateNcoFromPiEvent.transaction_id,
        modified_at: nco.modified_at!,
      };

      // Prepare NCO update context
      const ncoUpdateContext: NcoUpdateContext = {
        ncoDataSource: await EventHandlerContext.ncoDataSource,
        dynamoDb: EventHandlerContext.dynamoDb,
        ncoValidationTables: EventHandlerContext.ncoValidationTables,
        orderTypeTableName: EventHandlerContext.orderTypeTableName,
        quotaApiAdapter,
        mockQuotaApi,
      };

      // Delegate to shared NCO update logic
      const result = await processSharedNcoUpdate(ncoUpdateRequest, updateNcoFromPiEvent.user_auth_context, ncoUpdateContext, logger, outboundStatusUpdateStatement);

      if (result.success) {
        successfulEvents.push(updateNcoFromPiEvent);
      } else {
        if (result.errorType === 'expected') {
          expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: result.errorMessage! });
        } else {
          unexpectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: result.errorMessage! });
        }
      }

    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'NCO was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }
      logger.log(LogLevel.ERROR, 'Unexpected error processing UPDATE_NCO_FROM_PI event', { 
        data: { error: err, event: updateNcoFromPiEvent } 
      });
      unexpectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: err.message });
    }
  }

  // Handle notifications and batch responses (reuse existing logic)
  const notificationEvents = await transformEventNotifications({successfulEvents, expectedFailedEvents, unexpectedFailedEvents, unParseableEvents}, OneVmsEventKey.UPDATE_NCO_FROM_PI);
  
  // Unexpected fail events are not consumed and can be retried
    const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
      sqsBatchResponse.batchItemFailures.push({
        itemIdentifier: e.messageId,
        errorMessage: e.errorMessage,
      });
      return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
    });
    notificationEvents.push(...unexpectedNotifications);

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  return sqsBatchResponse;
};



// Handle event notifications (to be extracted to shared utility)


function validateSqsEvent(input: InboundEventHandlerEventUpdateNcoFromPi | undefined, logger: KasLambdaLogger): boolean {
  const [body_validated, validation_errors] = sqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, updateNcoFromPiFunc);
