import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSEvent, SQSHandler, SQSBatchResponse } from 'aws-lambda';
import { DataSource, Repository } from 'typeorm';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import { SOURCE_OBJ_TYPES } from '../../../../../lib/types/new-car-order-types';
import {
  InboundEventHandlerEventUpdateNcoFromPi,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
  UpdateNcoHandlerResult,
  UpdateNcoPayload,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { QuotaApiAdapter } from '../../../../utils/quota-api';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import {
  processNcoUpdate as processSharedNcoUpdate,
  NcoUpdateContext,
  NcoUpdateRequest,
  transformEventNotifications,
} from '../shared/update-nco-shared/update-nco-shared';
import { getStatusUpdateStatementsFromOutboundMapping, StatusUpdateStatement } from '../../../../utils/utils-typeorm';
import { eventToNotification } from '../../../../utils/process-steering-helpers';

import { EventHandlerContext } from '../event-handler-context';

type UpdateNcoFromPiWithMessageId = InboundEventHandlerEventUpdateNcoFromPi & { messageId: string };
type UpdateNcoFromPiWithError = UpdateNcoFromPiWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventUpdateNcoFromPi>(
  'InboundEventHandlerEventUpdateNcoFromPi',
);
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');

EventHandlerContext.init(
  OneVmsEventHandlerKey.UPDATE_NCO_FROM_PI,
  [
    NewCarOrderModel,
    NewCarOrderAuditTrailModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    ModelTypeVisibilityModel,
    CoraPurchaseIntentionModel,
  ],
  quotaApiSecretArn,
);

const allowMockQuotaApi = process.env.ALLOW_QUOTA_API_MOCK ?? 'false';

const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: EventHandlerContext.logger,
});

const updateNcoFromPiFunc = async (
  event: SQSEvent,
  _context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  //Remember fails and successes
  const successfulEvents: UpdateNcoFromPiWithMessageId[] = [];
  const unParseableEvents: Partial<UpdateNcoFromPiWithError>[] = [];
  const expectedFailedEvents: UpdateNcoFromPiWithError[] = [];
  const unexpectedFailedEvents: UpdateNcoFromPiWithError[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  const mockQuotaApi = allowMockQuotaApi === 'true' ? true : false;

  //Parse sqs events
  const updateNcoFromPiEvents = event.Records.map((record) => {
    try {
      const updateNcoFromPiEvent = JSON.parse(record.body) as InboundEventHandlerEventUpdateNcoFromPi | undefined;

      //Check if sqs content is valid
      const isValid = validateSqsEvent(updateNcoFromPiEvent, logger);
      if (!isValid) {
        const message = 'SQS record is not valid, skipping';
        logger.log(LogLevel.ERROR, message, {
          data: updateNcoFromPiEvent,
          correlationId: updateNcoFromPiEvent?.transaction_id,
        });
        unParseableEvents.push({
          ...updateNcoFromPiEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      return { ...updateNcoFromPiEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventUpdateNcoFromPi>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as UpdateNcoFromPiWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === UpdateNcoHandlerResult.SUCCESS,
    );
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
    }
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      updateNcoFromPiEvents.map((updateNcoFromPiEvent) =>
        eventToNotification(updateNcoFromPiEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: updateNcoFromPiEvents.map((updateNcoFromPiEvent) => ({
        itemIdentifier: updateNcoFromPiEvent.messageId,
        message,
      })),
    };
  }

  let piRepo: Repository<CoraPurchaseIntentionModel>;
  let ncoDataSource: DataSource;
  try {
    //create the typeorm datasource and repository obj for ncos and PIs
    ncoDataSource = await EventHandlerContext.getDataSource();
    piRepo = ncoDataSource.getRepository(CoraPurchaseIntentionModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      updateNcoFromPiEvents.map((updateNcoFromPiEvent) =>
        eventToNotification(updateNcoFromPiEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: updateNcoFromPiEvents.map((updateNcoFromPiEvent) => ({
        itemIdentifier: updateNcoFromPiEvent.messageId,
        message,
      })),
    };
  }

  // Prepare shared NCO update context (reused for all events)
  const ncoUpdateContext: NcoUpdateContext = {
    ncoDataSource,
    dynamoDb: EventHandlerContext.dynamoDb,
    quotaApiAdapter,
    mockQuotaApi,
  };

  //Process all events one by one
  for (const updateNcoFromPiEvent of updateNcoFromPiEvents) {
    logger.setObjectId(updateNcoFromPiEvent.payload.purchase_intention_id);

    try {
      // PI-specific validation: Check if PI exists and is converted
      const purchaseIntention = await piRepo.findOneBy({
        purchase_intention_id: updateNcoFromPiEvent.payload.purchase_intention_id,
        is_converted: true,
      });

      if (!purchaseIntention) {
        const message = `Purchase Intention not found: ${updateNcoFromPiEvent.payload.purchase_intention_id}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }

      // Check if PI was modified since the event was created (racing condition check)
      if (purchaseIntention.modified_at && updateNcoFromPiEvent.payload.pi_modified_at) {
        const piModifiedAt = new Date(purchaseIntention.modified_at);
        const eventPiModifiedAt = new Date(updateNcoFromPiEvent.payload.pi_modified_at);

        if (piModifiedAt > eventPiModifiedAt) {
          const message = `Purchase Intention was modified after event creation. PI modified: ${piModifiedAt.toISOString()}, Event PI modified: ${eventPiModifiedAt.toISOString()}`;
          logger.log(LogLevel.ERROR, message, { data: updateNcoFromPiEvent });
          expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
          continue;
        }
      }

      // Prepare request for shared NCO update logic
      const ncoUpdateRequest: NcoUpdateRequest = {
        nco_id: updateNcoFromPiEvent.nco_id,
        payload: {
          ...updateNcoFromPiEvent.payload,
        } as UpdateNcoPayload,
        transaction_id: updateNcoFromPiEvent.transaction_id,
        modified_at: updateNcoFromPiEvent.modified_at,
      };

      // Delegate to shared NCO update logic
      const result = await processSharedNcoUpdate(
        ncoUpdateRequest,
        updateNcoFromPiEvent.user_auth_context,
        ncoUpdateContext,
        logger,
        outboundStatusUpdateStatement,
        EventHandlerContext.applicationNameToAuthorize,
      );

      if (result.success) {
        successfulEvents.push(updateNcoFromPiEvent);
      } else {
        if (result.errorType === 'expected') {
          expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: result.errorMessage! });
        } else {
          unexpectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: result.errorMessage! });
        }
      }
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'NCO was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: message });
        continue;
      }
      logger.log(LogLevel.ERROR, 'Unexpected error processing UPDATE_NCO_FROM_PI event', {
        data: { error: err, event: updateNcoFromPiEvent },
      });
      unexpectedFailedEvents.push({ ...updateNcoFromPiEvent, errorMessage: err.message });
    }
  }

  // Handle notifications and batch responses using shared logic
  const notificationEvents = await transformEventNotifications<UpdateNcoFromPiWithMessageId>(
    { successfulEvents, expectedFailedEvents, unexpectedFailedEvents, unParseableEvents },
    OneVmsEventKey.UPDATE_NCO_FROM_PI,
    sqsBatchResponse,
  );

  // Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  return sqsBatchResponse;
};

function validateSqsEvent(
  input: InboundEventHandlerEventUpdateNcoFromPi | undefined,
  logger: KasLambdaLogger,
): boolean {
  const [body_validated, validation_errors] = sqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, updateNcoFromPiFunc);
