import { DataSource, In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraMdDealer, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import { CoraNCOBaseApiRequest } from '../../../../../lib/types/new-car-order-types';
import {
  InboundEventHandlerEventUpdateNcoFromPi,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
  UpdateNcoFromPiPayload,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createInboundEvent,
  createSqsEvent,
  createStandartOrgRelPattern,
  initDataSourceForIntTest,
  invokeGenericLambda,
  pollNotifications,
  prepareDynamodb,
} from '../../../../utils/integration-test-helpers';
import { ncoApiToDbObj } from '../../../../utils/utils-typeorm';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.UPDATE_NCO_FROM_PI}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('update-nco-from-pi');
const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;

//model type visibility test data
const mtvImpInPast = {
  //For IMP visible date in past
  importer_number: mdDealer.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvs: ModelTypeVisibilityModel[] = [mtvImpInPast];

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

//auth context apps test data
const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

// Purchase Intention test data
const purchaseIntention: CoraPurchaseIntentionModel = {
  purchase_intention_id: 'ITPIXXXXXX',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  quota_month: '2023-11',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc1',
  created_by: OneVmsSourceSystemKey.PVMS,
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  vehicle_status_code: 'V070',
  vehicle_configuration_pvmsnext: { testProp: 'Test' },
  vehicle_configuration_onevms: null,
  seller: '123456',
  business_partner_id: '0020000866',
  is_converted: true,
  has_change_request: true,
};

const oldCarOrderObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITPIXXXXXX', // Same as PI ID for converted PI
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  modified_at: '2025-04-09T14:45:28.203Z',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  order_status_onevms_timestamp_last_change: '2024-12-06T10:21:02.876Z',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: {
    created_by: 'IntegrationTester',
    modified_by: 'IntegrationTester',
    ordered_options: [
      {
        created_by: 'IntegrationTester',
        modified_by: 'IntegrationTester',
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [],
      },
    ],
    technical_options: null,
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

//updated nco request obj from PI
const updateNcoFromPiPayload: UpdateNcoFromPiPayload = {
  pi_modified_at: '2025-04-09T14:45:28.203Z',
  pk_new_car_order_id: 'ITPIXXXXXX',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otCustomerRel.pk_order_type,
  quota_month: '2024-12',
  requested_dealer_delivery_date: '2025-01-01',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc4',
  modified_at: '2022-12-06T10:21:02.876Z',
  modified_by: 'IntegrationTester',
  configuration_signature: 'mydummysignature',
  cnr: 'C23',
  configuration: {
    ordered_options: [
      {
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1_CHANGED',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1_CHANGED',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [],
      },
    ],
  },
  configuration_expire: null,
};

const defaultUpdateNcoFromPiSqsEvent: InboundEventHandlerEventUpdateNcoFromPi = {
  event_type: OneVmsEventKey.UPDATE_NCO_FROM_PI,
  transaction_id: uuidv4(),
  sub_transaction_id: uuidv4(),
  action_at: new Date().toISOString(),
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  nco_id: oldCarOrderObject.pk_new_car_order_id,
  modified_at: oldCarOrderObject.modified_at!,
  payload: {...updateNcoFromPiPayload, purchase_intention_id: 'ITPIXXXXXX'},
  source_system: OneVmsSourceSystemKey.CORA,
} satisfies InboundEventHandlerEventUpdateNcoFromPi;

let dataSource: DataSource;
let ncoRepository: Repository<NewCarOrderModel>;
let piRepository: Repository<CoraPurchaseIntentionModel>;

function deepcopyNcoEntity(oldNco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(oldNco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', oldNco);
}

let testMtvs: ModelTypeVisibilityModel[] = [];
let savedPi: CoraPurchaseIntentionModel;

beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    ModelTypeVisibilityModel,
    CoraPurchaseIntentionModel,
  ]);
  ncoRepository = dataSource.getRepository(NewCarOrderModel);
  piRepository = dataSource.getRepository(CoraPurchaseIntentionModel);
  const mtvRepository = dataSource.getRepository(ModelTypeVisibilityModel);
  
  testMtvs = await mtvRepository.save(mtvs);
  savedPi = await piRepository.save(purchaseIntention);
  const oldCarOrderObjectRes = await ncoRepository.save(deepcopyNcoEntity(oldCarOrderObject));

  //update modified_at fields because valid values are needed in request body
  oldCarOrderObject.modified_at = oldCarOrderObjectRes.modified_at;
  updateNcoFromPiPayload.modified_at = oldCarOrderObjectRes.modified_at;
  updateNcoFromPiPayload.pi_modified_at = savedPi.modified_at!;

  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDlrTableName, objs: [mdDealer] },
    { tableName: mdImpTableName, objs: [mdImporter] },
    { tableName: mdScTableName, objs: Object.values(scs) },
    { tableName: mdOtTableName, objs: Object.values(ots) },
  ]);
}, 120000);

afterAll(async () => {
  await ncoRepository.delete({
    pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id,
  });
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id,
  });
  await piRepository.delete({
    purchase_intention_id: purchaseIntention.purchase_intention_id,
  });
  await dataSource.getRepository(ModelTypeVisibilityModel).remove(testMtvs);
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDlrTableName,
      pks: [{ pk_importer_number: mdDealer.pk_importer_number, sk_dealer_number: mdDealer.sk_dealer_number }],
    },
    {
      tableName: mdImpTableName,
      pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
    },
    {
      tableName: mdScTableName,
      pks: scPks,
    },
    {
      tableName: mdOtTableName,
      pks: otPks,
    },
  ]);
}, 120000);

it('Should NOT update NCO from PI and return fail if input is missing required props', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('SQS record is not valid');
});

it('Should NOT update NCO from PI and return fail if auth context is faulty', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      user_auth_context: {
        ...eventPayload.user_auth_context,
        kasApplications: appsWithNoRole,
      },
    },
  ]);
  const ncoDb = await ncoRepository.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to get the visibility level');

  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update NCO from PI and return fail if PI cannot be found', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const modifiedPayload = {
    ...updateNcoFromPiPayload,
    purchase_intention_id: 'NOT_VALID_PI_ID',
  };
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: modifiedPayload,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Could not find purchase intention');
});

it('Should NOT update NCO from PI and return fail if PI modified_at does not match', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const modifiedPayload = {
    ...updateNcoFromPiPayload,
    pi_modified_at: new Date().toISOString(),
  };
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: modifiedPayload,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Purchase intention was changed by someone else');
});

it('Should NOT update NCO from PI and return fail if PI is not a change request', async () => {
  // Create a PI without change_request flag
  const nonChangeRequestPi = { ...purchaseIntention, purchase_intention_id: 'ITPI_NO_CR', change_request: false };
  await piRepository.save(nonChangeRequestPi);

  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const modifiedPayload = {
    ...updateNcoFromPiPayload,
    purchase_intention_id: 'ITPI_NO_CR',
  };
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: modifiedPayload,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Purchase intention is not a change request');

  // Cleanup
  await piRepository.delete({ purchase_intention_id: 'ITPI_NO_CR' });
});

it('Should NOT update NCO from PI and return fail if NCO cannot be found', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      nco_id: 'NOT_VALID_NCOID',
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Could not find old order with ncoid');
});

it('Should NOT update NCO from PI and return fail if NCO modified_at does not match', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('changed by someone else');
});

it('Should NOT update NCO from PI when updating to a invalid quota_month', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...updateNcoFromPiPayload,
        quota_month: '2999-01',
        requested_dealer_delivery_date: '2999-02-02',
      },
    },
  ]);

  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Error updating new car order, no quota available for the changes');
});

it('Should successfully update NCO from PI change request', async () => {
  const eventPayload = createInboundEvent(defaultUpdateNcoFromPiSqsEvent);
  const event = createSqsEvent([eventPayload]);

  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toEqual([]);

  const ncoDb = await ncoRepository.findOne({
    where: { pk_new_car_order_id: updateNcoFromPiPayload.pk_new_car_order_id },
    relations: [
      'configuration',
      'configuration.ordered_options',
      'configuration.ordered_options.content',
    ],
  });

  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.requested_dealer_delivery_date).toEqual('2025-01-01');
  expect(ncoDb?.quota_month).toEqual('2024-12');
  expect(ncoDb?.receiving_port_code).toEqual('ItNcoPc4');
  expect(ncoDb?.configuration?.ordered_options?.[0]?.option_type).toEqual('OPTIONTYPE1_CHANGED');
  expect(ncoDb?.configuration?.ordered_options?.[0]?.referenced_package_type).toEqual('RPTYPE1_CHANGED');
});
