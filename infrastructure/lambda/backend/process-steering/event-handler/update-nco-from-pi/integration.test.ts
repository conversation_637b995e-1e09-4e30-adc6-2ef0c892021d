import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { DataSource, Repository } from 'typeorm';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createTypeORMDataSource } from '../../../../config/typeorm-config';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import {
  InboundEventHandlerEventUpdateNcoFromPi,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandler<PERSON>ey } from '../../../../../lib/utils/constants';
import { EventHandlerContext } from '../event-handler-context';
import { getEnvVarWithAssert } from '../../../../utils/utils';
import { initDataSourceForIntTest } from '../../../../utils/integration-test-helpers';
import { setupMocks } from '../../../../utils/test-utils';

// Test configuration
const LAMBDA_ARN = getEnvVarWithAssert('UPDATE_NCO_FROM_PI_LAMBDA_ARN');
const INTEGRATION_TEST_EVENT_KEY = 'INTEGRATION_TEST';

describe('UPDATE_NCO_FROM_PI Integration Tests', () => {
  let dataSource: DataSource;
  let ncoRepository: any;
  let piRepository: Repository<CoraPurchaseIntentionModel>;
  let logger: KasLambdaLogger;
  let testNco: NewCarOrderModel;
  let testPi: CoraPurchaseIntentionModel;

  beforeAll(async () => {
    logger = new KasLambdaLogger('update-nco-from-pi-integration-test', LogLevel.TRACE);

    // Initialize data source
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      CoraPurchaseIntentionModel,
    ]);

    ncoRepository = dataSource.getRepository(NewCarOrderModel);
    piRepository = dataSource.getRepository(CoraPurchaseIntentionModel);
  });

  beforeEach(async () => {
    // Create test PI
    testPi = piRepository.create({
      purchase_intention_id: uuidv4(),
      is_converted: true,
      modified_at: new Date().toISOString(),
      created_by: 'integration-test',
      modified_by: 'integration-test',
      ...orderData.basePurchaseIntention,
    });
    await piRepository.save(testPi);

    // Create test NCO linked to PI
    testNco = ncoRepository.create({
      pk_new_car_order_id: uuidv4(),
      source_obj_id: testPi.purchase_intention_id,
      source_obj_type: SOURCE_OBJ_TYPES.PURCHASE_INTENTION,
      modified_at: new Date().toISOString(),
      created_by: 'integration-test',
      modified_by: 'integration-test',
      ...orderData.baseNewCarOrder,
    });
    await ncoRepository.save(testNco);
  });

  afterEach(async () => {
    // Clean up test data
    if (testNco) {
      await ncoRepository.delete({ pk_new_car_order_id: testNco.pk_new_car_order_id });
    }
    if (testPi) {
      await piRepository.delete({ purchase_intention_id: testPi.purchase_intention_id });
    }
  });

  afterAll(async () => {
    if (dataSource?.isInitialized) {
      await dataSource.destroy();
    }
  });

  describe('Successful UPDATE_NCO_FROM_PI scenarios', () => {
    it('Should successfully update NCO from PI with valid data', async () => {
      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
        model_type: 'UPDATED_MODEL',
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toEqual([]);

      // Verify NCO was updated
      const updatedNco = await ncoRepository.findOne({
        where: { pk_new_car_order_id: testNco.pk_new_car_order_id },
      });
      expect(updatedNco.model_type).toBe('UPDATED_MODEL');
    });

    it('Should handle quota consumption when relevant fields change', async () => {
      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
        model_year: '2025', // Change that triggers quota consumption
        quota_month: '2024-12',
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toEqual([]);

      // Verify NCO was updated
      const updatedNco = await ncoRepository.findOne({
        where: { pk_new_car_order_id: testNco.pk_new_car_order_id },
      });
      expect(updatedNco.model_year).toBe('2025');
    });
  });

  describe('PI-specific validation failures', () => {
    it('Should fail when PI does not exist', async () => {
      const nonExistentPiId = uuidv4();
      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: nonExistentPiId,
        pi_modified_at: new Date().toISOString(),
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('Purchase Intention not found');
    });

    it('Should fail when PI is not converted', async () => {
      // Update PI to not converted
      testPi.is_converted = false;
      await piRepository.save(testPi);

      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('Purchase Intention is not converted to NCO');
    });

    it('Should fail when PI modified_at timestamp indicates racing condition', async () => {
      const oldTimestamp = new Date(Date.now() - 60000).toISOString(); // 1 minute ago

      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: oldTimestamp, // Outdated timestamp
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('Purchase Intention was changed by someone else');
    });

    it('Should fail when NCO is not found for the PI', async () => {
      // Delete the NCO but keep the PI
      await ncoRepository.delete({ pk_new_car_order_id: testNco.pk_new_car_order_id });

      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('NCO not found for Purchase Intention');
    });
  });

  describe('Shared NCO update logic validation', () => {
    it('Should fail when NCO modified_at indicates racing condition', async () => {
      const oldNcoTimestamp = new Date(Date.now() - 60000).toISOString();

      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: oldNcoTimestamp, // Outdated NCO timestamp
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('Nco was changed by someone else');
    });

    it('Should fail when quota is not available', async () => {
      // This test would require mocking the quota API to return false
      // Implementation depends on how quota mocking is set up in the test environment
      const updatePayload = {
        ...orderData.updateNcoPayload,
        purchase_intention_id: testPi.purchase_intention_id,
        pi_modified_at: testPi.modified_at,
        model_year: '2025', // Change that triggers quota consumption
        quota_month: '2099-12', // Future quota month that should fail
      };

      const eventPayload = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: updatePayload,
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([eventPayload]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      // This test may pass or fail depending on quota API mock configuration
      // The important thing is that it doesn't crash
      expect(response).toBeDefined();
    });
  });

  describe('Batch processing', () => {
    it('Should handle mixed success and failure events in a batch', async () => {
      // Create a second PI and NCO for testing
      const testPi2 = piRepository.create({
        purchase_intention_id: uuidv4(),
        is_converted: false, // This will cause failure
        modified_at: new Date().toISOString(),
        created_by: 'integration-test',
        modified_by: 'integration-test',
        ...orderData.basePurchaseIntention,
      });
      await piRepository.save(testPi2);

      const testNco2 = ncoRepository.create({
        pk_new_car_order_id: uuidv4(),
        source_obj_id: testPi2.purchase_intention_id,
        source_obj_type: SOURCE_OBJ_TYPES.PURCHASE_INTENTION,
        modified_at: new Date().toISOString(),
        created_by: 'integration-test',
        modified_by: 'integration-test',
        ...orderData.baseNewCarOrder,
      });
      await ncoRepository.save(testNco2);

      const successEvent = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: {
          ...orderData.updateNcoPayload,
          purchase_intention_id: testPi.purchase_intention_id,
          pi_modified_at: testPi.modified_at,
        },
        nco_id: testNco.pk_new_car_order_id,
        modified_at: testNco.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const failureEvent = createInboundEvent({
        event_type: 'update-nco-from-pi',
        payload: {
          ...orderData.updateNcoPayload,
          purchase_intention_id: testPi2.purchase_intention_id,
          pi_modified_at: testPi2.modified_at,
        },
        nco_id: testNco2.pk_new_car_order_id,
        modified_at: testNco2.modified_at!,
        onevmseventkey: INTEGRATION_TEST_EVENT_KEY,
      });

      const sqsEvent = createSqsEvent([successEvent, failureEvent]);
      const response = await invokeGenericLambda<SQSBatchResponseWithError>(LAMBDA_ARN, sqsEvent);

      expect(response.batchItemFailures).toHaveLength(1);
      expect(response.batchItemFailures[0].message).toContain('Purchase Intention is not converted');

      // Clean up
      await ncoRepository.delete({ pk_new_car_order_id: testNco2.pk_new_car_order_id });
      await piRepository.delete({ purchase_intention_id: testPi2.purchase_intention_id });
    });
  });
});
