import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Kas<PERSON>ambdaLog<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraPurchaseIntentionModel } from '../../../../../lib/entities/purchase-intention-model';
import { CoraMdOrderType } from '../../../../../lib/types/masterdata-types';
import {
  UnchangeableKeysNewCarOrderModel,
  unchangeableKeysNewCarOrderModel,
} from '../../../../../lib/types/new-car-order-types';
import {
  InboundEventHandlerEventUpdateNco,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
  UpdateNcoHandlerResult,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { eventToNotification } from '../../../../utils/process-steering-helpers';
import { QuotaApiAdapter } from '../../../../utils/quota-api';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { getEnvVarWithAssert, getOrderType } from '../../../../utils/utils';
import {
  getExistingNco,
  getStatusUpdateStatementsFromOutboundMapping,
  ncoApiToDbObj,
  ncoConfigDbToApiObj,
  saveNcosWithAuditTrail,
  validateNcoRequest,
} from '../../../../utils/utils-typeorm';
import {
  getValidQuotaMonthForConfig,
  isQuotaMonthInAllowedRange,
  updateKasConfigurationWithOptionAddedAtTimestamp,
} from '../../../../utils/validation-helpers';
import { NcoExportActionType } from '../../../export-nco/types';
import { EventHandlerContext } from '../event-handler-context';
import { validatePurchaseIntention } from '../shared/pi-validation';

type UpdateNcoWithMessageId = InboundEventHandlerEventUpdateNco & { messageId: string };
type UpdateNcoWithError = UpdateNcoWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventUpdateNco>('InboundEventHandlerEventUpdateNco');
const quotaApiSecretArn = getEnvVarWithAssert('QUOTA_API_SECRET_ARN');
EventHandlerContext.init(
  OneVmsEventHandlerKey.UPDATE_NCO,
  [
    NewCarOrderModel,
    NewCarOrderAuditTrailModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    ModelTypeVisibilityModel,
    CoraPurchaseIntentionModel,
  ],
  quotaApiSecretArn,
);
const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });

// Table name environment variables
const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');
const ncoValidationTables = {
  coraOrgRelationTableName,
  shippingCodeTableName,
  orderTypeTableName,
  importerTableName,
  dealerTableName,
};
const allowMockQuotaApi = process.env.ALLOW_QUOTA_API_MOCK ?? 'false';

const quotaApiAdapter = new QuotaApiAdapter({
  quotaApiSecretArn: quotaApiSecretArn,
  logger: EventHandlerContext.logger,
});
const updateNcoFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  //Remember fails and successes
  const successfulEvents: UpdateNcoWithMessageId[] = [];
  const unParseableEvents: Partial<UpdateNcoWithError>[] = [];
  const expectedFailedEvents: UpdateNcoWithError[] = [];
  const unexpectedFailedEvents: UpdateNcoWithError[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  const mockQuotaApi = allowMockQuotaApi === 'true' ? true : false;

  //Parse sqs events
  const updateNcoEvents = event.Records.map((record) => {
    try {
      const updateNcoEvent = JSON.parse(record.body) as InboundEventHandlerEventUpdateNco | undefined;

      //Check if sqs content is valid
      const isValid = validateSqsEvent(updateNcoEvent, logger);
      if (!isValid) {
        const message = 'SQS record is not valid, skipping';
        logger.log(LogLevel.ERROR, message, {
          data: updateNcoEvent,
          correlationId: updateNcoEvent?.transaction_id,
        });
        unParseableEvents.push({
          ...updateNcoEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      return { ...updateNcoEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventUpdateNco>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as UpdateNcoWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: Omit<QueryDeepPartialEntity<NewCarOrderModel>, 'pk_new_car_order_id'> = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === UpdateNcoHandlerResult.SUCCESS,
    );
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
    }
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      updateNcoEvents.map((updateNcoEvent) =>
        eventToNotification(updateNcoEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: updateNcoEvents.map((updateNcoEvent) => ({
        itemIdentifier: updateNcoEvent.messageId,
        message,
      })),
    };
  }

  let ncoRepo: Repository<NewCarOrderModel>;
  let ncoDataSource: DataSource;
  try {
    //create the typeorm datasource and repository obj for ncos
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      updateNcoEvents.map((updateNcoEvent) =>
        eventToNotification(updateNcoEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: updateNcoEvents.map((updateNcoEvent) => ({
        itemIdentifier: updateNcoEvent.messageId,
        message,
      })),
    };
  }

  //Process all events one by one
  for (const updateNcoEvent of updateNcoEvents) {
    logger.setObjectId(updateNcoEvent.nco_id);
    const userAttributes = updateNcoEvent.user_auth_context;

    // Extract necessary attributes from auth context
    const visibilityLevel =
      userAttributes.kasApplications[EventHandlerContext.applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    const ppnId = userAttributes.organizationId;

    if (!visibilityLevel) {
      const message = 'Failed to get the visibility level';
      logger.log(LogLevel.ERROR, message, { data: event });
      expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
      continue;
    }

    try {
      //load the old Order
      const oldCarOrder = await getExistingNco({ ncoRepo: ncoRepo, ncoId: updateNcoEvent.nco_id }, logger);

      if (!oldCarOrder) {
        const message = 'Could not find old order with ncoid';
        logger.log(LogLevel.ERROR, message, { data: updateNcoEvent });
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }

      //compare suplied modified_at with nco modified_at and reject the action if they do not match
      if (new Date(oldCarOrder.modified_at ?? 0).toISOString() !== updateNcoEvent.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        logger.log(LogLevel.ERROR, message, { data: { oldCarOrder, updateNcoEvent } });
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }

      // Conditional PI validation - only run if purchase_intention_id is present
      if (updateNcoEvent.payload.purchase_intention_id && updateNcoEvent.payload.pi_modified_at) {
        const piRepo = ncoDataSource.getRepository(CoraPurchaseIntentionModel);
        const piValidationResult = await validatePurchaseIntention(
          {
            purchase_intention_id: updateNcoEvent.payload.purchase_intention_id,
            pi_modified_at: updateNcoEvent.payload.pi_modified_at,
          },
          piRepo,
          logger,
        );

        if (!piValidationResult.success) {
          expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: piValidationResult.errorMessage! });
          continue;
        }
      }

      const oldCarOrder_only_disallowed_keys = unchangeableKeysNewCarOrderModel.reduce(
        (resObj: Partial<UnchangeableKeysNewCarOrderModel>, key) => {
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
          return ((resObj as Record<string, string | boolean | undefined | null>)[key] = oldCarOrder![key]), resObj;
        },
        {},
      ) as UnchangeableKeysNewCarOrderModel;
      //remove all properties that are not in schema
      const newCarOrder = {
        ...oldCarOrder, // Old Car Order as Base
        ...updateNcoEvent.payload, // Overwrite with sent Values (will overwrite if explicit undefined is set for a key)
        ...oldCarOrder_only_disallowed_keys, // disallow overwriting disallowed keys
      };

      //ignore some params in validation -> prevent validation err on old disabled data that was not changed
      const isScSame = oldCarOrder.shipping_code === newCarOrder.shipping_code;
      const isOtSame = oldCarOrder.order_type === newCarOrder.order_type;

      //load old OT to prevent change from customer related OT to none customer related OT
      const oldOrderType: CoraMdOrderType | null = await getOrderType(
        dynamoDb,
        logger,
        orderTypeTableName,
        oldCarOrder.order_type,
      );
      if (!oldOrderType) {
        const message = `Could not find old order type with code ${oldCarOrder.order_type}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoEvent });
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }

      //validate all parameters based on users permissions
      const _res = await validateNcoRequest(
        {
          dynamoDb,
          newCarOrder,
          ppnId,
          visibilityLevel,
          tables: ncoValidationTables,
          customerRelatedOt: oldOrderType.is_customer_related ?? false,
          skipScDisabledCheck: isScSame,
          skipOtDisabledCheck: isOtSame,
          mtvRespoitory: ncoDataSource.getRepository(ModelTypeVisibilityModel),
        },
        logger,
      );
      if (!_res.valid) {
        const message = `Custom validation failed with: ${_res.error}`;
        logger.log(LogLevel.ERROR, message, { data: updateNcoEvent });
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }

      //find out if we need to consume quota again because of relevant changes
      //only quota_month can be changed atm, this is just for future proveness
      const consumeQuota =
        oldCarOrder.model_year !== newCarOrder.model_year ||
        oldCarOrder.model_type !== newCarOrder.model_type ||
        oldCarOrder.importer_number !== newCarOrder.importer_number ||
        oldCarOrder.dealer_number !== newCarOrder.dealer_number ||
        oldCarOrder.quota_month !== newCarOrder.quota_month;

      let couldConsumeQuota = true;
      if (consumeQuota) {
        // Check if quota is allowed
        const quotaAllowedRange = getValidQuotaMonthForConfig(
          { config: newCarOrder.configuration, old_config: ncoConfigDbToApiObj(oldCarOrder.configuration) },
          logger,
        );
        if (
          !isQuotaMonthInAllowedRange(
            { quotaMonth: newCarOrder.quota_month, from: quotaAllowedRange.from, until: quotaAllowedRange.until },
            logger,
          )
        ) {
          const message = `quota_month not in valid range by config ${JSON.stringify(quotaAllowedRange)})`;
          logger.log(LogLevel.ERROR, message, { data: updateNcoEvent });
          expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
          continue;
        }
        couldConsumeQuota = await quotaApiAdapter.consumeQuota(
          {
            nco: newCarOrder,
            mockApi: mockQuotaApi,
            correlationId: updateNcoEvent.transaction_id,
          },
          logger,
        );
      }
      if (couldConsumeQuota) {
        const userName = userAttributes.username;
        const ncoInsertObj = ncoApiToDbObj(
          {
            ...newCarOrder,
            configuration: updateKasConfigurationWithOptionAddedAtTimestamp(
              { config: newCarOrder.configuration, old_config: ncoConfigDbToApiObj(oldCarOrder.configuration) },
              logger,
            ),
          },
          userName,
          oldCarOrder,
        );
        await saveNcosWithAuditTrail(
          ncoDataSource,
          [updateNcoEvent.nco_id],
          NcoExportActionType.UPDATE,
          async (transactionManager: EntityManager) => {
            await transactionManager.delete(NcoConfigurationModel, {
              fk_new_car_order_id: ncoInsertObj.pk_new_car_order_id,
            });

            const updatedNco = (await transactionManager.preload(NewCarOrderModel, {
              ...ncoInsertObj,
              ...outboundStatusUpdateStatement,
            })) as NewCarOrderModel;
            const ncoUpdateRes = await transactionManager.save(updatedNco);
            return [ncoUpdateRes];
          },
          logger,
          false,
        );
        logger.log(LogLevel.INFO, `Nco was updated successfully.`);
        successfulEvents.push(updateNcoEvent);
      } else {
        const message = 'Error updating new car order, no quota available for the changes';
        logger.log(LogLevel.ERROR, message, { data: updateNcoEvent });
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during update';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...updateNcoEvent, errorMessage: message });
      continue;
    }
  }
  // Handle notifications and batch responses using shared logic
  const notificationEvents = await EventHandlerContext.transformEventNotifications(
    { successfulEvents, expectedFailedEvents, unexpectedFailedEvents, unParseableEvents },
    OneVmsEventKey.UPDATE_NCO,
  );

  // Unexpected fail events are not consumed and can be retried
  unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
  });

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

function validateSqsEvent(input: InboundEventHandlerEventUpdateNco | undefined, logger: KasLambdaLogger): boolean {
  const [body_validated, validation_errors] = sqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, updateNcoFunc);
