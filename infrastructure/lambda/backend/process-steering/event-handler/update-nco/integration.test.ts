import { DataSource, In, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { ModelTypeVisibilityModel } from '../../../../../lib/entities/model-type-visibility-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraMdDealer, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import { CoraNCOBaseApiRequest } from '../../../../../lib/types/new-car-order-types';
import {
  InboundEventHandlerEventUpdateNco,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SQSBatchResponseWithError,
  UpdateNcoPayload,
} from '../../../../../lib/types/process-steering-types';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createInboundEvent,
  createSqsEvent,
  createStandartOrgRelPattern,
  initDataSourceForIntTest,
  invokeGenericLambda,
  pollNotifications,
  prepareDynamodb,
} from '../../../../utils/integration-test-helpers';
import { ncoApiToDbObj } from '../../../../utils/utils-typeorm';
const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.UPDATE_NCO}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const mdScTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
const mdOtTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('update-nco');
const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;
//model type visibility test data
const mtvImpInPast = {
  //For IMP visible date in past
  importer_number: mdDealer.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvImpInFuture = {
  //For IMP visible date in future
  importer_number: mdDealer.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0002',
  my4: '2030',
  role: 'IMP',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvDlrInPast = {
  //For DLR visible date in past
  importer_number: mdDealer.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0001',
  my4: '2030',
  role: 'DLR',
  valid_from: '2017-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};
const mtvDlrInFuture = {
  //For DLR visible date in future
  importer_number: mdDealer.pk_importer_number,
  cnr: 'C23',
  model_type: 'MT0002',
  my4: '2030',
  role: 'DLR',
  valid_from: '5000-03-14',
  created_by: 'INT_TEST',
  modified_by: 'INT_TEST',
};

const mtvs: ModelTypeVisibilityModel[] = [mtvImpInPast, mtvImpInFuture, mtvDlrInPast, mtvDlrInFuture];

const scPks = Object.values(scs).map((sc) => ({
  pk_shipping_code: sc.pk_shipping_code,
}));

const otPks = Object.values(ots).map((ot) => ({
  pk_order_type: ot.pk_order_type,
}));

//auth context apps test data
const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const oldCarOrderObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITXXXXXX',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otNoCustomerRel.pk_order_type,
  modified_at: '2025-04-09T14:45:28.203Z',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoUpdDataPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  order_status_onevms_timestamp_last_change: '2024-12-06T10:21:02.876Z',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: {
    created_by: 'IntegrationTester',
    modified_by: 'IntegrationTester',
    ordered_options: [
      {
        created_by: 'IntegrationTester',
        modified_by: 'IntegrationTester',
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            created_by: 'IntegrationTester',
            modified_by: 'IntegrationTester',
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
    technical_options: null,
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};
const oldCarOrderCustomerRelatedObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITYYYYYY',
  dealer_number: 'ItNcoUpdDlr',
  importer_code: 'IT',
  importer_number: 'ItNcoUpdImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'ItOTCustomerRel1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'ItScUpdBlackAllow',
  receiving_port_code: 'ItNcoUpdPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  modified_at: '2025-04-09T14:45:28.203Z',
  cnr: 'C23',
  order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_NEW,
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const oldCarOrderNotCustomerRelatedObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITZZZZZZ',
  dealer_number: 'ItNcoUpdDlr',
  importer_code: 'IT',
  importer_number: 'ItNcoUpdImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'ItOTNoCustomerRel1',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'ItScUpdBlackAllow',
  receiving_port_code: 'ItNcoUpdPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  order_status_onevms_code: Constants.CORA_NEW_CAR_ORDER_STATUS_NEW,
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  cnr: 'C23',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

const oldCarOrderWrongStatus: NewCarOrderModel = {
  pk_new_car_order_id: 'ITQQQQQQ',
  dealer_number: 'ItNcoUpdDlr',
  importer_code: 'IT',
  importer_number: 'ItNcoUpdImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'ItOtUpdBlackAllow',
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: 'ItScUpdBlackAllow',
  receiving_port_code: 'ItNcoUpdPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  order_status_onevms_code: 'order_status_NOT_changeable',
  order_status_onevms_error_code: 'null',
  order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
  order_invoice_onevms_code: Constants.CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW,
  cnr: 'C23',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { created_by: 'IntegrationTester', modified_by: 'IntegrationTester', ordered_options: [] },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

//updated nco request obj
const newCarOrderReqObject: UpdateNcoPayload = {
  pk_new_car_order_id: 'ITXXXXXX',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otWhitelistAllow.pk_order_type,
  quota_month: '2024-12',
  requested_dealer_delivery_date: '2025-01-01',
  shipping_code: scs.scWhitelistAllow.pk_shipping_code,
  receiving_port_code: 'ItNcoPc4',
  modified_at: '2022-12-06T10:21:02.876Z',
  modified_by: 'IntegrationTester',
  configuration_signature: 'mydummysignature',
  cnr: 'C23',
  configuration: {
    ordered_options: [
      {
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1_CHANGED',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1_CHANGED',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            option_id: 'NewCONTENT1',
            option_type: 'NewCONTENTTYPE1',
            referenced_package: 'NewRP1',
            referenced_package_type: 'NewRPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
            content: [
              {
                option_id: 'NewCONTENT11',
                option_type: 'NewCONTENTTYPE11',
                referenced_package: 'NewRP11',
                referenced_package_type: 'NewRPTYPE11',
                referenced_package_sort_order: 1,
                package_content_sort_order: 1,
                option_subtype: 'OST11',
                option_subtype_value: 'OST_VALUE11',
              },
            ],
          },
        ],
      },
    ],
  },
  configuration_expire: null,
};
const defaultUpdateDataSqsEvent: InboundEventHandlerEventUpdateNco = {
  event_type: OneVmsEventKey.UPDATE_NCO,
  transaction_id: uuidv4(),
  sub_transaction_id: uuidv4(),
  action_at: new Date().toISOString(),
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  nco_id: oldCarOrderObject.pk_new_car_order_id,
  modified_at: oldCarOrderObject.modified_at!,
  payload: newCarOrderReqObject,
  source_system: OneVmsSourceSystemKey.CORA,
} satisfies InboundEventHandlerEventUpdateNco;

let dataSource: DataSource;
let repository: Repository<NewCarOrderModel>;

function deepcopyNcoEntity(oldNco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(oldNco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', oldNco);
}
let testMtvs: ModelTypeVisibilityModel[] = [];
beforeAll(async () => {
  dataSource = await initDataSourceForIntTest([
    NewCarOrderModel,
    NcoConfigurationModel,
    NcoConfigOrderedOptionsModel,
    NewCarOrderAuditTrailModel,
    ModelTypeVisibilityModel,
  ]);
  repository = dataSource.getRepository(NewCarOrderModel);
  const mtvRepository = dataSource.getRepository(ModelTypeVisibilityModel);
  testMtvs = await mtvRepository.save(mtvs);
  const oldCarOrderObjectRes = await repository.save(deepcopyNcoEntity(oldCarOrderObject));
  const oldCarOrderCustomerRelatedObjectRes = await repository.save(
    deepcopyNcoEntity(oldCarOrderCustomerRelatedObject),
  );
  const oldCarOrderNotCustomerRelatedObjectRes = await repository.save(
    deepcopyNcoEntity(oldCarOrderNotCustomerRelatedObject),
  );
  const oldCarOrderWrongStatusRes = await repository.save(deepcopyNcoEntity(oldCarOrderWrongStatus));

  //update modified_at fields because valid values are needed in request body
  oldCarOrderObject.modified_at = oldCarOrderObjectRes.modified_at;
  oldCarOrderCustomerRelatedObject.modified_at = oldCarOrderCustomerRelatedObjectRes.modified_at;
  oldCarOrderNotCustomerRelatedObject.modified_at = oldCarOrderNotCustomerRelatedObjectRes.modified_at;
  oldCarOrderWrongStatus.modified_at = oldCarOrderWrongStatusRes.modified_at;
  newCarOrderReqObject.modified_at = oldCarOrderObjectRes.modified_at;

  await prepareDynamodb([
    { tableName: orgRelTableName, objs: orgRels },
    { tableName: mdDlrTableName, objs: [mdDealer] },
    { tableName: mdImpTableName, objs: [mdImporter] },
    { tableName: mdScTableName, objs: Object.values(scs) },
    { tableName: mdOtTableName, objs: Object.values(ots) },
  ]);
}, 120000);

afterAll(async () => {
  await repository.delete({
    pk_new_car_order_id: In([
      oldCarOrderObject.pk_new_car_order_id,
      oldCarOrderCustomerRelatedObject.pk_new_car_order_id,
      oldCarOrderNotCustomerRelatedObject.pk_new_car_order_id,
      oldCarOrderWrongStatus.pk_new_car_order_id,
    ]),
  });
  await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
    pk_new_car_order_id: In([
      oldCarOrderObject.pk_new_car_order_id,
      oldCarOrderCustomerRelatedObject.pk_new_car_order_id,
      oldCarOrderNotCustomerRelatedObject.pk_new_car_order_id,
      oldCarOrderWrongStatus.pk_new_car_order_id,
    ]),
  });
  await dataSource.getRepository(ModelTypeVisibilityModel).remove(testMtvs);
  await dataSource.destroy();

  await cleanupDynamodb([
    {
      tableName: orgRelTableName,
      pks: orgRelPks,
    },
    {
      tableName: mdDlrTableName,
      pks: [{ pk_importer_number: mdDealer.pk_importer_number, sk_dealer_number: mdDealer.sk_dealer_number }],
    },
    {
      tableName: mdImpTableName,
      pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
    },
    {
      tableName: mdScTableName,
      pks: scPks,
    },
    {
      tableName: mdOtTableName,
      pks: otPks,
    },
  ]);
}, 120000);
it('Should NOT update nco and return fail if input is missing required props', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('SQS record is not valid');
});

it('Should NOT update nco and return fail if auth context is faulty', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      user_auth_context: {
        ...eventPayload.user_auth_context,
        kasApplications: appsWithNoRole,
      },
    },
  ]);
  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Failed to get the visibility level');

  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update nco and return fail if nco cannot be found', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      nco_id: 'NOT_VALID_NCOID',
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Could not find old order with ncoid');
  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});
it('Should NOT update nco and return fail if modified_at does not match', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('changed by someone else');

  const ncoDb = await repository.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
  expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
  expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  expect(ncoDb?.order_type).toEqual(oldCarOrderObject.order_type);
});

it('Should NOT update order object if order type is switched ', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    order_type: ots.otCustomerRel.pk_order_type,
  };
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Invalid switch of the ordertype');
});
it('Should NOT update order object if ncoID is missing/wrong ', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    order_type: ots.otWhitelistAllow.pk_order_type,
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: 'test',
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Could not find old order with ncoid');
});
it('Should NOT update if order objects shipping code cannot be found', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    shipping_code: 'somethingWrong',
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Order params do not match masterdata (ShippingCode)');
});
it('Should NOT update if order objects order type cannot be found', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    order_type: 'somethingWrong',
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain(' Order params do not match masterdata (OrderType)');
});
it('Should NOT update if order objects shipping code is not allowed by blacklist for importer', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    shipping_code: scs.scBlacklistDeny.pk_shipping_code,
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Shipping code not allowed for importer by blacklist');
});
it('Should NOT update if order objects shipping code is not allowed by whitelist for importer', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    shipping_code: scs.scWhitelistDeny.pk_shipping_code,
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Shipping code not allowed for importer by whitelist');
});

it('Should NOT update NCO when updating to a invalid quota_month (mock succ case)', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...newCarOrderReqObject,
        quota_month: '2999-01',
        requested_dealer_delivery_date: '2999-02-02',
      },
    },
  ]);

  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Error updating new car order, no quota available for the changes');
});
it('Should NOT update if order objects port code is not in any list for importer', async () => {
  const _body: UpdateNcoPayload = {
    ...newCarOrderReqObject,
    pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id,
    modified_at: newCarOrderReqObject.modified_at,
    receiving_port_code: 'somethingWrong',
  };

  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      modified_at: newCarOrderReqObject.modified_at!,
      nco_id: newCarOrderReqObject.pk_new_car_order_id,
      payload: _body,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Port code is not standard and not in list of importer or dealer');
});

it('Should NOT update NCO if order objects requested delivery date is before quota month', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...newCarOrderReqObject,
        requested_dealer_delivery_date: '2023-01-01',
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('Quota month after requested dealer delivery date. Must be before!');
});

it('Should NOT update NCO if quota month is changed to an unavailable quota (real quota API err case)', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...newCarOrderReqObject,
        quota_month: '2999-01',
        requested_dealer_delivery_date: '2999-02-02',
      },
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toBeDefined();
  expect(res.batchItemFailures.length).toBe(0);
  const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
  expect(subTransactions.length).toBeGreaterThan(0);
  expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
  expect(subTransactions[0].details).toContain('no quota available for the changes');
});

it('Should update NCO and ignore disallowed fields', async () => {
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...newCarOrderReqObject,
        model_type: 'MT0001',
        model_year: '2030',
      },
    },
  ]);

  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  expect(res.batchItemFailures).toEqual([]);
  const ncoDb = await repository.findOne({
    where: { pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id },
    relations: [
      'configuration',
      'configuration.ordered_options',
      'configuration.ordered_options.content',
      'configuration.ordered_options.content.content',
    ],
  });
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.requested_dealer_delivery_date).toEqual('2025-01-01');
  expect(ncoDb?.dealer_number).toEqual('ItNcoDlr01update-nco');
});

it('Should update NCO while ignoring disallowed update params ', async () => {
  //nco is modified by another test so i take the timestamp again from db
  const ncoDbOld = await repository.findOne({
    where: { pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id },
    relations: [
      'configuration',
      'configuration.ordered_options',
      'configuration.ordered_options.content',
      'configuration.ordered_options.content.content',
    ],
  });
  const eventPayload = createInboundEvent(defaultUpdateDataSqsEvent);
  const event = createSqsEvent([
    {
      ...eventPayload,
      payload: {
        ...newCarOrderReqObject,
        dealer_number: 'SomeDlrNr',
        importer_code: 'CC',
        importer_number: 'SomeImpNr',
        model_type: 'MT0002',
        model_year: '2040',
        modified_at: ncoDbOld?.modified_at,
      },
      modified_at: ncoDbOld?.modified_at,
    },
  ]);
  const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
  const ncoDb = await repository.findOne({
    where: { pk_new_car_order_id: newCarOrderReqObject.pk_new_car_order_id },
    relations: [
      'configuration',
      'configuration.ordered_options',
      'configuration.ordered_options.content',
      'configuration.ordered_options.content.content',
    ],
  });
  expect(res.batchItemFailures).toEqual([]);
  expect(ncoDb).not.toBeNull();
  expect(ncoDb?.requested_dealer_delivery_date).toEqual('2025-01-01');
  expect(ncoDb?.dealer_number).toEqual(newCarOrderReqObject.dealer_number);
  expect(ncoDb?.importer_number).toEqual(newCarOrderReqObject.importer_number);
  expect(ncoDb?.model_type).toEqual(newCarOrderReqObject.model_type);
  expect(ncoDb?.model_year).toEqual(newCarOrderReqObject.model_year);
  expect(ncoDb?.importer_number).toEqual(newCarOrderReqObject.importer_number);
  expect(ncoDb?.importer_code).toEqual(newCarOrderReqObject.importer_code);
});
