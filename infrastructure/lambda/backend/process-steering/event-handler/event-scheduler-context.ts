import {
  ActionAfterCompletion,
  CreateScheduleCommand,
  CreateScheduleCommandInput,
  FlexibleTimeWindowMode,
  ListSchedulesCommand,
  ListSchedulesCommandInput,
  SchedulerClient,
  ScheduleSummary,
  UpdateScheduleCommand,
  UpdateScheduleCommandInput,
} from '@aws-sdk/client-scheduler';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { EventHandlerContext } from './event-handler-context';
import { LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { InboundEventDispatcherEvent, InboundEventHandlerEvent } from '../../../../lib/types/process-steering-types';
import { GracePeriodConfig } from '../../../../lib/utils/constants';

export interface EventHandlerEventbridgeSchedule {
  name: string;
  incomingEvent: InboundEventHandlerEvent;
  event: InboundEventDispatcherEvent;
}

export class EventHandlerEventSchedulerContext {
  public static readonly schedulerEBClient = new SchedulerClient({ region: process.env.AWS_REGION });
  public static readonly dispatcherQueueArn = getEnvVarWithAssert('DISPATCHER_QUEUE_ARN');
  public static readonly eventBridgeSchedulerRoleArn = getEnvVarWithAssert('EVENT_BRIDGE_SCHEDULER_ROLE_ARN');
  public static readonly eventBridgeSchedulerKmsKeyArn = getEnvVarWithAssert('EVENT_BRIDGE_SCHEDULER_KMS_KEY_ARN');

  public static async getAllSchedules(schedulePrefix: string): Promise<Record<string, boolean>> {
    let nextToken: string | undefined = undefined;
    const allSchedules: ScheduleSummary[] = [];
    do {
      const queryParams: ListSchedulesCommandInput = {
        NamePrefix: schedulePrefix,
        NextToken: nextToken,
      };
      try {
        const schedules = await this.schedulerEBClient.send(new ListSchedulesCommand(queryParams));
        if (schedules.Schedules) allSchedules.push(...schedules.Schedules);
        nextToken = schedules.NextToken;
      } catch (error) {
        EventHandlerContext.logger.log(LogLevel.ERROR, 'Failed to query existing schedules', { data: error });
        throw error;
      }
    } while (nextToken);

    const allSchedulesLookup: Record<string, boolean> = {};
    allSchedules.forEach((schedule) => {
      if (schedule.Name) {
        allSchedulesLookup[schedule.Name] = true;
      }
    });
    return allSchedulesLookup;
  }
  public static getEventBridgeAtDate(gracePeriodConfig: GracePeriodConfig): string {
    let _timestamp_ms = new Date().getTime();
    const _days = gracePeriodConfig.days + gracePeriodConfig.months * 30;
    const _hours = gracePeriodConfig.hours + _days * 24;
    const _minutes = gracePeriodConfig.minutes + _hours * 60;
    _timestamp_ms += _minutes * 60 * 1000;
    return new Date(_timestamp_ms).toISOString().slice(0, 19);
  }

  public static async upsertEventBridgeSchedules(
    schedulePrefix: string,
    schedules: EventHandlerEventbridgeSchedule[],
    gracePeriodConfig: GracePeriodConfig,
  ): Promise<InboundEventHandlerEvent[]> {
    const allSchedulesLookup = await this.getAllSchedules(schedulePrefix);
    const res: InboundEventHandlerEvent[] = [];
    for (const { name, event, incomingEvent } of schedules) {
      const scheduleAt = this.getEventBridgeAtDate(gracePeriodConfig);
      const scheduleInput: UpdateScheduleCommandInput | CreateScheduleCommandInput = {
        Name: name,
        ScheduleExpression: `at(${scheduleAt})`,
        Target: {
          Arn: this.dispatcherQueueArn,
          RoleArn: this.eventBridgeSchedulerRoleArn,
          Input: JSON.stringify(event),
        },
        FlexibleTimeWindow: {
          Mode: gracePeriodConfig.flexible_window_min ? FlexibleTimeWindowMode.FLEXIBLE : FlexibleTimeWindowMode.OFF,
          MaximumWindowInMinutes: gracePeriodConfig.flexible_window_min,
        },
        KmsKeyArn: this.eventBridgeSchedulerKmsKeyArn,
        ActionAfterCompletion: ActionAfterCompletion.DELETE,
      };
      incomingEvent.payload = incomingEvent.payload
        ? { ...incomingEvent.payload, actionScheduledAt: scheduleAt }
        : { actionScheduledAt: scheduleAt };
      res.push(incomingEvent);

      const isExistingSchedule = allSchedulesLookup[name];
      EventHandlerContext.logger.log(LogLevel.DEBUG, `Schedule ${name} exists: ${!!isExistingSchedule}`);
      try {
        await this.schedulerEBClient.send(
          isExistingSchedule ? new UpdateScheduleCommand(scheduleInput) : new CreateScheduleCommand(scheduleInput),
        );
        EventHandlerContext.logger.log(LogLevel.DEBUG, `Upserted schedule for event`, {
          data: { scheduleName: name, event },
        });
      } catch (error) {
        EventHandlerContext.logger.log(LogLevel.ERROR, 'Error upserting schedule', { data: { error, event } });
        throw error;
      }
    }
    return res;
  }
}
