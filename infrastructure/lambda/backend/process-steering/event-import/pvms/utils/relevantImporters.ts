import { KasStage } from '@kas-resources/constructs';

export const relevantImporters: Record<KasStage, string[]> = {
  dev: [
    '2700000', //Polen <PERSON>eu
    '2600000', //Polen Alt
    '5600000', //Mazedonien
    '2620000', //Armenien
    '2620000', //Armenien
    '2170000', //Malta
    '5660000', //Moldavien
    '5660000', //Moldavien
    '3130000', //Bosnien
    '8230000', //Japan
    '9260000', //Australien
  ],
  int: [
    '2700000', //Polen Neu
    '2600000', //Polen Alt
    '5600000', //Mazedonien
    '2620000', //Armenien
    '2620000', //Armenien
    '2170000', //Malta
    '5660000', //Moldavien
    '5660000', //Moldavien
    '3130000', //Bosnien
    '8230000', //Japan
    '9260000', //Australien
  ],
  prod: [
    '2700000', //<PERSON>n <PERSON>eu
  ],
};
