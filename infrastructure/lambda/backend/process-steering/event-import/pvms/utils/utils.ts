import { MSKRecord } from 'aws-lambda';
import { PvmsOrderDataKafkaObject, PvmsOrderDataDTO } from '../../../../../../lib/types/pvms-types';
import { ObjectValidator } from '../../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { relevantImporters } from './relevantImporters';

export function decodeKafkaRecord(
  stage: string,
  record: MSKRecord,
  validator: ObjectValidator<PvmsOrderDataDTO>,
  logger: KasLambdaLogger,
): PvmsOrderDataKafkaObject | undefined {
  try {
    const key = Buffer.from(record.key, 'base64').toString('utf8');
    if (!record.value) {
      return undefined;
    }

    const valStr = Buffer.from(record.value, 'base64').toString('utf8');
    const parsedRec = JSON.parse(valStr) as PvmsOrderDataDTO;

    if (!validatePVMSCommon(parsedRec, validator, logger)) {
      logger.log(LogLevel.ERROR, 'Invalid PVMS record, skipping', { data: parsedRec });
      return undefined;
    } else if (isRelevantImporter(parsedRec, stage)) {
      return {
        key: key,
        value: parsedRec,
        timestamp: record.timestamp,
        numOfTries: 0,
      };
    } else {
      logger.log(
        LogLevel.INFO,
        `Got obj with importer "${parsedRec.order_info.trading_partner.importer_number}, which is not relevant. Skipping obj."`,
      );
      return undefined;
    }
  } catch (err) {
    logger.log(LogLevel.WARN, 'Failed to decode Kafka record', { data: err });
    return undefined;
  }
}

export function validatePVMSCommon(
  input: PvmsOrderDataDTO,
  validator: ObjectValidator<PvmsOrderDataDTO>,
  logger: KasLambdaLogger,
): boolean {
  const [body_validated, validation_errors] = validator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

function isRelevantImporter(pvmsObj: PvmsOrderDataDTO, stage: string): boolean {
  const importerNumber = pvmsObj.order_info.trading_partner.importer_number.replace(/^0+/, '');
  return relevantImporters[stage as 'dev' | 'int' | 'prod'].includes(importerNumber);
}
