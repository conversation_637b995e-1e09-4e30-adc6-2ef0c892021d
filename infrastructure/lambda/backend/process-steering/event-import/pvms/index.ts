/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { <PERSON><PERSON>, MSKEvent, MSKRecord } from 'aws-lambda';
import {
  PvmsOrderDataDTO,
  PvmsOrderDataDTOTransaction,
  PvmsOrderDataKafkaObject,
} from '../../../../../lib/types/pvms-types';
import { correlationHeader, getEnvVarWithAssert } from '../../../../utils/utils';
import { EventImportContext } from '../event-import-context';
import {
  InboundEventDispatcherEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../../../lib/types/process-steering-types';
import { decodeKafkaRecord } from './utils/utils';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { SendMessageCommand } from '@aws-sdk/client-sqs';
import { v4 as uuidv4 } from 'uuid';
import { Constants } from '../../../../../lib/utils/constants';
import { getExistingNco } from '../../../../utils/utils-typeorm';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';

const ENABLE_NCO_INVOICE_MAPPING: boolean = getEnvVarWithAssert('ENABLE_NCO_INVOICE_MAPPING') === 'true' ? true : false;
const pvmsOrderDataValidator = new ObjectValidator<PvmsOrderDataDTO>('PvmsOrderDataDTO');

EventImportContext.init(OneVmsEventKey.PVMS_IMPORT, [
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  NewCarOrderAuditTrailModel,
]);

export const handler: Handler<MSKEvent, void> = async (event, context) => {
  const logger = EventImportContext.logger;
  logger.setRequestContext(context);

  try {
    logger.log(LogLevel.INFO, `Got kafka topic event, processing...`, event.records);
    const parsedRecords = Object.values(event.records)
      .flat()
      .map((record: MSKRecord) => {
        EventImportContext.logger.setCorrelationId(correlationHeader(record));
        return decodeKafkaRecord(EventImportContext.stage, record, pvmsOrderDataValidator, logger);
      });
    const validatedRecords: PvmsOrderDataKafkaObject[] = parsedRecords.filter(Boolean) as PvmsOrderDataKafkaObject[];

    logger.log(LogLevel.INFO, `Received ${validatedRecords.length} valid PVMS Kafka records`);

    for (const record of validatedRecords) {
      try {
        await dispatchPvmsEvent(record);
      } catch (err) {
        logger.log(LogLevel.ERROR, 'Error dispatching PVMS event', { data: { record, error: err } });
        throw err;
      }
    }
  } catch (err) {
    logger.log(LogLevel.ERROR, 'PVMS Kafka Consumer failed', { data: err });
    throw err;
  }
};

const isPurchaseIntention = (record: PvmsOrderDataKafkaObject): boolean => {
  const status = record.value.order_info.status_info.vehicle_status_pvms_code;
  return Constants.PVMS_PURCHASE_INTENTION_STATUS_ALL.includes(status);
};

const dispatchPvmsEvent = async (record: PvmsOrderDataKafkaObject): Promise<void | string> => {
  const logger = EventImportContext.logger;
  if (isPurchaseIntention(record)) {
    logger.log(LogLevel.INFO, 'Detected Purchase Intention. Sending to PI SQS.');
    await sendToPiQueue(record);
    return;
  }

  logger.log(LogLevel.INFO, 'Detected New Car Order. Sending to Dispatcher.');
  const ncoId = record.value.ids.new_car_order_id;
  const dataSource = await EventImportContext.getDataSource();
  const existingNco = await getExistingNco({ ncoRepo: dataSource.getRepository(NewCarOrderModel), ncoId }, logger);

  if (!existingNco) {
    logger.log(LogLevel.WARN, 'NewCarOrder does not exist, the transaction is aborted.', {
      data: { new_car_order_id: ncoId },
    });
    return;
  }

  const dispatcherEvent: InboundEventDispatcherEvent = {
    transaction_id: uuidv4(),
    event_type: OneVmsEventKey.PVMS_UPDATE_NCO,
    action_at: new Date(record.timestamp).toISOString(),
    source_system: OneVmsSourceSystemKey.PVMS,
    ncos_info: [
      {
        pk_new_car_order_id: ncoId,
        sub_transaction_id: uuidv4(),
        modified_at: existingNco.modified_at ?? new Date().toISOString(),
      },
    ],
    payload: { ...record, mapInvoice: ENABLE_NCO_INVOICE_MAPPING },
  };

  await EventImportContext.sendToDispatcherSqs(dispatcherEvent);
};

const sendToPiQueue = async (record: PvmsOrderDataKafkaObject): Promise<void> => {
  const queueUrl = getEnvVarWithAssert('PI_IMPORT_QUEUE_URL');
  const dtoWithTransactionId: PvmsOrderDataDTOTransaction = { ...record.value, transaction_id: uuidv4() };
  const command = new SendMessageCommand({
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify(dtoWithTransactionId),
  });

  const res = await EventImportContext.sqsClient.send(command);
  if (!res.MessageId) {
    //Retryable, goes into DLQ
    throw new Error('Failed to send Purchase Intention to SQS.');
  }
  const notificationEvent: NotificationKafkaEvent = {
    transaction_id: dtoWithTransactionId.transaction_id,
    sub_transaction_id: uuidv4(),
    event_type: OneVmsEventKey.PVMS_IMPORT,
    action_at: new Date(record.timestamp).toISOString(),
    source_system: OneVmsSourceSystemKey.PVMS,
    status: NotificationStatus.ACCEPTED,
    action_by: OneVmsSourceSystemKey.PVMS,
  };
  await EventImportContext.publishKafkaNotifications([notificationEvent]);
};
