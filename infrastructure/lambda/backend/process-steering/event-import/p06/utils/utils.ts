import { MSKRecord } from 'aws-lambda';
import { ObjectValidator } from '../../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { relevantImporters } from './relevantImporters';
import { P06NewCarOrderDataDTO, P06NewCarOrderKafkaObject } from '../../../../../../lib/types/p06-types';
import { KasStage } from '@kas-resources/constructs';

export function decodeKafkaRecord(
  stage: string,
  record: MSKRecord,
  validator: ObjectValidator<P06NewCarOrderDataDTO>,
  logger: KasLambdaLogger,
): P06NewCarOrderKafkaObject | undefined {
  try {
    const key = Buffer.from(record.key, 'base64').toString('utf8');
    if (!record.value) {
      return undefined;
    }

    const valStr = Buffer.from(record.value, 'base64').toString('utf8');
    const parsedRec = JSON.parse(valStr) as P06NewCarOrderDataDTO;

    if (!validateP06Common(parsedRec, validator, logger)) {
      logger.log(LogLevel.ERROR, 'Invalid P06 record, skipping', { data: parsedRec });
      return undefined;
    } else if (isRelevantImporter(parsedRec, stage)) {
      return {
        key: key,
        value: parsedRec,
        timestamp: record.timestamp,
      };
    } else {
      logger.log(
        LogLevel.INFO,
        `Got obj with importer "${parsedRec.order_info.trading_partner.importer_number}, which is not relevant. Skipping obj."`,
      );
      return undefined;
    }
  } catch (err) {
    logger.log(LogLevel.WARN, 'Failed to decode Kafka record', { data: err });
    return undefined;
  }
}

export function validateP06Common(
  input: P06NewCarOrderDataDTO,
  validator: ObjectValidator<P06NewCarOrderDataDTO>,
  logger: KasLambdaLogger,
): boolean {
  const [body_validated, validation_errors] = validator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

function isRelevantImporter(p06Obj: P06NewCarOrderDataDTO, stage: string): boolean {
  const importerNumber = p06Obj.order_info.trading_partner.importer_number.replace(/^0+/, '');
  return relevantImporters[stage as KasStage].includes(importerNumber);
}
