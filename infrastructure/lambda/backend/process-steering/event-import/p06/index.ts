/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { <PERSON><PERSON>, MSKEvent, MSKRecord } from 'aws-lambda';
import { correlationHeader } from '../../../../utils/utils';
import { EventImportContext } from '../event-import-context';
import {
  InboundEventDispatcherEvent,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../../../lib/types/process-steering-types';
import { decodeKafkaRecord } from './utils/utils';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { v4 as uuidv4 } from 'uuid';
import { getExistingNco } from '../../../../utils/utils-typeorm';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { P06NewCarOrderDataDTO, P06NewCarOrderKafkaObject } from '../../../../../lib/types/p06-types';

const P06OrderDataValidator = new ObjectValidator<P06NewCarOrderDataDTO>('P06NewCarOrderDataDTO');

EventImportContext.init(OneVmsEventKey.P06_IMPORT, [
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  NewCarOrderAuditTrailModel,
]);

export const handler: Handler<MSKEvent, void> = async (event, context) => {
  const logger = EventImportContext.logger;
  logger.setRequestContext(context);

  try {
    logger.log(LogLevel.INFO, `Got kafka topic event, processing...`, event.records);
    const parsedRecords = Object.values(event.records)
      .flat()
      .map((record: MSKRecord) => {
        EventImportContext.logger.setCorrelationId(correlationHeader(record));
        return decodeKafkaRecord(EventImportContext.stage, record, P06OrderDataValidator, logger);
      });
    const validatedRecords: P06NewCarOrderKafkaObject[] = parsedRecords.filter(Boolean) as P06NewCarOrderKafkaObject[];

    logger.log(LogLevel.INFO, `Received ${validatedRecords.length} valid P06 Kafka records`);

    for (const record of validatedRecords) {
      try {
        await dispatchP06Event(record);
      } catch (err) {
        logger.log(LogLevel.ERROR, 'Error dispatching P06 event', { data: { record, error: err } });
        throw err;
      }
    }
  } catch (err) {
    logger.log(LogLevel.ERROR, 'P06 Kafka Consumer failed', { data: err });
    throw err;
  }
};

const dispatchP06Event = async (record: P06NewCarOrderKafkaObject): Promise<void | string> => {
  const logger = EventImportContext.logger;
  const ncoId = record.value.ids.new_car_order_id;

  const dataSource = await EventImportContext.getDataSource();
  const existingNco = await getExistingNco({ ncoRepo: dataSource.getRepository(NewCarOrderModel), ncoId }, logger);

  if (!existingNco) {
    logger.log(LogLevel.WARN, 'NewCarOrder does not exist, the transaction is aborted.', {
      data: { new_car_order_id: ncoId },
    });
    return;
  }

  const dispatcherEvent: InboundEventDispatcherEvent = {
    transaction_id: uuidv4(),
    event_type: OneVmsEventKey.P06_UPDATE_NCO,
    action_at: new Date(record.timestamp).toISOString(),
    source_system: OneVmsSourceSystemKey.P06,
    ncos_info: [
      {
        pk_new_car_order_id: ncoId,
        sub_transaction_id: uuidv4(),
        modified_at: existingNco.modified_at ?? new Date().toISOString(),
      },
    ],
    payload: record,
  };

  await EventImportContext.sendToDispatcherSqs(dispatcherEvent);
};
