import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { getUserName, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { fetchRelatedDataInParallel } from './utils';
import {
  NotificationTransactionGetAllQueryParams,
  NotificationTransactionResponse,
  SubTransaction,
  Transaction,
} from '../../../../lib/types/notification-center-types';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { stringToBoolean } from '../../../utils/validation-helpers';

const logger = new KasLambdaLogger('notification-center-get-all', LogLevel.TRACE);
const dynamoDbClient = new DynamoDBClient({ region: process.env.REGION ?? 'eu-west-1' });
const objectValidator = new ObjectValidator<NotificationTransactionGetAllQueryParams>(
  'NotificationTransactionGetAllQueryParams',
);

const TRANSACTION_TABLE_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_NAME');
const TRANSACTION_TABLE_INDEX_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_INDEX_NAME');
const TRANSACTION_TABLE_INDEX_PK_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_INDEX_PK_NAME');
const TRANSACTION_TABLE_INDEX_SK_NAME = getEnvVarWithAssert('TRANSACTION_TABLE_INDEX_SK_NAME');
const SUB_TRANSACTION_TABLE_NAME = getEnvVarWithAssert('SUB_TRANSACTION_TABLE_NAME');
const SUB_TRANSACTION_TABLE_PK_NAME = getEnvVarWithAssert('SUB_TRANSACTION_TABLE_PK_NAME');
const CORS_DOMAIN = getEnvVarWithAssert('CORS_DOMAIN');
const PADDOCK_CORS_DOMAIN = getEnvVarWithAssert('PADDOCK_CORS_DOMAIN');

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse query parameters
    const userName = getUserName({ event: event }, logger);
    if (!userName) {
      logger.log(LogLevel.WARN, 'Failed to get the userName', { data: sanitizeApiGwEvent({ event: event }, logger) });
      return sendFail({ message: 'No PPN username in request', status: 400, reqHeaders: event.headers }, logger);
    }

    // Validate body against the schema
    const [query_params_validated, validation_errors] = objectValidator.validate(event.queryStringParameters ?? {});
    if (query_params_validated === null) {
      logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
      return sendFail(
        {
          message: 'Ajv validation failed with: ' + JSON.stringify(validation_errors),
          status: 400,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    // Validate timestamp format if provided
    const ifModifiedSince = event.headers['if-modified-since'];
    const ifModifiedSinceDate = ifModifiedSince ? new Date(ifModifiedSince) : null;

    if (ifModifiedSinceDate && isNaN(ifModifiedSinceDate.getTime())) {
      return sendFail(
        {
          message:
            'If-Modified-Since header has the wrong format, refer to https://developer.mozilla.org/en-US/docs/Web/HTTP/Reference/Headers/If-Modified-Since',
          status: 400,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    // Build KeyConditionExpression for DynamoDB query
    let keyConditionExpression = `${TRANSACTION_TABLE_INDEX_PK_NAME} = :action_by`;
    const expressionAttributeValues: Record<string, unknown> = {
      ':action_by': userName,
    };
    const expressionAttributeNames: Record<string, string> = {};

    // Add conditions for the sort key (action_at) based on from/until
    if (query_params_validated.from && query_params_validated.until) {
      keyConditionExpression += ` AND ${TRANSACTION_TABLE_INDEX_SK_NAME} BETWEEN :from AND :until`;
      expressionAttributeValues[':from'] = query_params_validated.from;
      expressionAttributeValues[':until'] = query_params_validated.until;
    } else if (query_params_validated.from) {
      keyConditionExpression += ` AND ${TRANSACTION_TABLE_INDEX_SK_NAME} >= :from`;
      expressionAttributeValues[':from'] = query_params_validated.from;
    } else if (query_params_validated.until) {
      keyConditionExpression += ` AND ${TRANSACTION_TABLE_INDEX_SK_NAME} <= :until`;
      expressionAttributeValues[':until'] = query_params_validated.until;
    }

    // Build FilterExpression for optional filters
    let filterExpression = '';
    if (!stringToBoolean(query_params_validated.show_hidden)) {
      filterExpression += '#hidden = :hidden';
      expressionAttributeNames['#hidden'] = 'hidden'; // hidden is a reserved keyword in dynamodb
      expressionAttributeValues[':hidden'] = false;
    }

    if (query_params_validated.action_type) {
      filterExpression += filterExpression ? ' AND ' : '';
      filterExpression += 'event_type = :actionType';
      expressionAttributeValues[':actionType'] = query_params_validated.action_type;
    }

    if (ifModifiedSinceDate) {
      filterExpression += filterExpression ? ' AND ' : '';
      filterExpression += 'last_update >= :last_update';
      expressionAttributeValues[':last_update'] = ifModifiedSinceDate.toISOString();
    }

    let lastEvaluatedKey: Record<string, unknown> | undefined = undefined;
    const transactions: Transaction[] = [];

    do {
      const queryCommand = new QueryCommand({
        TableName: TRANSACTION_TABLE_NAME,
        IndexName: TRANSACTION_TABLE_INDEX_NAME,
        KeyConditionExpression: keyConditionExpression,
        FilterExpression: filterExpression || undefined,
        ExpressionAttributeNames:
          Object.keys(expressionAttributeNames).length === 0 ? undefined : expressionAttributeNames, // DynamoDb weirdness must not be empty obj
        ExpressionAttributeValues: expressionAttributeValues,
        ExclusiveStartKey: lastEvaluatedKey,
      });

      const result: QueryCommandOutput = await dynamoDbClient.send(queryCommand);

      if (result.Items) {
        transactions.push(...(result.Items as Transaction[]));
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    logger.log(LogLevel.TRACE, 'Transactions', { data: transactions });
    // Return 204 or 304 if no items are found
    if (!transactions.length) {
      // sendSuccess can only return 200
      logger.log(LogLevel.DEBUG, 'No transactions found, query params:', {
        data: { filterExpression, expressionAttributeNames, expressionAttributeValues },
      });
      return sendFail(
        {
          message: JSON.stringify([]),
          status: ifModifiedSinceDate ? 304 : 204,
          reqHeaders: event.headers,
          allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
        },
        logger,
      );
    }

    const getAllResult = await getSubTransactionsForTransactions(transactions);

    return sendSuccess(
      { body: getAllResult, reqHeaders: event.headers, allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN] },
      logger,
    );
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error querying DynamoDB:', { data: { error } });
    return sendFail(
      {
        message: 'Internal Server Error',
        status: 500,
        reqHeaders: event.headers,
        allowedOrigins: [CORS_DOMAIN, PADDOCK_CORS_DOMAIN],
      },
      logger,
    );
  }
};

async function getSubTransactionsForTransactions(
  transactions: Transaction[],
): Promise<NotificationTransactionResponse> {
  const transaction_ids = transactions.map((t) => t.transaction_id);
  logger.log(LogLevel.DEBUG, 'Getting SubTransactions for transaction_ids', { data: { transaction_ids } });
  const data = await fetchRelatedDataInParallel<SubTransaction>(
    {
      dynamoDbClient: dynamoDbClient,
      table_name: SUB_TRANSACTION_TABLE_NAME,
      pk_name: SUB_TRANSACTION_TABLE_PK_NAME,
      logger: logger,
    },
    transaction_ids,
  );
  return transactions.map((t) => {
    return { ...t, sub_transactions: data[t.transaction_id] };
  });
}
