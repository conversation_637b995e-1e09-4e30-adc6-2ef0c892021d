import { DynamoDBClient, ProvisionedThroughputExceededException } from '@aws-sdk/client-dynamodb';
import { QueryCommand, QueryCommandOutput } from '@aws-sdk/lib-dynamodb';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';

interface QueryProps {
  dynamoDbClient: DynamoDBClient;
  table_name: string;
  pk_name: string;
  logger: Kas<PERSON>ambdaLogger;
}
// Helper function to handle throttling with retries
const queryWithRetry = async (props: QueryProps, command: QueryCommand, retries = 3): Promise<QueryCommandOutput> => {
  try {
    return props.dynamoDbClient.send(command);
  } catch (error: unknown) {
    if (retries > 0 && error instanceof ProvisionedThroughputExceededException) {
      const delay = Math.pow(2, 3 - retries) * 100; // Exponential backoff
      props.logger.log(LogLevel.WARN, `Throttling detected. Retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
      return queryWithRetry(props, command, retries - 1);
    }
    throw error;
  }
};

// Function to query the second table for each transaction ID (with pagination)
const fetchAllItemsForId = async <T>(props: QueryProps, id: string): Promise<T[]> => {
  const results: T[] = [];
  let lastEvaluatedKey: Record<string, T> | undefined = undefined;

  do {
    const queryCommand = new QueryCommand({
      TableName: props.table_name,
      KeyConditionExpression: `${props.pk_name} = :${props.pk_name}`,
      ExpressionAttributeValues: {
        [`:${props.pk_name}`]: id,
      },
      ExclusiveStartKey: lastEvaluatedKey, // For pagination
    });

    const response = await queryWithRetry(props, queryCommand);

    // Collect items from this page
    if (response.Items) {
      results.push(...(response.Items as T[]));
    }

    lastEvaluatedKey = response.LastEvaluatedKey;
  } while (lastEvaluatedKey);

  return results;
};

// Function to fetch data for all transaction IDs in parallel
export const fetchRelatedDataInParallel = async <T>(props: QueryProps, ids: string[]): Promise<Record<string, T[]>> => {
  const resultsDict: Record<string, T[]> = {};

  await Promise.all(
    ids.map(async (id) => {
      const data = await fetchAllItemsForId<T>(props, id);
      resultsDict[id] = data;
    }),
  );

  return resultsDict;
};
