import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { BatchWriteCommand, BatchWriteCommandInput, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { getEnvVarWithAssert, correlationHeader, splitIntoBatches } from '../../../../utils/utils';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { NotificationKafkaEvent, NotificationStatus } from '../../../../../lib/types/process-steering-types';
import { SubTransaction, Transaction } from '../../../../../lib/types/notification-center-types';
import { v4 as uuidv4 } from 'uuid';

const NOTIFICATION_KAFKA_TOPIC = getEnvVarWithAssert('NOTIFICATION_KAFKA_TOPIC');
const TRANSACTIONS_TABLE_NAME = getEnvVarWithAssert('TRANSACTIONS_TABLE_NAME');
const SUBTRANSACTIONS_TABLE_NAME = getEnvVarWithAssert('SUBTRANSACTIONS_TABLE_NAME');

const dbClient = new DynamoDBClient({});
const logger = new KasLambdaLogger('notification-ingest', LogLevel.TRACE);
const objectValidator = new ObjectValidator<NotificationKafkaEvent>('NotificationKafkaEvent');

export const handler: Handler<MSKEvent, void> = async (event, context): Promise<void> => {
  logger.setRequestContext(context);
  const notifications = parseNotificationsFromEvent(event);

  logger.setCorrelationId(context.awsRequestId);
  logger.log(LogLevel.DEBUG, `Processing ${notifications.length} notifications`);

  await processNotifications(notifications);
};

function parseNotificationsFromEvent(event: MSKEvent): NotificationKafkaEvent[] {
  return Object.entries(event.records)
    .filter(([key]) => key.includes(NOTIFICATION_KAFKA_TOPIC))
    .flatMap(([, value]) => value.map(parseRecord).flat())
    .filter(Boolean) as NotificationKafkaEvent[];
}

function parseRecord(record: MSKRecord): NotificationKafkaEvent | undefined {
  try {
    logger.setCorrelationId(correlationHeader(record));
    const { value } = record;
    if (!value) return undefined;

    const rawEvent = JSON.parse(Buffer.from(value, 'base64').toString('utf8')) as unknown;
    const [validatedEvent, errors] = objectValidator.validate(rawEvent);
    if (!validatedEvent) {
      logger.log(LogLevel.WARN, 'Invalid event structure', { data: { errors: errors, object: rawEvent } });
      return undefined;
    }
    return validatedEvent;
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to parse notification', { data: { e, record } });
    return undefined;
  }
}

async function processNotifications(notifications: NotificationKafkaEvent[]): Promise<void> {
  const transactionsToInsert: Transaction[] = [];
  const subtransactionsToInsert: SubTransaction[] = [];
  const updateMetadataPromises: Promise<void>[] = [];

  for (const event of notifications) {
    logger.log(LogLevel.DEBUG, 'Processing event', { data: event });

    // Always insert subtransactions
    subtransactionsToInsert.push(transformToSubTransaction(event));

    // Only insert transactions with status accepted
    if (event.status === NotificationStatus.ACCEPTED) {
      transactionsToInsert.push(transformToTransaction(event));
    }
  }

  await writeTransactions(transactionsToInsert);
  await writeSubtransactions(subtransactionsToInsert);

  // Group events by transaction_id and find latest action_at per transaction
  const latestActionMap: Map<string, string> = new Map(); // transaction_id -> latest action_at

  for (const event of notifications) {
    const current = latestActionMap.get(event.transaction_id);
    if (!current || new Date(event.action_at) > new Date(current)) {
      latestActionMap.set(event.transaction_id, event.action_at);
    }
  }

  for (const [transaction_id, latestActionAt] of latestActionMap.entries()) {
    updateMetadataPromises.push(updateTransactionLastUpdated(transaction_id, latestActionAt));
  }

  await Promise.allSettled(updateMetadataPromises);
}

function transformToTransaction(event: NotificationKafkaEvent): Transaction {
  return {
    action_by: event.action_by,
    action_at: event.action_at,
    transaction_id: event.transaction_id,
    amount_of_objects: event.transaction_obj_amount ?? 0,
    event_type: event.event_type,
    hidden: false,
    last_update: event.action_at,
  };
}

function transformToSubTransaction(event: NotificationKafkaEvent): SubTransaction {
  return {
    transaction_id: event.transaction_id,
    uuid: uuidv4(),
    sub_transaction_id: event.sub_transaction_id,
    obj_id: event.nco_id,
    details: event.details,
    status: event.status,
    last_update: event.action_at,
  };
}

function deduplicateTransactions(transactions: Transaction[]): Transaction[] {
  const map = new Map<string, Transaction>();

  for (const txn of transactions) {
    if (!map.has(txn.transaction_id)) {
      map.set(txn.transaction_id, txn);
    }
  }

  return Array.from(map.values());
}

async function writeTransactions(transactions: Transaction[]): Promise<void> {
  if (transactions.length === 0) return;

  const deduplicated = deduplicateTransactions(transactions);
  const batches = splitIntoBatches(deduplicated, 25);

  for (const batch of batches) {
    const batchParams: BatchWriteCommandInput = {
      RequestItems: {
        [TRANSACTIONS_TABLE_NAME]: batch.map((txn) => ({
          PutRequest: { Item: txn },
        })),
      },
    };

    await dbClient.send(new BatchWriteCommand(batchParams));
  }

  logger.log(LogLevel.INFO, `Inserted ${deduplicated.length} unique transactions`);
}

async function writeSubtransactions(subtransactions: SubTransaction[]): Promise<void> {
  if (subtransactions.length === 0) return;

  const batches = splitIntoBatches(subtransactions, 25);

  for (const batch of batches) {
    const batchParams: BatchWriteCommandInput = {
      RequestItems: {
        [SUBTRANSACTIONS_TABLE_NAME]: batch.map((subTxn) => ({
          PutRequest: { Item: subTxn },
        })),
      },
    };

    await dbClient.send(new BatchWriteCommand(batchParams));
  }

  logger.log(LogLevel.INFO, `Inserted ${subtransactions.length} subtransactions`);
}

async function updateTransactionLastUpdated(transactionId: string, latestActionAt: string): Promise<void> {
  const updateCommand = new UpdateCommand({
    TableName: TRANSACTIONS_TABLE_NAME,
    Key: {
      transaction_id: transactionId,
    },
    UpdateExpression: 'SET last_update = :lastUpdated',
    ExpressionAttributeValues: {
      ':lastUpdated': latestActionAt,
    },
    ConditionExpression: 'attribute_exists(transaction_id)',
  });

  await dbClient.send(updateCommand);
  logger.log(LogLevel.INFO, `Set last_update for transaction ${transactionId} to ${latestActionAt}`);
}
