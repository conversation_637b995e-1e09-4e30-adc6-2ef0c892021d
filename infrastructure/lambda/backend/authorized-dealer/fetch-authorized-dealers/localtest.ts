/* eslint-disable */
/**
 * This local-test.ts file appears to be a script for local testing of a specific AWS Lambda function handler, defined in the same directory.
 *
 * Steps to run this script:
 * 1. Open a terminal window in the directory containing this file.
 * 2. Run the command `npm install` to install the dependencies for this script.
 * 3. Get AWS credentials for the account you want to test with.
 * 4. Export ENV variables for the local test. For example:
 *      export TABLE_NAME_ORG_RELS='cora-dev-cora-org-rel'
 * 5. Run npx ts-node lambda/backend/authorized-dealer/fetch-authorized-dealers/localtest.ts
 *
 * Note: The environment variables used in this script are mocked and need to be updated to match the environment variables used for your test.
 */
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { handler } from '.';

(async () => {
  process.env.AWS_REGION = 'eu-west-1';

  const event: APIGatewayProxyEvent = {
    body: null,
    headers: {},
    multiValueHeaders: {},
    httpMethod: '',
    path: '',
    pathParameters: {},
    queryStringParameters: null,
    multiValueQueryStringParameters: {},
    stageVariables: {},
    resource: '',
    requestContext: {
      authorizer: {
        userAttributes: JSON.stringify({
          username: 'xyzxyz',
          firstName: 'Max',
          lastName: 'Mustermann',
          porschePartnerNo: '4500000',
          organizationId: '5e16ed10-392b-11dc-b460-dbce88e795aa', // Porsche Latin America = Importer
          displayName: 'Porsche Latin America',
          //organizationId: '51f8bc30-800b-11e6-8616-f12d0c6f9666', // Porsche Center Puerto Rico = Dealer
          //organizationId: '4a143ab0-3331-11e2-ba48-fb232678f3db', //PSD
          kasApplications: {
            cora: [
              {
                role: 'ppn_approle_kas_importer_dev',
                modelTypeVisibility: 'IMP',
              },
            ],
          },
        }),
      },
      protocol: '',
      httpMethod: '',
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        clientCert: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '',
        user: null,
        userAgent: null,
        userArn: null,
      },
      path: '',
      requestTimeEpoch: 0,
      resourceId: '',
      resourcePath: '',
      domainName: '',
      requestId: '',
      routeKey: '',
      stage: '',
      accountId: '',
      apiId: '',
      domainPrefix: '',
    },
    isBase64Encoded: false,
  };

  const context: Context = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: '',
    functionVersion: '',
    invokedFunctionArn: '',
    memoryLimitInMB: '',
    awsRequestId: '',
    logGroupName: '',
    logStreamName: '',
    getRemainingTimeInMillis: function (): number {
      throw new Error('Function not implemented.');
    },
    done: function (error?: Error | undefined, result?: any): void {
      throw new Error('Function not implemented.');
    },
    fail: function (error: string | Error): void {
      throw new Error('Function not implemented.');
    },
    succeed: function (messageOrObject: any): void {
      throw new Error('Function not implemented.');
    },
  };

  const callback = (error: any, result: any) => {
    if (error) {
      console.error(error);
    } else {
      console.log(result);
    }
  };

  try {
    const result = await handler(event, context, callback);
    console.log(result);
  } catch (error) {
    console.error(error);
  }
})();
