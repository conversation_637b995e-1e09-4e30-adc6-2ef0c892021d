import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPiChangeRequest1737471400000 implements MigrationInterface {
    name = 'AddPiChangeRequest1737471400000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" ADD "has_change_request" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" DROP COLUMN "has_change_request"`);
    }

}
