import * as dotenv from 'dotenv';
dotenv.config();
import { DataSource } from 'typeorm';
import { getEnvVarWithAssert } from '../../lambda/utils/utils';

const SECRET_ARN = getEnvVarWithAssert('SECRET_ARN');
const CLUSTER_ARN = getEnvVarWithAssert('CLUSTER_ARN');

export default new DataSource({
  type: 'aurora-postgres',
  database: 'coraDB',
  secretArn: SECRET_ARN,
  resourceArn: CLUSTER_ARN,
  region: 'eu-west-1',
  synchronize: false,
  dropSchema: false,
  logging: true,
  entities: ['lib/entities/*{.ts,.js}'],
  migrations: ['./schema-migrations/migrations/*.ts'],
  subscribers: [],
  migrationsTableName: 'migration_table',
  extra: {
    ssl: {
      rejectUnauthorized: false,
    },
  },
});
