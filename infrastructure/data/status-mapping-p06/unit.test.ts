import p06ToOnevmsStatusMapping from './status_mapping_data.json';

describe('Validation Logic Tests', () => {
  test('check for uniqueness and type validity', (): void => {
    //check uniqueness of p06 status
    const keys = p06ToOnevmsStatusMapping.map((mapping) => mapping.p06_status);
    expect(keys.length).toEqual(Array.from(new Set(keys)).length);

    p06ToOnevmsStatusMapping.forEach((mapping) => {
      expect(mapping.p06_status).not.toBeNull();
      expect(mapping.p06_status).toBeDefined();
      expect(mapping.p06_status).not.toHaveLength(0);

      expect(mapping.one_vms_status).not.toBeNull();
      expect(mapping.one_vms_status).toBeDefined();
      expect(mapping.one_vms_status).not.toHaveLength(0);

      expect(mapping.one_vms_error_status).toBeDefined();
    });
  });
});
