import { DataSource, Repository } from 'typeorm';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../lib/entities/new-car-order-model';
import { AttributeValue, DynamoDBClient, ScanCommand, ScanCommandInput } from '@aws-sdk/client-dynamodb';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import { ncoConfigApiToDbObj } from '../../lambda/utils/utils-typeorm';
import { writeFile } from 'fs/promises';
import { dbCredentials } from './constants';
import { NewCarOrderAuditTrailModel } from '../../lib/entities/new-car-order-audit-trail-model';
import { NcoAuditTrailDynamoDb } from './old_types/audit';
import { NcoExportActionType } from '../../lambda/backend/export-nco/types';
import { Constants } from '../../lib/utils/constants';

const TABLE_NAME = 'cora-prod-new-car-order-audit';
const FAILED_DIR = 'failed_audit';

async function main(): Promise<void> {
  const dataSource = await new DataSource({
    type: 'aurora-postgres',
    database: dbCredentials.dbname,
    secretArn: dbCredentials.secretArn,
    resourceArn: dbCredentials.clusterArn,
    region: dbCredentials.region,
    synchronize: false,
    logging: false,
    entities: [
      CoraPurchaseIntentionModel,
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
    ],
  }).initialize();

  const client = new DynamoDBClient({ region: 'eu-west-1' });

  const res = await getAllFromTable(TABLE_NAME, client, dataSource);
  console.log(res[0]);
}

async function getAllFromTable(tableName: string, client: DynamoDBClient, dataSource: DataSource): Promise<string[]> {
  const items: string[] = [];
  let exclusiveStartKey: Record<string, AttributeValue> | undefined;
  exclusiveStartKey = {
    pk_new_car_order_id: { S: 'POPUX4D5C' },
    timestamp: { N: '1737056049256' },
  };

  try {
    do {
      const params: ScanCommandInput = {
        TableName: tableName,
        ExclusiveStartKey: exclusiveStartKey,
      };

      const command = new ScanCommand(params);
      const response = await client.send(command);

      const batch: NcoAuditTrailDynamoDb[] = [];
      if (response.Items) {
        for (const item of response.Items) {
          const _item = unmarshall(item) as NcoAuditTrailDynamoDb;
          console.log('Saving', _item.pk_new_car_order_id);
          items.push(_item.pk_new_car_order_id);
          batch.push(_item);
        }
      }
      await writeToPostgres(batch, dataSource.getRepository(NewCarOrderAuditTrailModel));
      exclusiveStartKey = response.LastEvaluatedKey;
      console.warn('LastEvaluatedKey', exclusiveStartKey);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } while (exclusiveStartKey);

    return items;
  } catch (error) {
    console.error('Error scanning DynamoDB table:', error);
    throw error;
  }
}

async function writeToPostgres(
  batch: NcoAuditTrailDynamoDb[],
  repository: Repository<NewCarOrderAuditTrailModel>,
): Promise<void> {
  try {
    const new_trails: NewCarOrderAuditTrailModel[] = [];
    for (const item of batch) {
      const nco_id = item.new_NewCarOrder?.pk_new_car_order_id ?? item.old_NewCarOrder?.pk_new_car_order_id;
      if (!nco_id) {
        throw Error('audittrail without id');
      }
      let new_nco: NewCarOrderModel | undefined;
      if (item.new_NewCarOrder) {
        new_nco = {
          ...item.new_NewCarOrder,
          configuration: ncoConfigApiToDbObj(
            item.new_NewCarOrder.configuration,
            item.new_NewCarOrder.modified_by ?? item.new_NewCarOrder.created_by,
            item.pk_new_car_order_id,
          ),
          order_invoice_onevms_code: 'null',
          modified_by: item.new_NewCarOrder.modified_by ?? item.new_NewCarOrder.created_by,
          order_status_onevms_timestamp_last_change:
            item.new_NewCarOrder.modified_at ?? item.new_NewCarOrder.created_at,
        };
      }
      let old_nco: NewCarOrderModel | undefined;
      if (item.old_NewCarOrder) {
        old_nco = {
          ...item.old_NewCarOrder,
          configuration: ncoConfigApiToDbObj(
            item.old_NewCarOrder.configuration,
            item.old_NewCarOrder.modified_by ?? item.old_NewCarOrder.created_by,
            item.pk_new_car_order_id,
          ),
          order_invoice_onevms_code: 'null',
          modified_by: item.old_NewCarOrder.modified_by ?? item.old_NewCarOrder.created_by,
          order_status_onevms_timestamp_last_change:
            item.old_NewCarOrder.modified_at ?? item.old_NewCarOrder.created_at,
        };
      }
      let action_type = item.old_NewCarOrder ? NcoExportActionType.UPDATE : NcoExportActionType.CREATE;
      if (
        new_nco?.order_status_onevms_code === Constants.CORA_NEW_CAR_ORDER_STATUS_CANCELLED &&
        old_nco?.order_status_onevms_code !== Constants.CORA_NEW_CAR_ORDER_STATUS_CANCELLED
      ) {
        action_type = NcoExportActionType.CANCEL;
      }
      const new_trail: NewCarOrderAuditTrailModel = repository.create({
        action_at: new Date(item.timestamp) as unknown as string,
        action_by:
          item.new_NewCarOrder?.modified_by ??
          item.new_NewCarOrder?.created_by ??
          item.old_NewCarOrder?.modified_by ??
          item.old_NewCarOrder?.created_by ??
          'UNKNOWN',
        action_correlation_id: item.new_NewCarOrder?.last_known_correlation_id ?? 'ID_NOT_SET',
        action_exported: true,
        action_type: action_type,
        pk_new_car_order_id: nco_id,
        new_nco: new_nco,
        old_nco: old_nco,
      });
      new_trails.push(new_trail);
    }

    await repository.save(new_trails);
  } catch (error) {
    for (const item of batch) {
      console.error(`${item.pk_new_car_order_id} failed`, error);
      await writeObjectToJsonFile(item);
    }
    return;
  }
  return;
}

async function writeObjectToJsonFile(data: NcoAuditTrailDynamoDb): Promise<void> {
  try {
    const jsonContent = JSON.stringify(data, null, 2);
    await writeFile(`${__dirname}/${FAILED_DIR}/${data.pk_new_car_order_id}.json`, jsonContent, 'utf-8');
  } catch (error) {
    console.error('Error writing JSON to file:', error);
    throw error;
  }
}

main().catch((error) => console.error('Error:', error));
