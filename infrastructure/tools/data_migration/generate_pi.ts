import { DataSource } from 'typeorm';
import { dbCredentials } from './constants';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';

const NUM_FAKE_PIS = 200;

const dealerToImporterMap = new Map<string, string>([
  ['7029906', '7000000'],
  ['2630070', '2630000'],
  ['2110434', '2110000'],
  ['7000680', '7000000'],
  ['8233023', '8230000'],
  ['7840073', '7840000'],
  ['7840058', '7840000'],
  ['2053325', '2050000'],
  ['8233021', '8230000'],
  ['8235011', '8230000'],
  ['4500248', '4500000'],
  ['4503001', '4500000'],
  ['2571041', '2570000'],
  ['1140020', '9690000'],
  ['8234003', '8230000'],
  ['4509901', '4500000'],
  ['2054176', '2050000'],
  ['9522227', '9520000'],
  ['2571601', '2570000'],
  ['1140040', '9690000'],
  ['8404500', '8400000'],
  ['4500940', '4500000'],
  ['2600006', '2600000'],
  ['7840099', '7840000'],
  ['5000120', '5000000'],
  ['8233020', '8230000'],
  ['2198051', '2190000'],
  ['2090050', '2090000'],
]);
const dealerNumbers = Array.from(dealerToImporterMap.keys());
const cnrs = ['C00', 'DE', 'IT'];

async function main(): Promise<void> {
  const dataSource = await new DataSource({
    type: 'aurora-postgres',
    database: dbCredentials.dbname,
    secretArn: dbCredentials.secretArn,
    resourceArn: dbCredentials.clusterArn,
    region: dbCredentials.region,
    synchronize: false,
    logging: false,
    entities: [CoraPurchaseIntentionModel],
  }).initialize();

  const repository = dataSource.getRepository(CoraPurchaseIntentionModel);

  for (let i = 0; i < NUM_FAKE_PIS; i++) {
    const dealer_number = dealerNumbers[i % dealerNumbers.length];
    const importer_number = dealerToImporterMap.get(dealer_number) ?? 'UNKNOWN';

    const cnr = cnrs[i % cnrs.length];

    const model_year = `${2023 + (i % 3)}`;
    const quota_month = `${model_year}-${String(1 + (i % 12)).padStart(2, '0')}`;
    const prefixes = ['DEC', 'CEC', 'CH', 'PE', 'DE', 'SE', 'NL'];
    const prefix = prefixes[i % prefixes.length];
    const numeric = Math.floor(10000 + Math.random() * 90000); // 5 cifre
    const purchaseIntentionId = `${prefix}${numeric}`;

    const pi: CoraPurchaseIntentionModel = {
      purchase_intention_id: purchaseIntentionId,
      created_by: 'GENERATOR',
      created_at: new Date(2024, i % 12, 2) as unknown as string,
      modified_by: 'GENERATOR',
      modified_at: new Date() as unknown as string, //modified_at: new Date().toISOString(),
      order_type: 'LF',
      shipping_code: '1',
      model_type: '992110',
      model_year,
      quota_month,
      dealer_number,
      importer_number,
      seller: '',
      business_partner_id: `BP${10000 + i}`,
      importer_code: 'DE',
      receiving_port_code: '',
      order_status_pvms_code: 'O070',
      vehicle_status_pvms_code: 'V070',
      requested_dealer_delivery_date: null,
      cnr,
      is_converted: false,
      vehicle_configuration_pvmsnext: JSON.stringify({
        Id: '',
        Vin: '',
        Taxes: '0.00',
        VehicleTag: { results: [] },
        VehicleOption: {
          Id: '',
          OptionLocal: { results: [] },
          OptionZoffer: { results: [] },
          OptionPackage: { results: [] },
          OptionExclusive: { results: [] },
          OptionIndividual: { results: [] },
          OptionCustomTailoring: { results: [] },
        },
        VehicleWLTPBody: { results: [] },
        VehicleWLTPHeader: { results: [] },
        ProductionDate: '0000-00-00',
        TotalPriceGross: '0.00',
      }),
    };

    try {
      await repository.save(pi);
      console.log(`Inserted PI ${pi.purchase_intention_id}`);
    } catch (err) {
      console.error(`Error inserting PI ${pi.purchase_intention_id}`, err);
    }
  }
}

main().catch(console.error);
// const idsToDelete = [
//   'DEC69225',
//   'CEC23112',
//   'CH40228',
//   'PE58210',
//   'DE69508',
//   'TEST51291',
//   'SE33218',
//   'NL90516',
//   'DEC98276',
//   'CEC14461',
//   'CH38321',
//   'PE90126',
//   'DE78657',
//   'TEST19435',
//   'SE41726',
//   'NL30129',
//   'DEC33802',
//   'CEC27223',
//   'CH56174',
//   'PE84021',
// ];

// async function deleteInsertedPIs() {
//   const dataSource = await new DataSource({
//     type: 'aurora-postgres',
//     database: dbCredentials.dbname,
//     secretArn: dbCredentials.secretArn,
//     resourceArn: dbCredentials.clusterArn,
//     region: dbCredentials.region,
//     synchronize: false,
//     logging: false,
//     entities: [CoraPurchaseIntentionModel],
//   }).initialize();

//   const repository = dataSource.getRepository(CoraPurchaseIntentionModel);

//   for (const id of idsToDelete) {
//     try {
//       const pi = await repository.findOneBy({ purchase_intention_id: id });
//       if (pi) {
//         await repository.remove(pi);
//         console.log(`Deleted PI ${id}`);
//       } else {
//         console.warn(`PI ${id} not found`);
//       }
//     } catch (err) {
//       console.error(`Error deleting PI ${id}`, err);
//     }
//   }

//   await dataSource.destroy();
// }

// deleteInsertedPIs().catch(console.error);
