import { DataSource, Repository } from 'typeorm';
import { CoraPurchaseIntentionModel } from '../../lib/entities/purchase-intention-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../lib/entities/new-car-order-model';
import { AttributeValue, DynamoDBClient, ScanCommand, ScanCommandInput } from '@aws-sdk/client-dynamodb';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import { CoraNCODatabaseObject } from './old_types/new_car_order_types';
import { ncoConfigApiToDbObj } from '../../lambda/utils/utils-typeorm';
import { readdir, readFile, writeFile } from 'fs/promises';
import { dbCredentials } from './constants';
import * as path from 'path';

const TABLE_NAME = 'cora-prod-new-car-order';
const FAILED_DIR = 'failed_orders';

async function main(): Promise<void> {
  const dataSource = await new DataSource({
    type: 'aurora-postgres',
    database: dbCredentials.dbname,
    secretArn: dbCredentials.secretArn,
    resourceArn: dbCredentials.clusterArn,
    region: dbCredentials.region,
    synchronize: false,
    logging: false,
    entities: [CoraPurchaseIntentionModel, NewCarOrderModel, NcoConfigurationModel, NcoConfigOrderedOptionsModel],
  }).initialize();

  const client = new DynamoDBClient({ region: 'eu-west-1' });

  const res = await getAllFromTable(TABLE_NAME, client, dataSource);
  console.log(res[0]);
  // Retry failed orders:
  // const ncos = await readJsonFilesFromDir(`${__dirname}/${FAILED_DIR}/`);
  // for (const _item of ncos) {
  //   console.log('Saving', _item.pk_new_car_order_id);
  //   await writeToPostgres(_item, dataSource.getRepository(NewCarOrderModel));
  // }
}

async function getAllFromTable(tableName: string, client: DynamoDBClient, dataSource: DataSource): Promise<string[]> {
  const items: string[] = [];
  let exclusiveStartKey: Record<string, AttributeValue> | undefined;

  try {
    do {
      const params: ScanCommandInput = {
        TableName: tableName,
        ExclusiveStartKey: exclusiveStartKey,
      };

      const command = new ScanCommand(params);
      const response = await client.send(command);

      if (response.Items) {
        for (const item of response.Items) {
          const _item = unmarshall(item) as CoraNCODatabaseObject;
          console.log('Saving', _item.pk_new_car_order_id);
          await writeToPostgres(_item, dataSource.getRepository(NewCarOrderModel));
          items.push(_item.pk_new_car_order_id);
        }
      }
      exclusiveStartKey = response.LastEvaluatedKey;
      console.warn('LastEvaluatedKey', exclusiveStartKey);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } while (exclusiveStartKey);

    return items;
  } catch (error) {
    console.error('Error scanning DynamoDB table:', error);
    throw error;
  }
}

async function writeToPostgres(item: CoraNCODatabaseObject, repository: Repository<NewCarOrderModel>): Promise<void> {
  try {
    const new_model: NewCarOrderModel = {
      ...item,
      created_at: item.created_at ? (new Date(item.created_at) as unknown as string) : undefined,
      modified_at: item.modified_at ? (new Date(item.modified_at) as unknown as string) : undefined,
      requested_dealer_delivery_date: item.requested_dealer_delivery_date
        ? (new Date(item.requested_dealer_delivery_date) as unknown as string)
        : undefined,
      order_invoice_onevms_code: 'null',
      configuration: ncoConfigApiToDbObj(
        item.configuration,
        item.modified_by ?? item.created_by,
        item.pk_new_car_order_id,
      ),
      modified_by: item.modified_by ?? item.created_by,
      order_status_onevms_timestamp_last_change: new Date(item.modified_at ?? item.created_at) as unknown as string,
    };
    await repository.save(new_model);
  } catch (error) {
    console.error(`${item.pk_new_car_order_id} failed`, error);
    await writeObjectToJsonFile(item);
    return;
  }
  return;
}

async function writeObjectToJsonFile(data: CoraNCODatabaseObject): Promise<void> {
  try {
    const jsonContent = JSON.stringify(data, null, 2);
    await writeFile(`${__dirname}/${FAILED_DIR}/${data.pk_new_car_order_id}.json`, jsonContent, 'utf-8');
  } catch (error) {
    console.error('Error writing JSON to file:', error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function readJsonFilesFromDir(dirPath: string): Promise<CoraNCODatabaseObject[]> {
  try {
    const files = await readdir(dirPath);
    const jsonFiles = files.filter((file) => file.endsWith('.json'));

    const data = await Promise.all(
      jsonFiles.map(async (file) => {
        const filePath = path.join(dirPath, file);
        const content = await readFile(filePath, 'utf-8');
        return JSON.parse(content) as CoraNCODatabaseObject;
      }),
    );

    return data;
  } catch (error) {
    console.error('Error reading JSON files:', error);
    return [];
  }
}

main().catch((error) => console.error('Error:', error));
