#!/bin/bash

echo "Fetching Lambda functions with triggers..."

# Get a list of all Lambda function names
function_names=$(aws lambda list-functions --query "Functions[*].FunctionName" --output text)

# Loop through each function
for function in $function_names; do
    has_triggers=false

    # Check for event source mappings
    event_mappings=$(aws lambda list-event-source-mappings --function-name "$function" --query "EventSourceMappings[?EventSourceArn!=null].[EventSourceArn,State]" --output table)
    if [ -n "$event_mappings" ]; then
        has_triggers=true
        echo "Function: $function"
        echo "  Event Source Mappings:"
        echo "$event_mappings"
    fi

    # Check for CloudWatch Events rules targeting the function
    rules=$(aws events list-rule-names-by-target --target-arn "arn:aws:lambda:eu-west-1:897501187430:function:$function" --query "RuleNames" --output text)
    if [ -n "$rules" ]; then
        has_triggers=true
        echo "Function: $function"
        echo "  CloudWatch Events Rules:"
        for rule in $rules; do
            targets=$(aws events list-targets-by-rule --rule "$rule" --query "Targets[*].[Id,Arn]" --output table)
            echo "    Rule: $rule"
            echo "$targets"
        done
    fi

    # Output only if triggers were found
    if [ "$has_triggers" = true ]; then
        echo "----------------------------------------"
    fi
done
