/**
 * 1. Get config from PVMS
 * 2. Overwrite config in DynamoDB
 * 3. Send Config to KCC for translation
 */

import { CoraNCOConfiguration } from '../data_migration/old_types/new_car_order_types';
import { OmConfigApiAdapter } from '../../lambda/utils/om-config-api';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs';
import { KafkaSecret, secretCache } from '../../lambda/utils/secret-cache';
import {
  NcoExportActionType,
  KafkaObjsWrapper,
  KafkaObjTyp,
  PvmsConfigKafkaObject,
} from '../../lambda/backend/export-nco/types';
import { KafkaAdapter } from '../../lambda/utils/kafka';
import { writeFile } from 'fs/promises';
import { Consumer, Kafka } from 'kafkajs';
import { DataSource, EntityManager } from 'typeorm';
import {
  NewCarOrderModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
} from '../../lib/entities/new-car-order-model';
import { dbCredentials } from '../data_migration/constants';
import { ncoConfigApiToDbObj, saveNcosWithAuditTrail } from '../../lambda/utils/utils-typeorm';
import * as readline from 'readline';
import { NewCarOrderAuditTrailModel } from '../../lib/entities/new-car-order-audit-trail-model';
import { OneVmsSourceSystemKey } from '../../lib/types/process-steering-types';

interface NCO {
  id: string;
  vguid: string;
  done: boolean;
}
const ncos: NCO[] = [
  {
    id: 'NCOID',
    vguid: 'vguid',
    done: false,
  },
];
const logger = new KasLambdaLogger('Manual_Script');

// IMPORTANT: Set this to the corresponding Change Request ID which is used as username for the change of the NewCarOrder
const CRQ_ID = 'INCxxxxxxxxxxxx';

process.env.EXPORT_NCO_SQS_URL = 'https://sqs.eu-west-1.amazonaws.com/************/ExportNcoQueue';
const CONFIG_API_SECRET_ARN = 'arn:aws:secretsmanager:eu-west-1:************:secret:CoraConfigApiSecret-tLnBlQ';
const KAFKA_BROKERS = ['pkc-zxm13.eu-west-1.aws.confluent.cloud:9092'];
const KAFKA_SECRET_ARN = 'arn:aws:secretsmanager:eu-west-1:************:secret:cora-streamzilla-kafka-secret-KLPfzR';
// process.env.EXPORT_NCO_SQS_URL = 'https://sqs.eu-west-1.amazonaws.com/************/ExportNcoQueue';
// const CONFIG_API_SECRET_ARN = 'arn:aws:secretsmanager:eu-west-1:************:secret:CoraConfigApiSecret-DGLyt4';
// const KAFKA_BROKERS = ['pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092'];
// const KAFKA_SECRET_ARN = 'arn:aws:secretsmanager:eu-west-1:************:secret:cora-streamzilla-kafka-secret-GduU4N';
const KAFKA_TOPIC_CONFIG_REQUEST = 'FRA_one_vms_kcc_translate_request';
const KAFKA_TOPIC_CONFIG_RESPONSE = 'FRA_one_vms_kcc_translate_response';
secretCache.initCache(CONFIG_API_SECRET_ARN, KAFKA_SECRET_ARN);
const configApiAdapter = new OmConfigApiAdapter({
  omConfigApiSecretArn: CONFIG_API_SECRET_ARN,
  logger,
});
//init kafka adapter
const kafkaAdapterConfigRequest = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: KAFKA_SECRET_ARN,
  logger,
});

const configRequests: Record<string, NewCarOrderModel> = {};

async function main(): Promise<void> {
  const dataSource = await new DataSource({
    type: 'aurora-postgres',
    database: dbCredentials.dbname,
    secretArn: dbCredentials.secretArn,
    resourceArn: dbCredentials.clusterArn,
    region: dbCredentials.region,
    synchronize: false,
    logging: false,
    entities: [NewCarOrderModel, NewCarOrderAuditTrailModel, NcoConfigurationModel, NcoConfigOrderedOptionsModel],
  }).initialize();

  const consumer = await consumeKccResponse(dataSource);
  // Graceful shutdown
  const shutdown = async (): Promise<void> => {
    try {
      console.log('Shutting down consumer...');
      await consumer.disconnect();
      await kafkaAdapterConfigRequest.disconnect();
      await dataSource.destroy();
      console.log('Kafka consumer disconnected');
    } catch (error) {
      console.error('Error during shutdown:', error);
    }
  };

  // Wrap shutdown to handle promises safely
  process.on('SIGINT', () => {
    shutdown().catch((error) => {
      console.error('Error during shutdown handling SIGINT:', error);
    });
  });

  process.on('SIGTERM', () => {
    shutdown().catch((error) => {
      console.error('Error during shutdown handling SIGTERM:', error);
    });
  });

  for (const nco of ncos) {
    const _nco = await handleNco(nco.id, nco.vguid, dataSource);
    configRequests[_nco.pk_new_car_order_id] = _nco;
  }
}

// Function to confirm the write operation.
async function confirmWrite(config: NcoConfigurationModel): Promise<boolean> {
  console.log('Configuration:');
  console.log(JSON.stringify(config, null, 2));
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    rl.question('Do you want to write this config to RDS? (yes/no) ', (answer: string) => {
      rl.close();
      resolve(answer.trim().toLowerCase().startsWith('y'));
    });
  });
}

async function handleNco(nco_id: string, vguid: string, dataSource: DataSource): Promise<NewCarOrderModel> {
  const nco = await getNcoFromTable(nco_id, dataSource);
  const config = await configApiAdapter.getConfig(vguid, 'europe');
  console.log('Nco', nco);
  console.log('Config', JSON.stringify(config));
  const order_with_new_config = { ...nco, configuration_expire: config };
  await writeObjectToJsonFile(order_with_new_config);
  await sendToKccKafka([order_with_new_config]);
  return order_with_new_config;
}

export async function getNcoFromTable(nco_id: string, dataSource: DataSource): Promise<NewCarOrderModel> {
  const ncoRepo = dataSource.getRepository(NewCarOrderModel);
  const nco = await ncoRepo.findOne({
    where: { pk_new_car_order_id: nco_id },
    relations: ['configuration', 'configuration.ordered_options', 'configuration.ordered_options.content'],
  });
  if (!nco) throw new Error('NCO not found');
  return nco;
}

async function consumeKccResponse(datasource: DataSource): Promise<Consumer> {
  const secret = await secretCache.getSecret<KafkaSecret>(KAFKA_SECRET_ARN);
  const kafka = new Kafka({
    clientId: 'FRA_one_vms_cora_manual_001',
    brokers: KAFKA_BROKERS,
    ssl: true, // Enable SSL if required by your Kafka setup
    sasl: {
      mechanism: 'plain', // Use 'plain' mechanism for SASL/PLAIN
      username: secret.username, // Replace with your SASL username
      password: secret.password, // Replace with your SASL password
    },
  });

  const consumer = kafka.consumer({ groupId: 'FRA_one_vms_cora_manual_005' }); // Replace with your consumer group ID

  const runConsumer = async (): Promise<void> => {
    try {
      // Connect to Kafka
      await consumer.connect();
      console.log('Kafka consumer connected with SASL/PLAIN');

      // Subscribe to the topic
      await consumer.subscribe({ topic: KAFKA_TOPIC_CONFIG_RESPONSE, fromBeginning: false });

      console.log('Consumer subscribed to topic');

      // Consume messages
      await consumer.run({
        // eslint-disable-next-line @typescript-eslint/require-await
        eachMessage: async ({ partition, message }) => {
          const key = message.key?.toString() ?? null;
          const value = message.value?.toString() ?? null;
          console.log(`Partition: ${partition}, Key: ${key}`);
          if (key && value) {
            const config = JSON.parse(value) as { kas: { configuration: CoraNCOConfiguration; model_info: unknown } };
            const _nco = configRequests[key];
            const translatedConfig = ncoConfigApiToDbObj(config.kas.configuration, CRQ_ID, _nco.pk_new_car_order_id);
            _nco.configuration = { ...translatedConfig, pk_config_id: _nco.configuration.pk_config_id };
            _nco.changed_by_system = OneVmsSourceSystemKey.CORA;
            _nco.modified_by = CRQ_ID;

            const confirmed = await confirmWrite(_nco.configuration);
            if (confirmed) {
              await writeObjectToRDS(_nco, datasource);
              console.log(`Written to RDS: ${key}`, JSON.stringify(config.kas.configuration));
            } else {
              console.log(`Write operation skipped for key: ${key}`);
            }

            const _res = ncos.find((nco) => nco.id === key);
            if (_res) {
              _res.done = true;
            }
            console.log('Status', JSON.stringify(ncos));
          }
        },
        partitionsConsumedConcurrently: 1,
      });
    } catch (error) {
      console.error('Error in Kafka consumer:', error);
    }
  };

  await runConsumer();
  return consumer;
}

async function sendToKccKafka(nco_requests: NewCarOrderModel[]): Promise<void> {
  //push pvms configs into config request topic
  try {
    const kafkaObjsWrapper: KafkaObjsWrapper<PvmsConfigKafkaObject> = {
      kafkaObjTyp: KafkaObjTyp.PVMS_CONFIG,
      kafkaObjs: nco_requests.map((nco) => ({
        id: nco.pk_new_car_order_id,
        obj: nco.configuration_expire as Record<string, unknown>,
      })),
      kafkaActionTyp: NcoExportActionType.CREATE,
    };
    logger.log(LogLevel.INFO, `Pushing to Config Request Topic (${KAFKA_TOPIC_CONFIG_REQUEST})`, {
      data: kafkaObjsWrapper,
    });
    await kafkaAdapterConfigRequest.pushObjsToTopic({
      kWrapper: kafkaObjsWrapper,
      topic: KAFKA_TOPIC_CONFIG_REQUEST,
      correlationid: 'NOT_IMPLEMENTED',
    });
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Error writing pvms config to kafka', { data: e });
    throw e;
  }
}

async function writeObjectToRDS(data: NewCarOrderModel, dataSource: DataSource): Promise<void> {
  try {
    console.log('Writing to RDS', data);
    await saveNcosWithAuditTrail(
      dataSource,
      [data.pk_new_car_order_id],
      NcoExportActionType.UPDATE,
      async (transactionManager: EntityManager) => {
        //delete old config first since typeorm is too stupid to do so
        await transactionManager.delete(NcoConfigurationModel, {
          fk_new_car_order_id: data.pk_new_car_order_id,
        });
        const updatedNco = await transactionManager.preload(NewCarOrderModel, data);
        const ncoUpdateRes = await transactionManager.save(updatedNco);
        return ncoUpdateRes ? [ncoUpdateRes] : [];
      },
      logger,
    );
  } catch (error) {
    console.error('Error writing item to RDS:', error);
  }
}

async function writeObjectToJsonFile(data: NewCarOrderModel): Promise<void> {
  try {
    const jsonContent = JSON.stringify(data, null, 2);
    await writeFile(`${__dirname}/tmp/${data.pk_new_car_order_id}.json`, jsonContent, 'utf-8');
  } catch (error) {
    console.error('Error writing JSON to file:', error);
    throw error;
  }
}

main().catch((error) => console.error('Error:', error));
