import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { writeFile } from 'fs/promises';
import path from 'path';

const TABLE_NAME = 'cora-prod-new-car-order-audit';
const PARTITION_KEY_NAME = 'pk_new_car_order_id';
// const SORT_KEY_NAME = "timestamp";

// Get the partition key value from the command-line argument
const partitionKeyValue = process.argv[2];

if (!partitionKeyValue) {
  console.error('Usage: ts-node script.ts <partitionKeyValue>');
  process.exit(1);
}

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);

async function queryItems(): Promise<void> {
  try {
    let lastEvaluatedKey: Record<string, unknown> | undefined;
    const allItems: unknown[] = [];

    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        KeyConditionExpression: '#pk = :pkval',
        ExpressionAttributeNames: {
          '#pk': PARTITION_KEY_NAME,
          // "#sk": SORT_KEY_NAME,
        },
        ExpressionAttributeValues: {
          ':pkval': partitionKeyValue,
        },
        ScanIndexForward: false, // false for descending order
        ExclusiveStartKey: lastEvaluatedKey, // For pagination
      });

      const { Items, LastEvaluatedKey } = await docClient.send(command);

      if (Items) {
        allItems.push(...Items);
      }

      lastEvaluatedKey = LastEvaluatedKey;
    } while (lastEvaluatedKey); // Continue fetching if there are more pages

    const filePath = path.join(__dirname, partitionKeyValue + '.json');
    await writeFile(filePath, JSON.stringify(allItems, null, 2));

    console.log(`Results written to ${filePath}`);
  } catch (error) {
    console.error('Error querying items:', error);
  }
}

queryItems().catch((e) => console.error(e));
