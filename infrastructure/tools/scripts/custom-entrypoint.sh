#!/bin/sh
cp /tmp/server.key /var/lib/postgresql/server.key
cp /tmp/server.crt /var/lib/postgresql/server.crt
chown postgres:postgres /var/lib/postgresql/server.key
chown postgres:postgres /var/lib/postgresql/server.crt
chmod 600 /var/lib/postgresql/server.crt
docker-entrypoint.sh "$@"


# chown -R postgres:postgres /usr/lib/postgresql
# chown -R postgres:postgres /var/lib/postgresql/data
# chmod 700 /var/lib/postgresql/data
# ls -la /var/lib/postgresql/data
# runuser -u postgres -- which postgres
# runuser -u postgres -- postgres -c config_file=/tmp/postgresql.conf
