services:
  postgres:
    image: postgres
    entrypoint: '/custom-entrypoint.sh'
    environment:
      POSTGRES_USER: psuser
      POSTGRES_PASSWORD: testpw
      POSTGRES_DB: coradb
    volumes:
      - ./postgres-certs/server.crt:/tmp/server.crt
      - ./postgres-certs/server.key:/tmp/server.key
      - ./custom-entrypoint.sh:/custom-entrypoint.sh
    ports:
      - '5432:5432'
    command: postgres -c ssl=on -c ssl_cert_file=/var/lib/postgresql/server.crt -c ssl_key_file=/var/lib/postgresql/server.key
