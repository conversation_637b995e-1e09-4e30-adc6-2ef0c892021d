{"compilerOptions": {"target": "ESNext", "module": "CommonJS", "lib": ["ESNext"], "moduleResolution": "Node", "declaration": false, "strict": true, "noEmit": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "inlineSourceMap": true, "inlineSources": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "skipLibCheck": true, "esModuleInterop": true}, "include": ["*/**/*.ts"], "exclude": ["node_modules", "cdk.out"]}