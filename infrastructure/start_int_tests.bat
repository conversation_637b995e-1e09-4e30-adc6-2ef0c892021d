@echo off
REM Grant execute permission (not needed in Windows, so we skip chmod)
REM Execute the script
call connectToBastionHostForIntTest.bat

@REM REM Run Jest tests with the specified configuration
@REM npx jest --testPathPattern=integration --config=jest.int.cjs
@echo off
REM Specify the path of the test file (use relative Windows-style path without quotes)
set TEST_FILE_PATH=infrastructure/lambda/backend/process-steering/event-handler/update-nco/integration.test.ts

REM Run Jest tests with the specified configuration and test file path
npx jest %TEST_FILE_PATH% --config=jest.int.cjs
