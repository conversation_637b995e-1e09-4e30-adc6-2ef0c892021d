[{"created_at": "2023-11-16T00:00:00.000Z", "created_by": "PVMS", "modified_at": "2024-12-01T17:07:26.279Z", "modified_by": "PVMS", "purchase_intention_id": "DEC41751", "order_type": "KF", "shipping_code": "1", "model_type": "98231", "model_year": "2024", "quota_month": "2023-10", "dealer_number": "1234", "importer_number": "importer1", "seller": "1656", "business_partner_id": "", "importer_code": "DE", "receiving_port_code": "", "vehicle_status_code": "V210", "requested_dealer_delivery_date": null, "vehicle_configuration_pvmsnext": {"Id": "", "Cnr": "C02", "Vin": "", "BpId": "", "Taxes": "11159.71", "TeqId": "dc6d1d3b-f764-418a-9d2c-f93b5e9d5818", "Vguid": "051Mkmoc7kwXaHdrS0GO6m", "LeadId": "", "CofferId": "c6fc61a0-dfdc-434d-a74b-c079e255e2b8", "Currency": "€", "Dealernr": "1110040", "TopColor": "0Q", "XTraceId": "", "Modeltype": "95BAU1", "Modelyear": "R", "PpnUserId": "", "PriceDate": "20231116120000", "SapUserId": "", "ConfigName": "231116_R95BAU1_SCHWARZRAMMINGE", "FreeZOffer": "", "Importerid": "DE", "Importernr": "9690000", "VehicleTag": {"results": [{"Key": "KB", "Value": "PCCDP", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')", "type": "YISA8034_CONFIG_SRV.tag"}}]}, "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7kwXaHdrS0GO6m')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7kwXaHdrS0GO6m')", "type": "YISA8034_CONFIG_SRV.Vehicle"}, "PorscheCode": "PR9HKZ96", "ProspectKit": false, "ExteriorColor": "0Q", "InteriorColor": "AF", "VehicleOption": {"Id": "1", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "type": "YISA8034_CONFIG_SRV.Option"}, "OptionLocal": {"results": []}, "OptionZoffer": {"results": []}, "OptionPackage": {"results": []}, "OptionExclusive": {"results": []}, "OptionIndividual": {"results": []}, "OptionCustomTailoring": {"results": []}}, "VehicleStatus": "V210", "ConfigModified": "", "IsDealerConfig": "", "LastUpdateDate": "20231116140316", "ProductionDate": "0000-00-00", "TotalPriceGross": "69895.00", "VehicleWLTPBody": {"results": [{"DataSource": "P", "EngineType": "ICE", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltp_body"}, "Typification": "WLTP_EU", "WLTPBodyWLTPBodyRecord": {"results": [{"DataType": "GENERAL_DATA", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpBodyRecord"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"FuelType": "", "ValueType": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpDataRecord"}, "EnergyManagementType": "", "WLTPDataRecordWLTPValue": {"results": [{"Key": "AERODYNAMIC_DRAG", "Unit": "m2", "Value": "0.951", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "EU_LEER_MAX", "Unit": "kg", "Value": "2160.0", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "EU_LEER_MIN", "Unit": "kg", "Value": "1920.0", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "FRONT_SURFACE_A", "Unit": "m2", "Value": "2.62", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MASS_ACTUAL", "Unit": "kg", "Value": "1923", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MASS_VEHICLE", "Unit": "kg", "Value": "1848", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MASS_VEHICLE_FRONT", "Unit": "kg", "Value": "1029", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MASS_VEHICLE_REAR", "Unit": "kg", "Value": "820", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "TEST_MASS_VEHICLE", "Unit": "kg", "Value": "1997", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "TIRE_ROLLING_RESISTANCE_FRONT", "Unit": "0/00", "Value": "8.4", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "TIRE_ROLLING_RESISTANCE_REAR", "Unit": "0/00", "Value": "8.4", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "TIRE_ROLLING_RESISTANCE_TOTAL", "Unit": "0/00", "Value": "8.4", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F0", "Unit": "N", "Value": "249.1", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F1", "Unit": "N/(km/h)", "Value": "0.286", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "VEHICLE_RESISTANCE_COEFFICENT_F2", "Unit": "N/(km/h)2", "Value": "0.04489", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}]}}]}}, {"DataType": "INTERPOLATIONS", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpBodyRecord"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"FuelType": "PETROL_E10", "ValueType": "CO2", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpDataRecord"}, "EnergyManagementType": "PURE", "WLTPDataRecordWLTPValue": {"results": [{"Key": "COMBINED", "Unit": "g/km", "Value": "228", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "EXTRA_HIGH", "Unit": "g/km", "Value": "232", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "HIGH", "Unit": "g/km", "Value": "198", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "LOW", "Unit": "g/km", "Value": "295", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MEDIUM", "Unit": "g/km", "Value": "222", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}]}}, {"FuelType": "PETROL_E10", "ValueType": "CONSUMPTION", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpDataRecord"}, "EnergyManagementType": "PURE", "WLTPDataRecordWLTPValue": {"results": [{"Key": "COMBINED", "Unit": "l/100km", "Value": "10.1", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "EXTRA_HIGH", "Unit": "l/100km", "Value": "10.3", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "HIGH", "Unit": "l/100km", "Value": "8.8", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "LOW", "Unit": "l/100km", "Value": "13.0", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}, {"Key": "MEDIUM", "Unit": "l/100km", "Value": "9.8", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}]}}]}}, {"DataType": "IP_FAMILY", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpBodyRecord"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"FuelType": "", "ValueType": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpDataRecord"}, "EnergyManagementType": "", "WLTPDataRecordWLTPValue": {"results": [{"Key": "NAME", "Unit": "0/00", "Value": "IP-AP95B034B00AT0S-WP1-1", "ValueTo": "", "ValueFrom": "", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltpValue"}}]}}]}}]}}]}, "CommentsModified": "", "CommissionNumber": "C41751", "CurrentMarketDate": "20240514175635", "VehicleWLTPHeader": {"results": [{"uuid": "9ac2644a-b471-4e69-8c28-b247b15e19df", "DataSource": "P", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='9ac2644a-b471-4e69-8c28-b247b15e19df',DataSource='P')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='9ac2644a-b471-4e69-8c28-b247b15e19df',DataSource='P')", "type": "YISA8034_CONFIG_SRV.wltp_header"}, "check_date": "/Date(1700092800000)/", "return_code": "200", "error_message": "", "response_date": "/Date(1700143360000)/"}]}}, "vehicle_configuration_onevms": null, "cnr": "C02", "dealer_name": "Porsche Zentrum Berlin", "is_converted": false}, {"created_at": "2024-03-04T00:00:00.000Z", "created_by": "PVMS", "modified_at": "2024-12-01T17:07:26.779Z", "modified_by": "PVMS", "purchase_intention_id": "CH755160", "order_type": "LF", "shipping_code": "1", "model_type": "98231", "model_year": "2024", "quota_month": "2024-02", "dealer_number": "1234", "importer_number": "importer1", "seller": "", "business_partner_id": "0020001980", "importer_code": "CH", "receiving_port_code": "", "vehicle_status_code": "V210", "requested_dealer_delivery_date": null, "vehicle_configuration_pvmsnext": {"Id": "", "Cnr": "C02", "Vin": "", "BpId": "20001980", "Taxes": "5424.98", "TeqId": "", "Vguid": "051Mkmoc7jwsnyP2Br2RP0", "LeadId": "", "CofferId": "", "Currency": "CHF", "Dealernr": "5000001", "TopColor": "0Q", "XTraceId": "", "Modeltype": "982120", "Modelyear": "R", "PpnUserId": "", "PriceDate": "20240304120000", "SapUserId": "", "ConfigName": "240304_R982120_MÜLLERHARALD2", "FreeZOffer": "", "Importerid": "CH", "Importernr": "5000000", "VehicleTag": {"results": [{"Key": "", "Value": "", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('')", "type": "YISA8034_CONFIG_SRV.tag"}}]}, "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwsnyP2Br2RP0')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwsnyP2Br2RP0')", "type": "YISA8034_CONFIG_SRV.Vehicle"}, "PorscheCode": "", "ProspectKit": false, "ExteriorColor": "0Q", "InteriorColor": "AD", "VehicleOption": {"Id": "1", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "type": "YISA8034_CONFIG_SRV.Option"}, "OptionLocal": {"results": []}, "OptionZoffer": {"results": []}, "OptionPackage": {"results": []}, "OptionExclusive": {"results": []}, "OptionIndividual": {"results": [{"Id": "744", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('744')", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('744')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}]}, "OptionCustomTailoring": {"results": []}}, "VehicleStatus": "V210", "ConfigModified": "", "IsDealerConfig": "X", "LastUpdateDate": "20240304154516", "ProductionDate": "0000-00-00", "TotalPriceGross": "72400.00", "VehicleWLTPBody": {"results": []}, "CommentsModified": "", "CommissionNumber": "755160", "CurrentMarketDate": "20240514175539", "VehicleWLTPHeader": {"results": []}}, "vehicle_configuration_onevms": null, "cnr": "C02", "dealer_name": "Porsche Zentrum Schinznach-Bad", "is_converted": false}, {"created_at": "2024-03-21T00:00:00.000Z", "created_by": "PVMS", "modified_at": "2024-12-01T17:07:26.983Z", "modified_by": "PVMS", "purchase_intention_id": "PCNF07125", "order_type": "KF", "shipping_code": "1", "model_type": "992110", "model_year": "2024", "quota_month": "2024-08", "dealer_number": "7000050", "importer_number": "7000000", "seller": "ANWENWEN", "business_partner_id": "6002454206", "importer_code": "PCN", "receiving_port_code": "", "vehicle_status_code": "V999", "requested_dealer_delivery_date": null, "vehicle_configuration_pvmsnext": {"Id": "", "Cnr": "C33", "Vin": "WP0AB2A97NS229691", "BpId": "6002454206", "Taxes": "204179.31", "TeqId": "", "Vguid": "051Mkte77jwvxJqW7DVepm", "LeadId": "665661", "CofferId": "", "Currency": "￥", "Dealernr": "7000050", "TopColor": "A1", "XTraceId": "", "Modeltype": "992110", "Modelyear": "R", "PpnUserId": "", "PriceDate": "20240321120000", "SapUserId": "", "ConfigName": "R992110_240321_PCN", "FreeZOffer": "", "Importerid": "PCN", "Importernr": "7000000", "VehicleTag": {"results": [{"Key": "", "Value": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('')", "type": "YISA8034_CONFIG_SRV.tag"}}]}, "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkte77jwvxJqW7DVepm')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkte77jwvxJqW7DVepm')", "type": "YISA8034_CONFIG_SRV.Vehicle"}, "PorscheCode": "", "ProspectKit": false, "ExteriorColor": "A1", "InteriorColor": "AP", "VehicleOption": {"Id": "1", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "type": "YISA8034_CONFIG_SRV.Option"}, "OptionLocal": {"results": []}, "OptionZoffer": {"results": []}, "OptionPackage": {"results": []}, "OptionExclusive": {"results": []}, "OptionIndividual": {"results": [{"Id": "2ZF", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('2ZF')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('2ZF')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}, {"Id": "46J", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('46J')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('46J')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}, {"Id": "8IU", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('8IU')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('8IU')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}, {"Id": "9R1", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('9R1')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('9R1')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}, {"Id": "QJ4", "ContentOf": "", "SortOrder": "", "UpgradeTo": "", "PagComment": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('QJ4')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('QJ4')", "type": "YISA8034_CONFIG_SRV.Individual"}, "DealerComment": "", "ImporterComment": ""}]}, "OptionCustomTailoring": {"results": []}}, "VehicleStatus": "V999", "ConfigModified": "", "IsDealerConfig": "", "LastUpdateDate": "20240321100057", "ProductionDate": "2024-03-21", "TotalPriceGross": "1480300.00", "VehicleWLTPBody": {"results": [{"DataSource": "B", "EngineType": "", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='',DataSource='B')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='',DataSource='B')", "type": "YISA8034_CONFIG_SRV.wltp_body"}, "Typification": "", "WLTPBodyWLTPBodyRecord": {"results": []}}]}, "CommentsModified": "", "CommissionNumber": "F07125", "CurrentMarketDate": "20240514235702", "VehicleWLTPHeader": {"results": [{"uuid": "005056BB8E811EDEB9EE05F40844B2DA", "DataSource": "B", "__metadata": {"id": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='005056BB8E811EDEB9EE05F40844B2DA',DataSource='B')", "uri": "https://sapk16-lb.porsche.org:7210/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='005056BB8E811EDEB9EE05F40844B2DA',DataSource='B')", "type": "YISA8034_CONFIG_SRV.wltp_header"}, "check_date": "/Date(1725062400000)/", "return_code": "204", "error_message": "CORE:Incomplete data for typification RDW", "response_date": "/Date(1711017953312)/"}]}}, "vehicle_configuration_onevms": null, "cnr": "C33", "dealer_name": "Porsche Centre Beijing Yizhuang", "is_converted": true}]