import * as cdk from 'aws-cdk-lib';
import { Duration, RemovalPolicy } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import {
  CrossAccountBackupSource,
  RdsToS3Ex<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s<PERSON><PERSON><PERSON><PERSON>,
  KasS3Bucket,
} from '@kas-resources/constructs';
import { aws_ssm as ssm, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';

/**
 * ## DisasterRecoveryStack
 *
 * This construct automates Aurora RDS disaster recovery by:
 * - Creating a backup vault and backup S3 bucket in the source account.
 * - **Exporting database data** using `pg_dump` and storing it in the backup S3 bucket.
 * - **Creating recovery points** of the backup S3 Bucket using AWS Backup Plan.
 * - **Copying backups** from the source vault to the target vault in a separate AWS account.
 * - **Scheduling and orchestrating** these processes using AWS Step Functions and AWS Backup Plan.
 *
 * @remarks
 * ## Setup Requirements:
 * Before deploying, ensure the target AWS account is correctly configured:
 * 1. **Deployment of CDK application**
 *    - The target AWS account must be **bootstrapped** before deployment.
 *    - Deploy this CDK application before attempting a backup restore.
 * 2. **Target Backup Vault**
 *    - The **backup vault** must be **manually created** in the target AWS account.
 *    - The vault ARN (`targetBackupVaultArn`) must be provided in this app’s configuration file.
 * 3. **Vault Policy**
 *    - The target vault must allow `backup:CopyIntoBackupVault` from the **source account**.
 * 4. **S3 Bucket in Target Account**
 *    - Required for restoring data from S3 after copy action of the backup.
 *
 *
 * @example
 * Example **disasterRecovery** configuration in config.ts file:
 * ```typescript
 * disasterRecovery: {
 *   applicationShortName: Constants.APPLICATION_SHORT_NAME,
 *   targetAccount: '********',
 *   targetBackupVaultArn: `arn:aws:backup:${Constants.BACKUP_REGION}:********:backup-vault:${Constants.BACKUP_TARGET_VAULT_NAME}`,
 *   backupName: 'CoraDBBackup',
 *   backupTags: [{ key: 'cross-account-backup', value: 'true' }],
 *   // Schedule for full backup and cross-account copy process.
 *   backupCronSchedule?: { minute: '0', hour: '0', month: '*', weekDay: '1' } // Default if not set: Sundays at 1 AM
 * }
 * ```
 */
export class DisasterRecoveryStack extends cdk.Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const auroraSecurityGroupID = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraSecurityGroupPName,
    );
    const auroraSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraSecurityGroup',
      auroraSecurityGroupID,
    );

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const globalS3KeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalS3KmsKeyArnPName,
    );
    const globalS3Key = KasKmsKey.fromKeyArn(this, 'GlobalRdsKey', globalS3KeyArn);

    const auroraAdminSecretArn: string = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAdminSecretArnPName,
    );
    const auroraAdminSecret = KasSecret.fromSecretAttributes(this, id + 'auroraAdminSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraAdminSecretArn,
    });

    new CrossAccountBackupSource(this, 'source', props.disasterRecovery!);

    const bucketName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-disaster-recovery-bucket`;
    const backupBucket = new KasS3Bucket(this, 'BackupBucket', {
      bucketName: bucketName,
      versioned: true,
      autoDeleteObjects: true,
      removalPolicy: RemovalPolicy.DESTROY,
      encryptionKey: globalS3Key,
      lifecycleRules: [
        {
          id: 'cleanup_old_exports',
          enabled: true,
          expiration: Duration.days(90),
        },
      ],
    });
    props.disasterRecovery?.backupTags.forEach((tag) => cdk.Tags.of(backupBucket).add(tag.key, tag.value));

    new RdsToS3Exporter(this, 'exporter', {
      databaseClusterSecret: auroraAdminSecret,
      databaseClusterSecurityGroup: auroraSecurityGroup,
      databaseClusterVpc: props.vpc,
      exportJobSubnet: props.vpc.privateSubnets[0],
      exportBucket: backupBucket,
      logGroupKeyArnPName: props.globalParameterNames.logGroupKmsKeyArnPName,
      stage: props.stage,
      kmsKey: globalS3Key,
      applicationShortName: props.applicationNameToAuthorize,
    });
  }
}
