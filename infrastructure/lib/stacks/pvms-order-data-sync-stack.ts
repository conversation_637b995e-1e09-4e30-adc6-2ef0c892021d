import { Stack, Duration, aws_ssm, Removal<PERSON><PERSON>y, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import {
  KafkaConsumer<PERSON><PERSON><PERSON><PERSON>,
  Kas<PERSON><PERSON>ey,
  Kas<PERSON>odejsFunction,
  KasSec<PERSON>,
  KasSnsTopic,
  KasSqsQueue,
} from '@kas-resources/constructs';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { Constants } from '../utils/constants';
import { AuthenticationMethod, SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { SnsDestination } from 'aws-cdk-lib/aws-lambda-destinations';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { GlobalStack } from './global-stack';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { OneVmsEventKey } from '../types/process-steering-types';

export class PVMSOrderDataSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const auroraReaderSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraReaderSecretArnPName,
    );
    const auroraReaderSecret = KasSecret.fromSecretAttributes(this, id + 'auroraReaderSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraReaderSecretArn,
    });

    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    //import config api secret
    const _configApiSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.configApiSecretArnPName,
    );
    const configApiSecret = KasSecret.fromSecretAttributes(this, id + 'CoraConfigApiSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _configApiSecretArn,
    });

    // Kafka Parameters
    const _kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );

    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });

    // Import global health monitoring topic and key
    const healthMonitoringTopicKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.healthMonitoringTopicKmsKeyArnPName,
    );
    const healthMonitoringTopicKey = KasKmsKey.fromKeyArn(
      this,
      'healthMonitoringTopicKey',
      healthMonitoringTopicKeyArn,
    );

    const _healthMonitoringTopicArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.healthMonitoringTopicArnPName,
    );
    const healthMonitoringTopic = KasSnsTopic.fromTopicArn(
      this,
      id + 'CoraHealthMonitoringTopicInterface',
      _healthMonitoringTopicArn,
    );

    // Import global sqs kms key key
    const globalSqsKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );
    const globalSqsKmsKey = KasKmsKey.fromKeyArn(this, 'globalSqsKmsKey', globalSqsKmsKeyArn);

    //Dlq for the pvms pi import
    const pvmsPiDataInboundDlq = new KasSqsQueue(this, 'PvmsPiDataInboundDlq', {
      queueName: 'PvmsPiDataInboundDlq',
      encryptionMasterKey: globalSqsKmsKey,
    });

    //Sqs queue that the pvms kafka consumer sends PI Objects from PVMS topic into
    const pvmsPiDataInboundQueue = new KasSqsQueue(this, 'PvmsPiDataInboundQueue', {
      visibilityTimeout: Duration.seconds(60),
      queueName: 'PvmsPiDataInboundQueue',
      encryptionMasterKey: globalSqsKmsKey,
      deadLetterQueue: { queue: pvmsPiDataInboundDlq, maxReceiveCount: 3 },
    });

    const eventDispatcherInboundQueueArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.eventDispatcherInboundQueueArnPName,
    );
    const eventDispatcherInboundQueue = KasSqsQueue.fromQueueAttributes(this, 'EventDispatcherInboundQueue', {
      queueArn: eventDispatcherInboundQueueArn,
      keyArn: globalSqsKmsKey.keyArn,
    });

    // Retrieve all necessary security groups
    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    const externalAccessSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.externalAccessSecurityGroupPName,
    );
    const externalAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ExternalAccessSecurityGroup',
      externalAccessSecurityGroupId,
    );

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeormLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    const importPVMSOrderDataLambda = new KafkaConsumerLambda(this, `EventImport-${OneVmsEventKey.PVMS_IMPORT}`, {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-event-import-${OneVmsEventKey.PVMS_IMPORT}`,
      entry: 'lambda/backend/process-steering/event-import/pvms/index.ts',
      handler: 'handler',
      description:
        'Consumes PVMS inbound events, parses, validates and spreads them accordingly to the corresponding Purchase Intention or NCO event handler.',
      environment: {
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        AURORA_SECRET_ARN: auroraReaderSecret.secretArn,
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        DISPATCHER_QUEUE_URL: eventDispatcherInboundQueue.queueUrl,
        PI_IMPORT_QUEUE_URL: pvmsPiDataInboundQueue.queueUrl,
        ENABLE_NCO_INVOICE_MAPPING: props.featureFlags.ncoInvoiceMapping.toString(),
      },
      timeout: Duration.minutes(1),
      memorySize: 256,
      customManagedKey: logGroupKey,
      kafkaSecret: kafkaSecret,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: `FRA_one_vms_cora_pvms_sync_consumer_group_${props.stage}_008`,
      kafkaImportTopic: props.kafkaParameters.pvmsOrderDataTopic,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      //send kafka consumption errors directly to health monitoring topic
      onFailureDestination: new SnsDestination(healthMonitoringTopic),
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [auroraAccessSecurityGroup, kafkaSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [typeormLayer],
      stage: props.stage,
    });

    eventDispatcherInboundQueue.grantSendMessages(importPVMSOrderDataLambda);
    pvmsPiDataInboundQueue.grantSendMessages(importPVMSOrderDataLambda);
    kafkaSecret.grantRead(importPVMSOrderDataLambda);
    auroraReaderSecret.grantRead(importPVMSOrderDataLambda);

    healthMonitoringTopic.grantPublish(importPVMSOrderDataLambda);
    healthMonitoringTopicKey.grantEncryptDecrypt(importPVMSOrderDataLambda);

    //create lambda that consumes the PVMS PI data inbound queue and upserts the PIs into rds
    const upsertPvmsPisLambda = new KasNodejsFunction(this, 'UpsertPvmsPisLambda', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/process-steering/event-import/pvms/pi-upsert-event-handler/index.ts',
      handler: 'handler',
      environment: {
        CONFIG_API_SECRET_ARN: configApiSecret.secretArn,
        AURORA_SECRET_ARN: auroraWriterSecret.secretArn,
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
      },
      timeout: Duration.minutes(1),
      customManagedKey: logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-upsert-pvms-pi-data`,
      description: 'Triggered on message in pvms pi data inbound queue',
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [auroraAccessSecurityGroup, kafkaSecurityGroup, externalAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [typeormLayer],
      stage: props.stage,
    });

    //add event source to trigger the pvms pi upsert lambda on new sqs messages
    const upsertPvmsPisEventSource = new SqsEventSource(pvmsPiDataInboundQueue, {
      batchSize: 10,
      enabled: true,
    });
    upsertPvmsPisLambda.addEventSource(upsertPvmsPisEventSource);

    kafkaSecret.grantRead(upsertPvmsPisLambda);
    configApiSecret.grantRead(upsertPvmsPisLambda);
    auroraWriterSecret.grantRead(upsertPvmsPisLambda);
  }
}
