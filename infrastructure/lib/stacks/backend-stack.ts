import {
  aws_certificatemanager as acm,
  aws_apigateway as apigateway,
  aws_iam,
  aws_logs,
  aws_ssm,
  Duration,
  aws_ec2 as ec2,
  aws_lambda as lambda,
  RemovalPolicy,
  aws_route53 as route53,
  aws_route53_targets as route53Targets,
  Stack,
} from 'aws-cdk-lib';

import {
  InternalDeveloperFirewallConstruct,
  KasDefaultLogRetentions,
  KasDynamodbTable,
  KasKmsKey,
  KasNodejsFunction,
  KasSecret,
  KasSqsQueue,
} from '@kas-resources/constructs';
import { PolicyStatement } from 'aws-cdk-lib/aws-iam';

import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AuditTrailEndpointConstruct } from '../constructs/audit-trail-endpoint-construct';
import { AuthorizedDealerConstruct } from '../constructs/authorized-dealer-construct';
import { GetUserPermissionConstruct } from '../constructs/get-user-permission-construct';
import { ListsEndpointsConstruct } from '../constructs/lists-endpoints-construct';
import { MasterDataConstruct } from '../constructs/master-data-construct';
import { ModelTypeConstruct } from '../constructs/model-type-construct';
import { MonitoringEndpointsConstruct } from '../constructs/monitoring-construct';
import { NewCarOrderEndpointConstruct } from '../constructs/order-endpoint-construct';
import { PurchaseIntentionEndpointConstruct } from '../constructs/purchase-intention-endpoint-construct';
import { QuotaEndpointConstruct } from '../constructs/quota-endpoint-construct';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk, CoraBuildingBlockRules, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';

import { GlobalStack } from './global-stack';
import { BuySellTransferEndpointConstruct } from '../constructs/buy-sell-transfer-construct';
import { NotificationCenterEndpointConstruct } from '../constructs/notification-center-endpoint-contruct';

export class BackendStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);
    const vpc = props.vpc;

    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);

    //import global table key
    const coraGlobalDynamoKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoKmsKeyArnPName,
    );
    const coraGlobalDynamoKmsKey = KasKmsKey.fromKeyArn(this, 'globalDynamoKmsKey', coraGlobalDynamoKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    // Import global nco export sqs queue and inbound dispatcher queue
    const globalSqsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );
    const globalNcoExportQueueArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalNcoExportQueueArnPName,
    );
    const globalNcoExportQueue = KasSqsQueue.fromQueueAttributes(this, 'GlobalNcoExportQueue', {
      queueArn: globalNcoExportQueueArn,
      keyArn: globalSqsKeyArn,
    });

    const eventDispatcherInboundQueueArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.eventDispatcherInboundQueueArnPName,
    );
    const eventDispatcherInboundQueue = KasSqsQueue.fromQueueAttributes(this, 'EventDispatcherInboundQueue', {
      queueArn: eventDispatcherInboundQueueArn,
      keyArn: globalSqsKeyArn,
    });

    // Import aurora secrets
    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    const auroraReaderSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraReaderSecretArnPName,
    );
    const auroraReaderSecret = KasSecret.fromSecretAttributes(this, id + 'auroraReaderSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraReaderSecretArn,
    });

    const domainForFrontend = `${props.hostedZoneName}`;
    const corsDomain = `https://${domainForFrontend}`;

    //import all masterdata tables
    //import global cora-md dynamodb kms key
    const coraGlobalDynamoMdKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoMdKmsKeyArnPName,
    );
    const mdGlobalDynamoTableKey = KasKmsKey.fromKeyArn(this, 'globalDynamoMdKmsKey', coraGlobalDynamoMdKmsKeyArn);

    //import all masterdata dynamodb tables with global key
    const impTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
    const impTable = KasDynamodbTable.fromTableAttributes(this, 'ImpTable', {
      tableName: impTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const dlrTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
    const dlrTable = KasDynamodbTable.fromTableAttributes(this, 'DlrTable', {
      tableName: dlrTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const scTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
    const scTable = KasDynamodbTable.fromTableAttributes(this, 'ScTable', {
      tableName: scTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const statusMappingTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_STATUS_MAPPING_TABLE_NAME}`;
    const statusMappingTable = KasDynamodbTable.fromTableAttributes(this, 'StatusMappingTable', {
      tableName: statusMappingTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const otTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
    const otTable = KasDynamodbTable.fromTableAttributes(this, 'OtTable', {
      tableName: otTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const pcTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_PORT_CODE_TABLE_NAME}`;
    const pcTable = KasDynamodbTable.fromTableAttributes(this, 'PcTable', {
      tableName: pcTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    //import cora org rel table
    const coraOrgRelDdbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName,
    );
    const coraOrgRelTable = KasDynamodbTable.fromTableAttributes(this, 'CoraOrgRelTable', {
      tableName: coraOrgRelDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    //import purchase intentions table
    const purchaseIntentionsTable = KasDynamodbTable.fromTableAttributes(this, 'purchaseIntentionsTable', {
      tableName: Constants.buildResourceName(
        props.stage,
        Constants.DYNAMODB_CORA_PURCHASE_INTENTIONS_TABLE_PARAMS.tableName,
      ),
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    const quotaTable = KasDynamodbTable.fromTableAttributes(this, 'QuotaTable', {
      tableName: Constants.buildResourceName(props.stage, Constants.DYNAMODB_QUOTA_TABLE_NAME),
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    const _kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );
    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });
    const _quotaApiSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.quotaApiSecretArnPName,
    );
    const quotaApiSecret = KasSecret.fromSecretAttributes(this, id + 'CoraQuotaApiSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _quotaApiSecretArn,
    });

    // Retrieve all necessary security groups
    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    const externalAccessSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.externalAccessSecurityGroupPName,
    );
    const externalAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ExternalAccessSecurityGroup',
      externalAccessSecurityGroupId,
    );

    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    const vpcEndpointsSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.vpcEndpointsSecurityGroupPName,
    );
    const vpcEndpointsSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'VpcEndpointsSecurityGroup',
      vpcEndpointsSecurityGroupId,
    );

    //create loggroup for apigw access logs
    const apiGwLoggGroup = new aws_logs.LogGroup(this, 'ApiGwBackendLogGroup', {
      logGroupName: props.stage + 'coraBackendApiAccessLogs',
      retention: KasDefaultLogRetentions[props.stage],
      encryptionKey: logGroupKey,
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    const stageName = 'prod';

    // Create the API Gateway
    const api = new apigateway.RestApi(this, `cora-${props.stage}-backend`, {
      description: `Cora OC ${props.stage} Backend API`,
      disableExecuteApiEndpoint: true,
      defaultCorsPreflightOptions: {
        statusCode: 200,
        allowOrigins: [corsDomain],
        allowCredentials: true,
        allowHeaders: [
          'Content-Type',
          'Authorization',
          'x-kas-request-id', //For correlationId
          'X-Amz-Date',
          'X-Api-Key',
          'X-Amz-Security-Token',
        ],
      },
      deployOptions: {
        accessLogDestination: new apigateway.LogGroupLogDestination(apiGwLoggGroup),
        accessLogFormat: apigateway.AccessLogFormat.jsonWithStandardFields(),
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        stageName: stageName,
        throttlingRateLimit: 500,
        throttlingBurstLimit: 1000,
      },
      policy: new aws_iam.PolicyDocument({
        statements: [
          new aws_iam.PolicyStatement({
            actions: ['execute-api:Invoke'],
            resources: [`execute-api:/${stageName}/OPTIONS/*`],
            effect: aws_iam.Effect.ALLOW,
            principals: [new aws_iam.AnyPrincipal()],
          }),
        ],
      }),
    });

    // Import the hosted zone for your domain
    const hostedZone = route53.HostedZone.fromLookup(this, 'HostedZone', {
      domainName: props.hostedZoneName,
    });

    // Create a certificate for the domain
    const certificate = new acm.Certificate(this, 'ApiCertificate', {
      domainName: `api.${domainForFrontend}`,
      validation: acm.CertificateValidation.fromDns(hostedZone),
    });

    // Create custom domain and add base path mapping
    const customDomain = new apigateway.DomainName(this, 'ApiCustomDomain', {
      domainName: `api.${domainForFrontend}`,
      certificate,
      endpointType: apigateway.EndpointType.REGIONAL,
      securityPolicy: apigateway.SecurityPolicy.TLS_1_2,
    });

    new apigateway.BasePathMapping(this, 'ApiBasePathMapping', {
      domainName: customDomain,
      restApi: api,
    });

    // Create a Route53 record to point to the API Gateway
    new route53.ARecord(this, 'ApiGatewayRecord', {
      zone: hostedZone,
      recordName: 'api',
      target: route53.RecordTarget.fromAlias(new route53Targets.ApiGatewayDomain(customDomain)),
    });

    // Create a security group for the authorizer flow
    const authorizerSecurityGroup = new ec2.SecurityGroup(this, 'BackendAuthorizerSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'BackendAuthorizerSecurityGroup',
    });
    authorizerSecurityGroup.addEgressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(443));

    // Create a Lambda function for the authorizer
    const authorizerFunction = new KasNodejsFunction(this, 'LambdaAuthorizer', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-backend-authorizer`,
      entry: 'lambda/backend/authorizer/index.ts',
      handler: 'handler',
      timeout: Duration.seconds(10),
      environment: {
        ISSUER: props.idp.issuer,
        PUBLIC_KEY_ENDPOINT: props.idp.publicKeyUrl,
        CLIENT_ID: props.idp.clientId,
        KAS_AUTH_ENDPOINT_URL: props.kasAuthEndpointUrl,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        PPN_ROLES_WRITE: JSON.stringify(props.ppnRolesWrite),
        PERMISSIONS_DYNAMODB_TABLE_NAME: `cora-md-${props.stage}-RBAM-role-permissions`,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      customManagedKey: logGroupKey,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_AUTH },
      securityGroups: [authorizerSecurityGroup],
      stage: props.stage,
    });
    authorizerFunction.addToRolePolicy(
      new PolicyStatement({
        actions: ['dynamodb:GetItem'],
        resources: [
          `arn:aws:dynamodb:eu-west-1:${props.env.account}:table/cora-md-${props.stage}-RBAM-role-permissions`,
        ],
      }),
    );

    const keyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      `/${props.stage}/cora-md-${props.stage}-RBAM/rbam-table-key-arn`,
    );
    authorizerFunction.addToRolePolicy(
      new PolicyStatement({
        actions: ['kms:Decrypt'],
        resources: [keyArn],
      }),
    );

    // Create RequestAuthorizer for the API Gateway
    const authorizer = new apigateway.RequestAuthorizer(this, 'Authorizer', {
      handler: authorizerFunction,
      identitySources: [apigateway.IdentitySource.header('Cookie')], // Use the 'Cookie' header as the identity source
      resultsCacheTtl: Duration.minutes(5), // Cache duration for the authorizer
    });

    new apigateway.GatewayResponse(this, 'AccessDeniedResponse', {
      restApi: api,
      type: apigateway.ResponseType.ACCESS_DENIED,
      responseHeaders: {
        'Access-Control-Allow-Origin': `'${corsDomain}'`,
        'Access-Control-Allow-Credentials': "'true'",
      },
      statusCode: '403',
      templates: {
        'application/json':
          '{"message": "You are not authorized to execute this action, reason: $context.authorizer.errorMessage"} ',
      },
    });

    // Create a new resource that handles the redirect to the IDP
    new apigateway.GatewayResponse(this, 'UnauthenticatedResponse', {
      restApi: api,
      type: apigateway.ResponseType.UNAUTHORIZED,
      responseHeaders: {
        'Access-Control-Allow-Origin': `'${corsDomain}'`,
        'Access-Control-Allow-Credentials': "'true'",
      },
      statusCode: '401',
      templates: {
        'application/json':
          '{"message": "You are not authorized to access this application, reason: $context.authorizer.errorMessage"} ',
      },
    });

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeORMLayer = lambda.LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    /*
    Definition of the Backend API begins from here (after the Authorizer)
    Add any API Gateway resources and methods here. Make sure to associate the authorizer with the resources/methods.
    */

    //Construct for purchase intentions Endpoints
    new PurchaseIntentionEndpointConstruct(this, 'PurchaseIntentionEndpoint', {
      api,
      authorizer,
      stage: props.stage,
      corsDomain,
      logGroupKey,
      coraOrgRelTable,
      pvmsPurchaseIntentionTable: purchaseIntentionsTable,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      logSubscriptionLambda,
      auroraAccessSecurityGroup,
      vpc,
      vpcEndpointsSecurityGroup,
      auroraReaderSecret: auroraReaderSecret,
      eventDispatcherInboundQueue,
      typeormLayer: typeORMLayer,
      kafkaParameters: { ...props.kafkaParameters, kafkaSecret },
      kafkaSecurityGroup,
    });

    new NotificationCenterEndpointConstruct(this, 'NotificationCenterEndpoint', {
      authorizer,
      stage: props.stage,
      corsDomain,
      paddockCorsDomain: `https://${props.paddockDomainName}`,
      logGroupKey,
      logSubscriptionLambda,
      vpc,
      coraGlobalDynamoKmsKey,
      parentResource: api.root,
      vpcEndpointsSecurityGroup,
    });

    //Construct for the NewCarOrder Endpoints
    new NewCarOrderEndpointConstruct(this, 'NewCarOrderEndpoint', {
      api,
      authorizer,
      stage: props.stage,
      corsDomain,
      logGroupKey,
      quotaApiSecret,
      coraOrgRelTable,
      impTable,
      dlrTable,
      scTable,
      otTable,
      quotaTable,
      statusMappingTable,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      logSubscriptionLambda,
      auroraAccessSecurityGroup,
      externalAccessSecurityGroup,
      vpc,
      auroraReaderSecret: auroraReaderSecret,
      auroraWriterSecret: auroraWriterSecret,
      typeormLayer: typeORMLayer,
      globalNcoExportQueue,
      eventDispatcherInboundQueue,
      featureFlags: props.featureFlags,
      kafkaParameters: { ...props.kafkaParameters, kafkaSecret },
      kafkaSecurityGroup,
    });

    //Construct for buy-sell-transfer
    if (props.featureFlags.buySellTransfer) {
      new BuySellTransferEndpointConstruct(this, 'BuySellTransferEndpoint', {
        api,
        authorizer,
        stage: props.stage,
        logGroupKey,
        coraOrgRelTable,
        corsDomain,
        logSubscriptionLambda,
        vpc,
        auroraAccessSecurityGroup,
        auroraRdsSecret: auroraWriterSecret,
        typeormLayer: typeORMLayer,
        globalNcoExportQueue,
      });
    }

    /*
    Read only API for master data
    */
    new MasterDataConstruct(this, 'MasterDataEndpoint', {
      api: api,
      authorizer: authorizer,
      stage: props.stage,
      corsDomain: corsDomain,
      logGroupKey: logGroupKey,
      coraOrgRelTable,
      impTable,
      dlrTable,
      scTable,
      otTable,
      pcTable,
      statusMappingTable,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      logSubscriptionLambda,
      vpcEndpointsSecurityGroup,
      vpc,
      auroraAccessSecurityGroup,
      auroraReaderSecret,
      typeORMLayer,
    });

    const bossResource = api.root.addResource('boss');

    //Construct for the ModelType from BOSS Endpoints
    new ModelTypeConstruct(this, 'ModelTypeEndpoint', {
      authorizer,
      stage: props.stage,
      corsDomain,
      coraOrgRelTable,
      logGroupKey,
      auroraAccessSecurityGroup: auroraAccessSecurityGroup,
      auroraReaderSecret: auroraReaderSecret,
      auroraWriterSecret: auroraWriterSecret,
      typeormLayer: typeORMLayer,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      kafkaParameters: { ...props.kafkaParameters, kafkaSecret },
      parentResource: bossResource,
      logSubscriptionLambda,
      kafkaSecurityGroup,
      vpcEndpointsSecurityGroup,
      vpc,
    });

    //Construct for the AuthorizedDealer from BOSS Endpoints
    new AuthorizedDealerConstruct(this, 'AuthorizedDealerEndpoint', {
      authorizer,
      stage: props.stage,
      corsDomain,
      coraOrgRelTable,
      impTable,
      logGroupKey,
      parentResource: bossResource,
      logSubscriptionLambda,
      vpc,
      vpcEndpointsSecurityGroup,
    });

    //Construct for Quota Endpoint
    new QuotaEndpointConstruct(this, 'QuotaEndpoint', {
      api,
      authorizer,
      stage: props.stage,
      corsDomain,
      logGroupKey,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      coraGlobalDynamoKmsKey,
      logSubscriptionLambda,
      vpc,
      vpcEndpointsSecurityGroup,
      auroraAccessSecurityGroup,
      auroraRdsSecret: auroraReaderSecret,
      typeormLayer: typeORMLayer,
    });

    //Construct for Lists Endpoints for PI and NCO Lists
    new ListsEndpointsConstruct(this, 'ListsEndpoints', {
      api: api,
      authorizer: authorizer,
      coraOrgRelTable: coraOrgRelTable,
      corsDomain: corsDomain,
      logGroupKey: logGroupKey,
      stage: props.stage,
      coraGlobalDynamoKmsKey: coraGlobalDynamoKmsKey,
      coraMdGlobalDynamoKmsKey: mdGlobalDynamoTableKey,
      logSubscriptionLambda,
      auroraAccessSecurityGroup,
      vpc,
      vpcEndpointsSecurityGroup,
      auroraReaderSecret: auroraReaderSecret,
      typeormLayer: typeORMLayer,
    });

    new AuditTrailEndpointConstruct(this, 'AuditTrailEndpoint', {
      authorizer: authorizer,
      corsDomain: corsDomain,
      logGroupKey: logGroupKey,
      parentResource: api.root,
      stage: props.stage,
      logSubscriptionLambda,
      vpc,
      auroraAccessSecurityGroup,
      auroraReaderSecret: auroraReaderSecret,
      typeormLayer: typeORMLayer,
    });

    new MonitoringEndpointsConstruct(this, 'MonitoringEndPoints', {
      api: api,
      authorizer: authorizer,
      stage: props.stage,
      logGroupKey: logGroupKey,
      corsDomain: corsDomain,
      logSubscriptionLambda,
      vpc,
      vpcEndpointsSecurityGroup,
      auroraReaderSecret: auroraReaderSecret,
      auroraAccessSecurityGroup,
      layers: [typeORMLayer],
    });

    new GetUserPermissionConstruct(this, 'GetUserPermissionConstruct', {
      api: api,
      authorizer: authorizer,
      stage: props.stage,
      logGroupKey: logGroupKey,
      corsDomain: corsDomain,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      logSubscriptionLambda,
      vpc,
      vpcEndpointsSecurityGroup,
    });

    // Lambda function to export Swagger
    if (props.stage !== 'prod') {
      const swaggerDocExporter = new KasNodejsFunction(this, 'SwaggerDocExporter', {
        runtime: ConstantsCdk.NODE_JS_VERSION,
        functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-swagger-doc-exporter`,
        entry: 'lambda/backend/swagger-doc-exporter/index.ts',
        handler: 'handler',
        environment: {
          REST_API_ID: api.restApiId,
        },
        logGroupRemovalPolicy: RemovalPolicy.DESTROY, // Cannot be prod, because of if
        customManagedKey: logGroupKey,
        errHandlingLambda: logSubscriptionLambda,
        bundling: {
          externalModules: LambdaDefaultBundlingExternalModules,
        },
        vpc: vpc,
        vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
        securityGroups: [externalAccessSecurityGroup],
        stage: props.stage,
      });

      const swaggerDocExporterLambdaIntegration = new apigateway.LambdaIntegration(swaggerDocExporter);
      const swaggerDocExporterResource = api.root.addResource('swagger.json');
      swaggerDocExporterResource.addMethod('GET', swaggerDocExporterLambdaIntegration, { authorizer });

      swaggerDocExporter.addToRolePolicy(
        new PolicyStatement({
          actions: ['apigateway:GET'],
          // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
          resources: [`arn:aws:apigateway:${props.env.region}::/restapis/${api.restApiId}/stages/prod/exports/swagger`],
        }),
      );
    }

    const wafLogGroup = new aws_logs.LogGroup(this, 'developerFirewallBackendLogGroup', {
      logGroupName: 'aws-waf-logs-' + props.stage + 'coraBackendWAFTrafficLogs',
      //overwrite default for dev and int because big data be expensive
      retention: props.stage !== 'prod' ? aws_logs.RetentionDays.ONE_WEEK : KasDefaultLogRetentions['prod'],
      encryptionKey: logGroupKey,
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    // WAF that blocks all requests except for the ones coming from the internal developer network
    new InternalDeveloperFirewallConstruct(this, 'InternalDeveloperFirewall', api, wafLogGroup, CoraBuildingBlockRules);
  }
}
