import { Stack, Duration, aws_ssm, RemovalPolicy, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { KasDynamodbTable, KasKmsKey, KasNodejsFunction, KasSecret, KasSqsQueue } from '@kas-resources/constructs';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { Constants } from '../utils/constants';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { GlobalStack } from './global-stack';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export class CSSOrderDataSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    // Kafka Parameters
    const _kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );

    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });

    // Import global sqs kms key key
    const globalSqsKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );
    const globalSqsKmsKey = KasKmsKey.fromKeyArn(this, 'globalSqsKmsKey', globalSqsKmsKeyArn);

    //Dlq for the css pi import
    const cssPiDataInboundDlq = new KasSqsQueue(this, 'CssPiDataInboundDlq', {
      queueName: 'CssPiDataInboundDlq',
      encryptionMasterKey: globalSqsKmsKey,
    });

    //Sqs queue that the pvms kafka consumer sends PI Objects from PVMS topic into
    const cssPiDataInboundQueue = new KasSqsQueue(this, 'CssPiDataInboundQueue', {
      visibilityTimeout: Duration.seconds(60),
      queueName: 'CssPiDataInboundQueue',
      encryptionMasterKey: globalSqsKmsKey,
      deadLetterQueue: { queue: cssPiDataInboundDlq, maxReceiveCount: 3 },
    });

    // Retrieve all necessary security groups
    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    //import cora md importer and dealer dynamodb tables
    const coraGlobalDynamoMdKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoMdKmsKeyArnPName,
    );
    const mdGlobalDynamoTableKey = KasKmsKey.fromKeyArn(this, 'globalDynamoMdKmsKey', coraGlobalDynamoMdKmsKeyArn);
    const impTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
    const dlrTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;

    const impTable = KasDynamodbTable.fromTableAttributes(this, 'ImpTable', {
      tableName: impTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const dlrTable = KasDynamodbTable.fromTableAttributes(this, 'DlrTable', {
      tableName: dlrTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeormLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    //create lambda that consumes the CSS PI data inbound queue and upserts the PIs into rds
    const upsertCssPisLambda = new KasNodejsFunction(this, 'UpsertCssPisLambda', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/process-steering/event-import/css/pi-upsert-event-handler/index.ts',
      handler: 'handler',
      environment: {
        AURORA_SECRET_ARN: auroraWriterSecret.secretArn,
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        TABLE_NAME_MD_IMPORTER: impTable.tableArn,
        TABLE_NAME_MD_DEALER: dlrTable.tableArn,
      },
      timeout: Duration.minutes(1),
      customManagedKey: logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-upsert-css-pi-data`,
      description: 'Triggered on message in css pi data inbound queue',
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [auroraAccessSecurityGroup, kafkaSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [typeormLayer],
      stage: props.stage,
    });

    //add event source to trigger the pvms pi upsert lambda on new sqs messages
    const upsertCssPisEventSource = new SqsEventSource(cssPiDataInboundQueue, {
      batchSize: 10,
      enabled: true,
    });
    upsertCssPisLambda.addEventSource(upsertCssPisEventSource);

    kafkaSecret.grantRead(upsertCssPisLambda);
    auroraWriterSecret.grantRead(upsertCssPisLambda);
    impTable.grantReadData(upsertCssPisLambda);
    dlrTable.grantReadData(upsertCssPisLambda);
  }
}
