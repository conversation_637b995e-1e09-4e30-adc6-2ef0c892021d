import { aws_ssm, Stack, aws_ec2 as ec2, Duration, RemovalPolicy } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Constants } from '../utils/constants';
import { KafkaConsumer<PERSON>ambda, KasKms<PERSON>ey, KasNodejsFunction, KasSecret, KasSqsQueue } from '@kas-resources/constructs';
import { AuthenticationMethod, SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { GlobalStack } from './global-stack';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export class AuditTrailStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    //Import Kafka secret for normal env
    const kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );
    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecret', {
      secretCompleteArn: kafkaSecretArn,
      encryptionKey: globalSecretKey,
    });

    //import Aurora Secret
    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    // Retrieve all necessary security groups
    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    // Import global sqs kms key
    const globalSqsKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );

    // Import global nco export sqs queue
    const globalNcoExportQueueArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalNcoExportQueueArnPName,
    );
    const globalNcoExportQueue = KasSqsQueue.fromQueueAttributes(this, 'GlobalNcoExportQueue', {
      queueArn: globalNcoExportQueueArn,
      keyArn: globalSqsKmsKeyArn,
    });
    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeORMLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    //create lambda that listens on nco audit trail queue and exports ncos
    const exportNcoLambda = new KasNodejsFunction(this, 'exportNcoLambda', {
      vpc: vpc,
      securityGroups: [kafkaSecurityGroup, auroraAccessSecurityGroup],
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-export-nco-lambda`,
      entry: 'lambda/backend/export-nco/index.ts',
      handler: 'index.handler',
      timeout: Duration.seconds(60),
      memorySize: 256,
      runtime: ConstantsCdk.NODE_JS_VERSION,
      customManagedKey: logGroupKey,
      logGroupRemovalPolicy: RemovalPolicy.DESTROY,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      environment: {
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        KAFKA_TOPIC_ONE_VMS: props.kafkaParameters.newCarOrderTopicOneVms,
        KAFKA_TOPIC_PVMS: props.kafkaParameters.newCarOrderTopicPvms,
        AURORA_SECRET_ARN: auroraWriterSecret.secretArn,
      },
      layers: [typeORMLayer],
      stage: props.stage,
    });

    kafkaSecret.grantRead(exportNcoLambda);
    auroraWriterSecret.grantRead(exportNcoLambda);
    globalNcoExportQueue.grantConsumeMessages(exportNcoLambda);

    //add event source to trigger the export lambda on new nco audit trail queue message
    const ncoAuditTrailEventSource = new SqsEventSource(globalNcoExportQueue, {
      batchSize: 10,
      enabled: true,
    });
    exportNcoLambda.addEventSource(ncoAuditTrailEventSource);

    //create lambda that listens on notification topic and exports ncos when the eventhandler returns i.O.
    const exportNcoNotificationLambdaName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-notification-event-consumer-export-function`;
    const groupId = `FRA_one_vms_cora_notification_event_consumer_group_${props.stage}_01`;
    const exportNcoNotificationLambda = new KafkaConsumerLambda(this, 'exportNcoNotificationLambda', {
      vpc: vpc,
      description:
        'Function to consume notification topic and export all successfull eventhandler notifications on a NewCarOrder.',
      securityGroups: [kafkaSecurityGroup, auroraAccessSecurityGroup],
      functionName: exportNcoNotificationLambdaName,
      entry: 'lambda/backend/export-event/index.ts',
      timeout: Duration.seconds(60),
      memorySize: 256,
      runtime: ConstantsCdk.NODE_JS_VERSION,
      customManagedKey: logGroupKey,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      environment: {
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        KAFKA_TOPIC_ONE_VMS: props.kafkaParameters.newCarOrderTopicOneVms,
        KAFKA_TOPIC_PVMS: props.kafkaParameters.newCarOrderTopicPvms,
        KAFKA_TOPIC_P06: props.kafkaParameters.newCarOrderTopicP06,
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        AURORA_SECRET_ARN: auroraWriterSecret.secretArn,
      },
      layers: [typeORMLayer],
      stage: props.stage,
      kafkaImportTopic: props.kafkaParameters.ncoNotificationTopic,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: groupId,
      kafkaSecret: kafkaSecret,
      kafkaSecretKmsKeyAlias: Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME),
      kafkaBatchSize: 200,
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
    });
    kafkaSecret.grantRead(exportNcoNotificationLambda);
    auroraWriterSecret.grantRead(exportNcoNotificationLambda);
  }
}
