import { Stack, StackProps, aws_ssm } from 'aws-cdk-lib';
import { Effect, FederatedPrincipal, PolicyStatement, Role } from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { CoraStackProps } from '../types_cdk/cdk-types';
import { KasKmsKey, KasSecret } from '@kas-resources/constructs';

export interface E2eTestStackProps extends StackProps {
  testRoleName: string;
}
export class E2eTestStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackProps & E2eTestStackProps) {
    super(scope, id, props);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    //import aurora writer secret
    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    //create a Role for E2E test environment in repo acc to assume, to make dynamoDb API calls
    const testRunnerRole = new Role(this, props.testRoleName, {
      roleName: props.testRoleName,
      assumedBy: new FederatedPrincipal(
        `arn:aws:iam::${Stack.of(this).account}:oidc-provider/cicd.skyway.porsche.com`,
        {
          StringEquals: {
            'cicd.skyway.porsche.com:aud': 'https://cicd.skyway.porsche.com',
          },
          StringLike: {
            'cicd.skyway.porsche.com:sub': [
              'project_path:DiTP/agile-release-trains/kas/onevms/cora/*:ref_type:branch:ref:*',
            ],
          },
        },
        'sts:AssumeRoleWithWebIdentity',
      ),
    });
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['kms:Decrypt', 'kms:Encrypt', 'kms:GenerateDataKey*'],
        resources: [`arn:aws:kms:${Stack.of(this).region}:${Stack.of(this).account}:key/*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['dynamodb:BatchWriteItem', 'dynamodb:PutItem', 'dynamodb:GetItem', 'dynamodb:Query'],
        resources: [`arn:aws:dynamodb:${Stack.of(this).region}:${Stack.of(this).account}:table/*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['lambda:InvokeFunction'],
        resources: [`arn:aws:lambda:${Stack.of(this).region}:${Stack.of(this).account}:function:*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['ec2:DescribeInstances'],
        resources: [`*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['rds:DescribeDbClusterEndpoints'],
        resources: [`*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['ssm:StartSession', 'ssm:SendCommand', 'ssm:TerminateSession'],
        resources: [`*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['ssm:GetParameter'],
        resources: [`arn:aws:ssm:${Stack.of(this).region}:${Stack.of(this).account}:parameter/*`],
      }),
    );
    testRunnerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['scheduler:GetSchedule', 'scheduler:DeleteSchedule'],
        resources: [`*`],
      }),
    );
    globalSecretKey.grantDecrypt(testRunnerRole);
    auroraWriterSecret.grantRead(testRunnerRole);
  }
}
