import { CoraPurchaseIntentionModel } from '../entities/purchase-intention-model';
import { FilterModel, SortModelItem } from './ag-grid-ssrm-types';
import { CoraNCOBaseApiResponse } from './new-car-order-types';
import { PvmsOrderDataDTO, VehicleConfigurationPvmsNext } from './pvms-types';

export interface PvmsOrderDataDTOWithConfig extends PvmsOrderDataDTO {
  vehicle_configuration_pvmsnext: VehicleConfigurationPvmsNext; //loaded from config API
}

export interface CoraNCPurchaseIntentionApiResponse extends CoraPurchaseIntentionModel {
  dealer_name?: string;
}

export interface CoraNCPurchaseIntentionQueryApiRequest extends Partial<CoraPurchaseIntentionModel> {
  /**
   * @description Limit for response page size, number as string (queryparam)
   * @pattern ^\d+$
   */
  limit?: string;
  /**
   * @description Offset for response page size, number as string (queryparam)
   * @pattern ^\d+$
   */
  offset?: string;
  colId?: string;
  sort?: string;
}
export type FilterModelRequestPI = Partial<Record<keyof CoraPurchaseIntentionModel, FilterModel>>;
export interface CoraNCPurchaseIntentionRequestBody {
  /** First row requested or undefined for all rows. */
  startRow: number | undefined;
  /** Index after the last row required row or undefined for all rows. */
  endRow: number | undefined;
  filterModel?: FilterModelRequestPI;
  sortModel?: SortModelItem[];
}
export interface CoraPurchaseIntentionQueryApiResponse {
  data: Omit<CoraPurchaseIntentionGetApiResponse, 'configuration' | 'configuration_expire'>[];
  rowCount: number;
}
export type CoraPurchaseIntentionGetApiResponse = CoraNCOBaseApiResponse & {
  dealer_name?: string;
};
