import { SQSBatchResponse } from 'aws-lambda';
import { NewCarOrderModel } from '../entities/new-car-order-model';
import { KasAuthEndpointResponse } from './kas-auth-types';
import { PvmsOrderDataKafkaObject } from './pvms-types';
import { CoraNCOBaseApiRequest } from './new-car-order-types';
import { P06NewCarOrderKafkaObject } from './p06-types';

export enum OneVmsEventKey {
  DEALLOCATE_QUOTA = 'deallocate',
  UPDATE_CORE_DATA = 'update-core-data',
  CANCEL = 'cancel',
  CANCEL_SCHEDULED = 'cancel-scheduled',
  PVMS_IMPORT = 'pvms-import',
  PVMS_UPDATE_NCO = 'pvms-update-data',
  P06_IMPORT = 'p06-import',
  P06_UPDATE_NCO = 'p06-update-data',
  CONVERT_PI = 'convert-pi',
  CREATE = 'create',
  MOVE_TO_INVENTORY = 'move-to-inventory',
  REMOVE_FROM_INVENTORY = 'remove-from-inventory',
  UPDATE_NCO = 'update-data',
  UPDATE_NCO_FROM_PI = 'update-data-from-pi',
  CSS_IMPORT = 'css-import',
  TOTAL_LOSS_REPORT = 'total-loss-report',
  TOTAL_LOSS_PERSIST = 'total-loss-persist',
  TOTAL_LOSS_REVOKE = 'total-loss-revoke',
}

export enum OneVmsSourceSystemKey {
  CORA = 'cora',
  CORA_USER = 'cora_user',
  CORA_SYSTEM = 'cora_system',
  PVMS = 'pvms',
  P06 = 'p06',
  CSS = 'css',
}

export enum SpecialStatusCode {
  ALL = '*',
  CUSTOM = 'CUSTOM',
  NONE = 'null',
}

export interface NcoIdWithModifiedAt {
  pk_new_car_order_id: string;
  modified_at: string;
}

export interface NcoInfo extends NcoIdWithModifiedAt {
  sub_transaction_id: string;
}

export interface InboundEventDispatcherEvent {
  event_type: OneVmsEventKey;
  transaction_id: string; //uuid
  sub_transaction_id?: string; //uuid, only set if event has no ncos_info
  action_at: string; //timestamp
  user_auth_context?: KasAuthEndpointResponse;
  /**
   * @maxItems 100 //is set to 1 if left out for some reason?
   */
  ncos_info?: NcoInfo[];
  payload?: unknown;
  source_system: OneVmsSourceSystemKey;
}

export type InboundApiEventResponseData = Pick<
  InboundEventDispatcherEvent,
  'event_type' | 'transaction_id' | 'action_at'
>;
export interface InboundApiEventResponse {
  message: string;
  data: InboundApiEventResponseData;
}

export interface InboundEventHandlerUnparsableEvent extends InboundEventHandlerEvent {
  errorMessage: string;
  messageId: string;
}

export interface InboundEventHandlerEvent {
  event_type: OneVmsEventKey;
  transaction_id: string; //uuid
  sub_transaction_id: string; //uuid
  action_at: string; //timestamp
  user_auth_context?: KasAuthEndpointResponse;
  nco_id?: string;
  modified_at?: string;
  payload?: unknown;
  source_system: OneVmsSourceSystemKey;
}

export interface UserOrderActionEventHandlerEvent extends InboundEventHandlerEvent {
  user_auth_context: KasAuthEndpointResponse;
  nco_id: string;
  modified_at: string;
}

export interface SystemOrderActionEventHandlerEvent extends InboundEventHandlerEvent {
  nco_id: string;
  modified_at: string;
}

// Default Types. Reuse for all events that do not have specific parameters.
// If they do, then create a new interface referencing these ones.
export interface DefaultApiRequest {
  /**
   * @minItems 1
   */
  nco_ids_with_modified_at: NcoIdWithModifiedAt[];
}

export enum DefaultEventHandlerResult {
  SUCCESS = 'success',
  ERROR = 'error',
}

//UPDATE NCO CORE INTERFACES
export type UpdateNcoCorePayload = Partial<
  Pick<NewCarOrderModel, 'order_type' | 'shipping_code' | 'receiving_port_code'>
>;

export interface UpdateNcoCoreApiRequest extends DefaultApiRequest {
  payload: UpdateNcoCorePayload;
}

export interface InboundEventDispatcherEventUpdateNcoCore extends InboundEventDispatcherEvent {
  payload: UpdateNcoCorePayload;
  user_auth_context: KasAuthEndpointResponse;
  /**
   * @minItems 1
   */
  ncos_info: NcoInfo[];
}

export enum UpdateNcoCoreHandlerResult {
  SUCCESS = 'success',
}

export interface InboundEventHandlerEventUpdateNcoCore extends UserOrderActionEventHandlerEvent {
  payload: UpdateNcoCorePayload;
}

//UPDATE NCO INTERFACES
export interface UpdateNcoFromPiPayload {
  purchase_intention_id: string;
  pi_modified_at: string;
}

export type UpdateNcoPayload = CoraNCOBaseApiRequest &
  Pick<NewCarOrderModel, 'modified_at' | 'modified_by'> & {
    // Optional PI fields for consolidated handler support
    purchase_intention_id?: string;
    pi_modified_at?: string;
  };

export interface CoraNcoUpdateApiRequest {
  /**
   * @minItems 1
   */
  nco_ids_with_modified_at: NcoIdWithModifiedAt[];
  payload: UpdateNcoPayload;
}

export enum UpdateNcoHandlerResult {
  SUCCESS = 'success',
}

export interface InboundEventHandlerEventUpdateNco extends UserOrderActionEventHandlerEvent {
  payload: UpdateNcoPayload;
}

export interface InboundEventDispatcherEventUpdateNco extends InboundEventDispatcherEvent {
  payload: UpdateNcoPayload;
  user_auth_context: KasAuthEndpointResponse;
  /**
   * @minItems 1
   */
  ncos_info: NcoInfo[];
}
// RPORT/REVOKE TOTAL LOSS
export interface CoraNCOTotalLossApiRequest {
  /**
   * @minItems 1
   */
  nco_ids_with_modified_at: NcoIdWithModifiedAt[];
}

//keep consistent with config in cora md
export enum TotalLossHandlerResult {
  SUCCESS = 'success',
}

//CANCEL NCO INTERFACES
export interface CancelNcoApiRequest extends DefaultApiRequest {
  cancellation_reason: string;
}

export interface InboundEventHandlerEventCancelNco extends UserOrderActionEventHandlerEvent {
  payload: {
    cancellation_reason: string;
  };
}

export enum CancelNcoHandlerResult {
  SUCCESS = 'success',
}

//DEALLOCATE QUOTA INTERFACES
export interface InboundEventHandlerEventDeallocateQuota extends UserOrderActionEventHandlerEvent {
  payload: unknown;
}

export interface InboundEventHandlerEventPVMSUpdateNco extends SystemOrderActionEventHandlerEvent {
  payload: PvmsOrderDataKafkaObject & { mapInvoice: boolean };
}

export interface InboundEventHandlerEventP06UpdateNco extends SystemOrderActionEventHandlerEvent {
  payload: P06NewCarOrderKafkaObject;
}

//CONVERT PI INTERFACES
export type ConvertPiApiRequest = CoraNCOBaseApiRequest & Required<Pick<NewCarOrderModel, 'deal_id' | 'modified_at'>>;

export type ConvertPiPayload = ConvertPiApiRequest & { purchase_intention_id: string; cookie_header?: string };

export interface InboundEventHandlerEventConvertPi extends InboundEventHandlerEvent {
  payload: ConvertPiPayload;
  user_auth_context: KasAuthEndpointResponse;
}

//UPDATE NCO FROM PI INTERFACES
export interface UpdateNcoFromPiPayload extends UpdateNcoPayload {
  pi_modified_at: string;
  purchase_intention_id: string; // Required for the specific PI API
}
export interface UpdateNcoFromPiApiRequest {
  /**
   * @minItems 1
   */
  nco_ids_with_modified_at: NcoIdWithModifiedAt[];
  payload: UpdateNcoFromPiPayload;
}

export interface InboundEventHandlerEventUpdateNcoFromPi extends UserOrderActionEventHandlerEvent {
  payload: UpdateNcoFromPiPayload & { purchase_intention_id: string };
}

export interface InboundEventDispatcherEventUpdateNcoFromPi extends InboundEventDispatcherEvent {
  payload: UpdateNcoFromPiPayload;
  user_auth_context: KasAuthEndpointResponse;
  /**
   * @minItems 1
   */
  ncos_info: NcoInfo[];
}

//CREATE NCO INTERFACES
export type CreateNcoPayload = Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id'>;

export interface CreateNcoApiRequest {
  payload: CreateNcoPayload;
}

export interface InboundEventHandlerEventCreateNco extends InboundEventHandlerEvent {
  payload: CreateNcoPayload;
  user_auth_context: KasAuthEndpointResponse;
}

//Sqs Lambda Response types
export interface BatchItemFailureWithError {
  itemIdentifier: string;
  errorMessage?: string;
}

export interface SQSBatchResponseWithError extends Omit<SQSBatchResponse, 'batchItemFailures'> {
  batchItemFailures: BatchItemFailureWithError[];
}

//Notification topic types
export interface NotificationKafkaEvent {
  transaction_id: string; // uuid/correlationId
  sub_transaction_id: string; // uuid for event
  transaction_obj_amount?: number;
  event_type: OneVmsEventKey;
  nco_id?: string; // nco id, if exists
  action_by: string;
  action_at: string; // iso timestamp
  source_system: OneVmsSourceSystemKey | string; // Cora/PVMS/P06 etc.
  details?: unknown; // any valid Json e.g. how many should be copied / Error message / Addtional ObjIds (e.g. importer transfer created obj id) etc.
  status: NotificationStatus;
}

export enum NotificationStatus {
  ACCEPTED = 'accepted',
  DISPATCHED = 'dispatched',
  EVENT_HANDLER_IO = 'event_handler_io',
  EVENT_HANDLER_NIO = 'event_handler_nio',
  EXPORTED = 'exported',
  ERROR = 'error',
}

// Types for generic API Gateway handler
export interface CorePayload {
  payload: unknown;
  sub_transaction_id?: string;
  ncos_info?: NcoInfo[];
  event_type: OneVmsEventKey;
}
