// Main DTO interface
export interface PvmsOrderDataDTO {
  ids: IDs;
  model_info: ModelInfo;
  order_info: OrderInfo;
  logistics_info: LogisticsInfo;
  production_info?: ProductionInfo;
  appointment_date_info?: AppointmentDateInfo;
  vehicle_technical_info?: VehicleTechnicalInfo;
  ordered_config_options?: OrderedConfigOption[];
}

export interface PvmsOrderDataDTOTransaction extends PvmsOrderDataDTO {
  transaction_id: string;
}

// First-level nested interfaces

export interface IDs {
  new_car_order_id: string;
  vguid_pvms_DEPRECATED: string;
  internal_vehicle_number_pvms_DEPRECATED?: string;
  commission_id?: string;
  production_number_porsche?: string;
  production_number_vw?: string;
  vehicle_identification_number?: string;
  vbto_id?: string;
  kate_id?: string;
  lead_id?: string;
  business_partner_id: string;
  tequipment_id?: string;
  order_reservation_id?: string;
  porsche_code?: string;
}

export interface ModelInfo {
  model_year_code?: string;
  model_year: number;
  model_type: string;
  material_sap_DEPRECATED?: string;
  country_code: string;
}

export interface OrderInfo {
  base_info: {
    fiscal_year?: number;
    quota_month: string | null;
    order_type: string;
    is_ordered_by_dealer?: boolean;
    is_locator_visible?: boolean;
    locator_transfer_status?: string;
  };
  planning?: {
    blocking_reason_planning?: string;
  };
  trading_partner: {
    importer_code: string;
    importer_number: string;
    dealer_sold_to_number: string;
    dealer_ship_to_number: string;
  };
  status_info: {
    vehicle_status_pvms_code: string;
    vehicle_status_pvms_timestamp: string;
    order_status_pvms_code: string;
    order_status_pvms_timestamp: string;
  };
  sales_info: {
    sales_person_id: string;
    sales_person_last_name?: string;
    sales_person_first_name?: string;
    cancellation_reason?: string;
    customer_cancellation_reason?: string;
  };
  delivery_info?: {
    mileage?: number;
    mileage_unit?: string;
    homologation_number?: string;
    license_plate_number?: string;
    has_follow_up_warranty?: boolean;
    has_roadside_assistance?: boolean;
  };
}

export interface LogisticsInfo {
  shipping_code: string;
  shipping_block?: string;
  incoterms?: string;
  current_location_code?: string;
  next_location_code?: string;
  receiving_port_code?: string;
  vessel_imo_number?: string;
  vessel_name?: string;
  bill_of_lading_number?: string;
}

export interface ProductionInfo {
  production_plant?: string;
}

export interface AppointmentDateInfo {
  production_logistic_dates?: {
    order_creation_date?: string;
    change_freeze_point_date?: string;
    expected_cp_80_date?: string;
    expected_dealer_delivery_date?: string;
    requested_dealer_delivery_date?: string;
    actual_dealer_delivery_date?: string;
  };
  customer_order_dates?: {
    customer_order_intake_cancellation_date?: string;
  };
  process_specific?: {
    factory_pickup_status?: string;
  };
}

export interface VehicleTechnicalInfo {
  general?: {
    combustion_engine_number?: string;
    engine_order_type?: string;
    tire_code?: string;
    key_code?: string;
    transmission_number?: string;
    transmission_order_type?: string;
    weight_front_axle?: string;
    weight_rear_axle?: string;
    emission_standard?: string;
  };
}

export interface OrderedConfigOption {
  option_code?: string;
  option_id?: string;
  option_type_short?: string;
  option_type_long?: string;
  reference_package?: string;
  package_option_type?: string;
  option_text?: string;
}

export interface PvmsOrderDataKafkaObject {
  key: string;
  value: PvmsOrderDataDTO;
  timestamp: number;
  numOfTries: number;
}

// Do we care about this? Einfach unknown?
export interface VehicleConfigurationPvmsNext {
  __metadata: {
    id: string; //"https://sapk17-lb.porsche.org:7211/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mknpc7jo9tpy3K0%7D2Tm')",
    uri: string; //"https://sapk17-lb.porsche.org:7211/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mknpc7jo9tpy3K0%7D2Tm')",
    type: string; //"YISA8034_CONFIG_SRV.Vehicle"
  };
  XTraceId: string; //""
  Id: string; //""
  SapUserId: string; //""
  Cnr: string; //"C02"
  Importernr: string; //"4500000"
  Dealernr: string; //"4500155"
  Importerid: string; //"USM"
  PpnUserId: string; //""
  Modelyear: string; //"P"
  Modeltype: string; //"982360"
  ConfigModified: string; //""
  CommentsModified: string; //""
  ConfigName: string; //"system_copy"
  Currency: string; //"$"
  IsDealerConfig: string; //""
  LeadId: string; //""
  BpId: string; //""
  ProspectKit: boolean;
  Taxes: string; //"0.00"
  TeqId: string; //""
  CofferId: string; //""
  TotalPriceGross: string; //"98035.00"
  PorscheCode: string; //"PPNUQ373"
  ExteriorColor: string; //"1A"
  TopColor: string; //"2V"
  InteriorColor: string; //"BM"
  Vguid: string; //"051Mknpc7jo9tpy3K0}2Tm"
  VehicleStatus: string; //"V260"
  LastUpdateDate: string; //"20221231000434"
  CurrentMarketDate: string; //"20240320035512"
  FreeZOffer: string; //""
  PriceDate: string; //"20230301120000"
  ProductionDate: string; //"0000-00-00"
  Vin: string; //"WP0CD2A89PS216395"
  CommissionNumber: string; //"F40032"
  VehicleOption: Record<string, unknown>;
  VehicleWLTPHeader: Record<string, unknown>;
  VehicleWLTPBody: Record<string, unknown>;
  VehicleTag: Record<string, unknown>;
}
