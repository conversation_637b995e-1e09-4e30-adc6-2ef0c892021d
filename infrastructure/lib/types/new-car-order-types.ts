import { NewCarOrderModel, NewCarOrderModelWithQuota } from '../entities/new-car-order-model';
import { FilterModel, SortModelItem } from './ag-grid-ssrm-types';

export interface CoraNCOConfigOrderedOptions {
  option_id: string;
  option_type?: string;
  option_validity?: {
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    valid_until?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    valid_from?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    serial_to?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    serial_from?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    offer_period_start?: string;
    /**
     * Date Format but allows empty string
     * @pattern ^(\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])|)$
     */
    offer_period_end?: string;
    /**
     * @description Matrial lead time, in number of days
     * @pattern ^(\d+|)$
     */
    material_lead_time?: string; //(Anzahl in Tagen)
    /**
     * @description When an option was added to the order
     * @format date-time
     */

    added_to_order_timestamp?: string;
  };
  referenced_package?: string;
  referenced_package_type?: string;
  referenced_package_sort_order?: number;
  package_content_sort_order?: number;
  option_subtype?: string;
  option_subtype_value?: string;
  content?: CoraNCOConfigOrderedOptions[];
}

export interface CoraNCOConfiguration {
  ordered_options: CoraNCOConfigOrderedOptions[];
  technical_options?: unknown;
}

export interface KccNCOConfigReponseKafkaValue {
  kas: {
    model_info: {
      model_type: string;
      model_year: number;
      country_code: string;
    };
    configuration: CoraNCOConfiguration;
  };
}

export interface KccNCOConfigWindowMessagePayload extends KccNCOConfigReponseKafkaValue {
  pvms: PvmsNCOConfigurationExpire;
}

export interface KccNCOConfigReponseKafkaObject {
  key: string;
  value: KccNCOConfigReponseKafkaValue;
  timestamp: number;
}

export type PvmsNCOConfigurationExpire = unknown;

export type NewCarOrderModelWithoutCoraConfig = Omit<NewCarOrderModel, 'configuration'>;

export type CoraNCOBaseApiResponse = Omit<NewCarOrderModel, 'configuration'> & {
  configuration: CoraNCOConfiguration;
} & Required<Pick<NewCarOrderModel, 'created_by' | 'created_at' | 'modified_by' | 'modified_at'>>;

export type CoraNCOGetApiResponse = CoraNCOBaseApiResponse & {
  dealer_name?: string;
};

export type CoraNCOGetApiResponseNoConfig = Omit<CoraNCOGetApiResponse, 'configuration' | 'configuration_expire'>;

export type CoraNCOQuotaConsumeObj = Pick<
  NewCarOrderModelWithQuota,
  'pk_new_car_order_id' | 'model_year' | 'model_type' | 'importer_number' | 'dealer_number' | 'quota_month'
>;

export type CoraNCOBaseApiRequest = Omit<
  NewCarOrderModel,
  | 'changed_by_system'
  | 'cancellation_reason'
  | 'order_status_onevms_timestamp_last_change'
  | 'order_status_onevms_error_code'
  | 'order_status_onevms_code'
  | 'order_invoice_onevms_code'
  | 'modified_by'
  | 'modified_at'
  | 'created_by'
  | 'created_at'
  | 'configuration'
  | 'quota_month'
> & {
  configuration_signature: string;
  configuration_expire_signature?: string;
  dealer_name?: string;
  configuration: CoraNCOConfiguration;
  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  quota_month: string;
};

// Api Request Objects
export type CoraNCOEditApiRequest = CoraNCOBaseApiRequest & Pick<NewCarOrderModel, 'modified_at' | 'modified_by'>;

export type CoraNCOCreateApiRequest = Omit<CoraNCOBaseApiRequest, 'pk_new_car_order_id' | 'business_partner_id'>;

export interface NCOActionFilters {
  changeable?: boolean;
}

export type FilterModelRequest = Partial<Record<keyof CoraNCOBaseApiResponse, FilterModel>>;
/** Based on ag-grid IServerSideGetRowsRequest */
export interface CoraNCOQueryApiRequestBody {
  /** First row requested or undefined for all rows. */
  startRow: number | undefined;
  /** Index after the last row required row or undefined for all rows. */
  endRow: number | undefined;
  actionFilters?: NCOActionFilters;
  filterModel?: FilterModelRequest;
  sortModel?: SortModelItem[];
}

export interface CoraNCOQueryApiResponse {
  data: Omit<CoraNCOGetApiResponse, 'configuration' | 'configuration_expire'>[];
  rowCount: number;
}

export interface CoraNCOQueryApiRequest extends Partial<CoraNCOBaseApiResponse> {
  /**
   * @description Changeable flag to simplify api requests
   */
  order_changeable?: string;
  /**
   * @description Limit for response page size, number as string (queryparam)
   * @pattern ^\d+$
   */
  limit?: string;
  /**
   * @description Offset for response page size, number as string (queryparam)
   * @pattern ^\d+$
   */
  offset?: string;
  colId?: string;
  sort?: string;
}

export interface CoraNCOIdsQueryApiRequest {
  /**
   * @description dealer number
   * @pattern ^\d+$
   */
  dealer_number: string;
}

export interface CoraNCOCancelApiRequest {
  /**
   * @minProperties 1
   */
  ncoids: Record<string, string>;
  cancellation_reason: string;
}

export interface CoraNCODeallocateApiRequest {
  /**
   * @minProperties 1
   */
  ncoids: Record<string, string>;
}

export interface CoraNCOImporterTransferApiRequest {
  /**
   * @minProperties 1
   */
  new_car_order_ids: Record<string, string>;
  to_importer_number: string;
  to_dealer_number: string;
  new_nco_attributes: Partial<Pick<NewCarOrderModel, 'order_type' | 'shipping_code' | 'receiving_port_code'>>;
}

export type CoraNCOImporterTransferApiResponse = CoraNCOImporterTransferApiResponseItem[];
export interface CoraNCOImporterTransferApiResponseItem {
  old_new_car_order_id: string;
  new_new_car_order_id?: string;
  isSuccess: boolean;
  error_msg?: string;
}
//
export interface CoraNCOBuySellTransferApiRequest {
  /**
   * @minItems 1
   */
  new_car_order_ids: string[];
  source_dealer_number: string;
  target_dealer_number: string;
}

export type CoraNCOOBuySellTransferApiResponse = CoraNCOOBuySellTransferApiResponseItem;
export interface CoraNCOOBuySellTransferApiResponseItem {
  new_car_order_ids: string[];
}

export interface CoraNCOIdsApiRequest {
  importer_number?: string;
  dealer_number?: string;
}

export type CoraNCOIdsApiResponse = CoraNCOIdsApiResponseItem;
export interface CoraNCOIdsApiResponseItem {
  new_car_order_ids: string[];
}

export interface CoraNCOHandleDealerInventoryActionApiRequest {
  /**
   * @minItems 1
   */
  new_car_order_ids: string[];
}

export type CoraNCOCopyApiRequest = Record<string, number>;

export type CoraNCOCopyApiResponse = CoraNCOCopyApiResponseObj[];

export interface CoraNCOCopyApiResponseObj {
  quota_month: string;
  quota_consumed: number;
  quota_failed: number;
  new_car_order_ids: string[];
}

export const unchangeableKeysNewCarOrderModel = [
  'pk_new_car_order_id',
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'importer_code',
  'created_at',
  'created_by',
  'order_status_onevms_code',
  'order_status_onevms_error_code',
  'order_status_onevms_timestamp_last_change',
] as const;
export type UnchangeableKeysNewCarOrderModel = Pick<
  NewCarOrderModel,
  (typeof unchangeableKeysNewCarOrderModel)[number]
>;

export const unchangeableKeysCoraNCPurchaseIntentionToNCO = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'importer_code',
  'pk_new_car_order_id',
] as const;
export type UnchangeableKeysCoraNCPurchaseIntentionToNCO = Pick<
  NewCarOrderModel,
  (typeof unchangeableKeysCoraNCPurchaseIntentionToNCO)[number]
>;

export const frontendRequiredQueryParamsNCOCreate = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'cnr',
  'importer_code',
  'quota_month',
] as const;
export type FrontendNCOCreateQueryParams = Pick<
  NewCarOrderModel,
  (typeof frontendRequiredQueryParamsNCOCreate)[number]
>;

export enum SOURCE_OBJ_TYPES {
  NEW_CAR_ORDER = 'nco',
  PURCHASE_INTENTION = 'pi',
}
export interface CoraNCOUpdateCoreDataApiRequest {
  /**
   * @minProperties 1
   */
  new_car_order_ids: Record<string, string>;
  new_nco_attributes: Partial<Pick<NewCarOrderModel, 'order_type' | 'shipping_code' | 'receiving_port_code'>>;
}

export interface CoraNCOBuySellTransferApiRequest {
  source_dealer_number: string;
  target_dealer_number: string;
  /**
   * @minItems 1
   */
  new_car_order_ids: string[];
}
