export interface OrderTypeText {
  languageCode: string;
  languageCountryIsoCode: string;
  orderType: string;
  text: string;
}

export interface OrderTypeValue {
  orderType?: string; //is missing in some messages
  orderTypeTexts: OrderTypeText[];
}

export interface KasData {
  importerNr?: string; //not all messages have this field, eg ggid1 does not for some reason
  cNr?: string; //not all messages have this field, eg ggid1 does not for some reason
}

export interface KafkaModelTypeText {
  ggId: number;
  my4: number;
  orderType: string;
  /**
   * @minItems 1
   */
  values: OrderTypeValue[];
  /**
   * @minItems 1
   */
  kasData: KasData[];
}

export interface KeyValueModelTypeText {
  key: string;
  value: KafkaModelTypeText;
  timestamp: number;
  actionType: string;
}
