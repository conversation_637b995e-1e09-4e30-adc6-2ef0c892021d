import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseModel } from './base-model';
import { CoraNCOConfiguration } from '../types/new-car-order-types';

@Entity({ name: `cora_purchase_intention` })
export class CoraPurchaseIntentionModel extends BaseModel {
  @PrimaryColumn({ type: 'text', update: false })
  public purchase_intention_id: string;

  @Column({ type: 'text' })
  public order_type: string;

  @Column({ type: 'text', nullable: true })
  public shipping_code: string | null;

  @Column({ type: 'text' })
  public model_type: string;

  @Column({ type: 'character varying', length: 4 })
  public model_year: string;

  /**
   * @pattern ^\d\d\d\d-\d\d$
   */
  @Column({ type: 'character varying', length: 7 })
  public quota_month: string;

  @Column({ type: 'text' })
  public dealer_number: string;

  @Column({ type: 'text' })
  public importer_number: string;

  @Column({ type: 'text' })
  public seller: string;

  @Column({ type: 'text' })
  public business_partner_id: string;

  /**
   * @minLength 2
   */
  @Column({ type: 'text' })
  public importer_code: string;

  @Column({ type: 'text', nullable: true })
  public receiving_port_code?: string | null;

  @Column({ type: 'text' })
  public vehicle_status_code: string;

  @Column({ type: 'date', nullable: true })
  public requested_dealer_delivery_date?: string | null;

  @Column({ type: 'jsonb', nullable: true })
  public vehicle_configuration_pvmsnext: unknown;

  @Column({ type: 'jsonb', nullable: true })
  public vehicle_configuration_onevms: CoraNCOConfiguration | null; //jsonb for now, might need it relational later

  /**
   * @pattern ^C\d\d$
   */
  @Column({ type: 'text' })
  public cnr: string;

  @Column({ type: 'bool', nullable: false, default: false })
  public is_converted?: boolean;

  @Column({ type: 'bool', nullable: false, default: false })
  public has_change_request?: boolean;
}
