import { Entity, Column, Unique, PrimaryGeneratedColumn, Index } from 'typeorm';
import { NewCarOrderModel } from './new-car-order-model';
import { NcoExportActionType } from '../../lambda/backend/export-nco/types';

@Entity({ name: 'new_car_order_audit_trail' })
@Unique(['pk_new_car_order_id', 'action_at'])
export class NewCarOrderAuditTrailModel {
  @PrimaryGeneratedColumn()
  public pk_audit_trail_id: number;

  //not as foreign key to avoid issues with nco delete or whatever
  @Index()
  @Column({ type: 'text' })
  public pk_new_car_order_id: string;

  @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP(6)' })
  public action_at: string;

  @Column({ type: 'text' })
  public action_by: string;

  @Column({ type: 'text' })
  public action_type: NcoExportActionType;

  @Column({ type: 'text' })
  public action_correlation_id: string;

  @Column({ type: 'bool' })
  public action_exported: boolean;

  @Column({
    type: 'jsonb',
    nullable: true,
    transformer: {
      to(value: NewCarOrderModel): string {
        return JSON.stringify(value);
      },
      from(value: string): NewCarOrderModel {
        return JSON.parse(value) as NewCarOrderModel;
      },
    },
  })
  public old_nco?: NewCarOrderModel | null;

  @Column({
    type: 'jsonb',
    nullable: true,
    transformer: {
      to(value: NewCarOrderModel): string {
        return JSON.stringify(value);
      },
      from(value: string): NewCarOrderModel {
        return JSON.parse(value) as NewCarOrderModel;
      },
    },
  })
  public new_nco?: NewCarOrderModel | null;
}
