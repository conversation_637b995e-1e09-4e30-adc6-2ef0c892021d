import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseModel } from './base-model';

@Entity({ name: 'model_type_visibility' })
export class ModelTypeVisibilityModel extends BaseModel {
  @PrimaryColumn({ type: 'text', update: false })
  public role: string;

  @PrimaryColumn({ type: 'text', update: false })
  public importer_number: string;

  @PrimaryColumn({ type: 'text', update: false })
  public my4: string;

  @PrimaryColumn({ type: 'text', update: false })
  public model_type: string;

  @PrimaryColumn({ type: 'text', update: false })
  public cnr: string;

  @Column({ type: 'text', nullable: false })
  public valid_from: string;
}
