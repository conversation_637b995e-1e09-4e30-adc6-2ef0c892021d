import { Column, Entity, PrimaryColumn } from 'typeorm';
import { BaseModel } from './base-model';

@Entity({ name: 'model_type_text' })
export class ModelTypeTextModel extends BaseModel {
  @PrimaryColumn({ type: 'text', update: false })
  public iso_language_code: string;

  @PrimaryColumn({ type: 'int', update: false })
  public ggid: number;

  @PrimaryColumn({ type: 'int', update: false })
  public model_year: number;

  @PrimaryColumn({ type: 'text', update: false })
  public model_type: string;

  @Column({ type: 'text' })
  public model_text: string;

  @Column({ type: 'text' })
  public language_code: string;

  @Column({ type: 'text', nullable: true })
  public importer_number: string | null;

  @Column({ type: 'text', nullable: true })
  public cnr: string | null;
}
