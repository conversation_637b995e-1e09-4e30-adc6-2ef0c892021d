import { Construct } from 'constructs';

import { RestApi, RequestAuthorizer, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { RemovalPolicy, aws_iam, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';

import { Constants } from '../utils/constants';
import { KasDynamodbTable, KasKmsKey, KasNodejsFunction, KasStage } from '@kas-resources/constructs';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';

interface QuotaApiEndpointConstructProps {
  api: RestApi;
  authorizer: RequestAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  applicationNameToAuthorize: string;
  coraGlobalDynamoKmsKey: KasKms<PERSON>ey;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  auroraRdsSecret: ISecret;
  typeormLayer: ILayerVersion;
}

export class QuotaEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: QuotaApiEndpointConstructProps) {
    super(scope, id);

    const quotaTableDbName = Constants.buildResourceName(props.stage, Constants.DYNAMODB_QUOTA_TABLE_NAME);

    const quotaTable = KasDynamodbTable.fromTableAttributes(this, 'QuotaTable', {
      tableName: quotaTableDbName,
      encryptionKey: props.coraGlobalDynamoKmsKey,
    });

    const orgRelTableDbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName,
    );
    const orgRelTable = KasDynamodbTable.fromTableAttributes(this, 'OrgRelTable', {
      tableName: orgRelTableDbName,
      encryptionKey: props.coraGlobalDynamoKmsKey,
    });

    const apiResource = props.api.root.addResource('quota');

    // Lambda for fetching all quotas
    const fetchRelevantQuotas = new KasNodejsFunction(this, 'FetchRelevantQuotas', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/quota/fetch-quota/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-quotas`,
      environment: {
        QUOTA_TABLE_NAME: quotaTable.tableName,
        TABLE_NAME_ORG_RELS: orgRelTable.tableName,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        CORS_DOMAIN: props.corsDomain,
        MOCK_QUOTA_RESPONSE: props.stage === 'dev' ? 'true' : 'false',
        STAGE: props.stage,
        AURORA_SECRET_ARN: props.auroraRdsSecret.secretArn,
      },
      description: 'Retrieve a list of all relevant quotas from the DynamoDB table.',
      errHandlingLambda: props.logSubscriptionLambda,
      customManagedKey: props.logGroupKey,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup, props.auroraAccessSecurityGroup],
      layers: [props.typeormLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      stage: props.stage,
    });

    quotaTable.grantReadData(fetchRelevantQuotas);
    orgRelTable.grantReadData(fetchRelevantQuotas);

    props.auroraRdsSecret.grantRead(fetchRelevantQuotas);

    //allow access to gsi to query parent_ppn_id column
    fetchRelevantQuotas.addToRolePolicy(
      new aws_iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: aws_iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${orgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`],
      }),
    );

    const quotaIntegration = new LambdaIntegration(fetchRelevantQuotas);

    apiResource.addMethod('GET', quotaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.querystring.importer_number': true,
        'method.request.querystring.dealer_number': true,
        'method.request.querystring.model_type': true,
        'method.request.querystring.model_year': true,
      },
    });
  }
}
