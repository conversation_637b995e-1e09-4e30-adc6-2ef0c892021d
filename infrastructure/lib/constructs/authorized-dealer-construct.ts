import { Construct } from 'constructs';
import { LambdaIntegration, IAuthorizer, IResource } from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { aws_iam as iam, aws_ec2 as ec2, RemovalPolicy } from 'aws-cdk-lib';
import { KasNodejsFunction, KasDynamodbTable, KasKmsKey, KasStage } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';

export interface AuthorizedDealerConstructProps {
  authorizer: IAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  coraOrgRelTable: KasDynamodbTable;
  impTable: KasDynamodbTable;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  vpc: ec2.IVpc;
}

export class AuthorizedDealerConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: AuthorizedDealerConstructProps) {
    super(scope, id);

    const authorizedDealerResource = props.parentResource.addResource('authorized-dealer');

    // GET /authorized-dealer
    const getAuthorizedDealers = new KasNodejsFunction(this, 'GetAuthorizedDealers', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/authorized-dealer/fetch-authorized-dealers/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        TABLE_NAME_IMPORTER: props.impTable.tableName,
        CORS_DOMAIN: props.corsDomain,
      },
      description: 'Get all dealers from org tree that the user is authorized for',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-authorized-dealers`,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    props.coraOrgRelTable.grantReadData(getAuthorizedDealers);
    props.impTable.grantReadData(getAuthorizedDealers);

    //allow access to gsi to query parent_ppn_id column
    getAuthorizedDealers.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [
          `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
        ],
      }),
    );

    const getAuthorizedDealersLambdaIntegration = new LambdaIntegration(getAuthorizedDealers);
    authorizedDealerResource.addMethod('GET', getAuthorizedDealersLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
