import { Construct } from 'constructs';

import { RestApi, RequestAuthorizer } from 'aws-cdk-lib/aws-apigateway';
import { Constants } from '../utils/constants';
import { KasDynamodbTable, KasKmsKey, KasStage } from '@kas-resources/constructs';
import { PurchaseIntentionListEndpointConstruct } from './purchase-intention-list-endpoint-construct';
import { OrderListEndpointConstruct } from './order-list-endpoint-construct';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { aws_ec2 as ec2 } from 'aws-cdk-lib';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';

interface ListsEndpointsConstructProps {
  api: RestApi;
  authorizer: RequestAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  coraOrgRelTable: KasDynamodbTable;
  coraGlobalDynamoKmsKey: KasKmsKey;
  coraMdGlobalDynamoKmsKey: KasKmsKey;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraReaderSecret: ISecret;
  typeormLayer: ILayerVersion;
}

export class ListsEndpointsConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: ListsEndpointsConstructProps) {
    super(scope, id);

    const listsResource = props.api.root.addResource('lists');

    //order lists table
    const newCarOrdersForListsTableName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_CORA_NEW_CAR_ORDER_LISTS_TABLE_PARAMS.tableName,
    );
    const newCarOrdersForListsTable = KasDynamodbTable.fromTableAttributes(this, 'newCarOrdersForListsTable', {
      tableName: newCarOrdersForListsTableName,
      encryptionKey: props.coraGlobalDynamoKmsKey,
    });

    //purchase intention lists table
    const purchaseIntentionsForListsTableName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_PVMS_PURCHASE_INTENTIONS_LISTS_TABLE_PARAMS.tableName,
    );
    const purchaseIntentionsForListsTable = KasDynamodbTable.fromTableAttributes(
      this,
      'purchaseIntentionsForListsTable',
      {
        tableName: purchaseIntentionsForListsTableName,
        encryptionKey: props.coraGlobalDynamoKmsKey,
      },
    );

    //md status mapping table
    //import global cora-md dynamodb kms key

    const statusMappingTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_STATUS_MAPPING_TABLE_NAME}`;
    const oneVmsStatusTable = KasDynamodbTable.fromTableAttributes(this, 'OneVmsStatusTable', {
      tableName: statusMappingTableDdbName,
      encryptionKey: props.coraMdGlobalDynamoKmsKey,
    });

    new OrderListEndpointConstruct(this, 'OrderListEndpoint', {
      api: props.api,
      authorizer: props.authorizer,
      stage: props.stage,
      logGroupKey: props.logGroupKey,
      newCarOrderListsTable: newCarOrdersForListsTable,
      globalDynamoKmsKey: props.coraGlobalDynamoKmsKey,
      corsDomain: props.corsDomain,
      coraOrgRelTable: props.coraOrgRelTable,
      parentResource: listsResource,
      logSubscriptionLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      auroraAccessSecurityGroup: props.auroraAccessSecurityGroup,
      coraMdStatusMappingTable: oneVmsStatusTable,
      auroraRdsSecret: props.auroraReaderSecret,
      typeormLayer: props.typeormLayer,
    });

    new PurchaseIntentionListEndpointConstruct(this, 'PurchaseIntentionListEndpoint', {
      api: props.api,
      authorizer: props.authorizer,
      stage: props.stage,
      logGroupKey: props.logGroupKey,
      globalDynamoKmsKey: props.coraGlobalDynamoKmsKey,
      purchaseIntentionListsTable: purchaseIntentionsForListsTable,
      corsDomain: props.corsDomain,
      coraOrgRelTable: props.coraOrgRelTable,
      parentResource: listsResource,
      logSubscriptionLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      vpcEndpointsSecurityGroup: props.vpcEndpointsSecurityGroup,
      auroraAccessSecurityGroup: props.auroraAccessSecurityGroup,
      auroraRdsSecret: props.auroraReaderSecret,
      typeormLayer: props.typeormLayer,
    });
  }
}
