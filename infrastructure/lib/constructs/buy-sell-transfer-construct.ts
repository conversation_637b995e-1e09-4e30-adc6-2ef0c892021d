import { Construct } from 'constructs';
import { RestApi, LambdaIntegration, IAuthorizer } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { RemovalPolicy, aws_iam, Duration, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { KasNodejsFunction, KasKmsKey, KasSqsQueue } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface BuySellTransferEndpointConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: string;
  logGroupKey: <PERSON>s<PERSON><PERSON><PERSON><PERSON>;
  coraOrgRelTable: ITable;
  corsDomain: string;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  auroraRdsSecret: ISecret;
  typeormLayer: ILayerVersion;
  globalNcoExportQueue: KasSqsQueue;
}

export class BuySellTransferEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: BuySellTransferEndpointConstructProps) {
    super(scope, id);

    const specialActionsResource = props.api.root.addResource('special-actions');
    const ncoResource = specialActionsResource.addResource('buy-sell-transfer');

    // POST /buy-sell-transfer
    const buySellTransfer = new KasNodejsFunction(this, 'BuySellTransfer', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/buy-sell-transfer/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        STAGE: props.stage,
        AURORA_SECRET_ARN: props.auroraRdsSecret.secretArn,
        EXPORT_NCO_SQS_URL: props.globalNcoExportQueue.queueUrl,
      },
      description: 'Transfer orders from a dealer to another dealer',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-buy-sell-transfer`,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      timeout: Duration.minutes(3),
      memorySize: 512,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      layers: [props.typeormLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
    });

    props.coraOrgRelTable.grantReadData(buySellTransfer);
    buySellTransfer.addToRolePolicy(
      new aws_iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: aws_iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${props.coraOrgRelTable.tableArn}/index/${Constants.CORA_ORG_PERMISSON_CHECK_PARAMS.pPIDIndex}`],
      }),
    );

    props.globalNcoExportQueue.grantSendMessages(buySellTransfer);
    props.auroraRdsSecret.grantRead(buySellTransfer);

    const buySellTransferLambdaIntegration = new LambdaIntegration(buySellTransfer);
    ncoResource.addMethod('POST', buySellTransferLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
