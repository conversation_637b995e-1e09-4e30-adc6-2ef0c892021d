import {
  Kas<PERSON><PERSON><PERSON>dbT<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  KasNodejsFun<PERSON>,
  Kas<PERSON><PERSON><PERSON>,
  KasSqsQueue,
  KasStage,
} from '@kas-resources/constructs';
import { Duration, RemovalPolicy, aws_iam, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { IAuthorizer, LambdaIntegration, RestApi } from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager/lib';
import { Construct } from 'constructs';
import { OneVmsEventKey } from '../types/process-steering-types';
import { CoraFeatureFlags } from '../types_cdk/cdk-types';
import { Constants, EXISTING_PERMISSIONS } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import {
  ProcessSteeringEventApiConstruct,
  ProcessSteeringEventApiConstructProps,
} from './process-steering-event-api-construct';

export interface NewCarOrderEndpointConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  quotaApiSecret: KasSecret;
  coraOrgRelTable: KasDynamodbTable;
  scTable: KasDynamodbTable;
  otTable: KasDynamodbTable;
  dlrTable: KasDynamodbTable;
  impTable: KasDynamodbTable;
  quotaTable: KasDynamodbTable;
  statusMappingTable: KasDynamodbTable;
  applicationNameToAuthorize: string;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  externalAccessSecurityGroup: ec2.ISecurityGroup;
  kafkaSecurityGroup: ec2.ISecurityGroup;
  auroraReaderSecret: ISecret;
  auroraWriterSecret: ISecret;
  typeormLayer: ILayerVersion;
  globalNcoExportQueue: KasSqsQueue;
  eventDispatcherInboundQueue: KasSqsQueue;
  featureFlags: CoraFeatureFlags;
  kafkaParameters: {
    brokers: string[];
    ncoNotificationTopic: string;
    kafkaSecret: ISecret;
  };
}

export class NewCarOrderEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: NewCarOrderEndpointConstructProps) {
    super(scope, id);

    // DEFINITION OF EVENT API LAMBDAS, AUTOGENERATED FROM all eligibile OneVmsEventKeys.
    // IMPORTANT: PLEASE ADD YOUR EVENT KEY HERE IF IT SHOULD HAVE AN API LAMBDA
    const allowedEventKeys: OneVmsEventKey[] = [
      OneVmsEventKey.CANCEL,
      OneVmsEventKey.UPDATE_CORE_DATA,
      OneVmsEventKey.TOTAL_LOSS_REPORT,
      OneVmsEventKey.TOTAL_LOSS_REVOKE,
      OneVmsEventKey.DEALLOCATE_QUOTA,
      OneVmsEventKey.UPDATE_NCO,
      OneVmsEventKey.MOVE_TO_INVENTORY,
      OneVmsEventKey.REMOVE_FROM_INVENTORY,
      OneVmsEventKey.CREATE,
    ];
    //Add event keys that use ExternalAccessSecurityGroup
    //This is needed because the UPDATE NCO event api handler calls an external API to verify the KCC signature of the order
    const eventKeysWithExternalAccessSecurityGroup: OneVmsEventKey[] = [OneVmsEventKey.UPDATE_NCO];
    const commonEventApiHandlerProps = {
      applicationNameToAuthorize: props.applicationNameToAuthorize,
      vpc: props.vpc,
      stage: props.stage,
      dispatcherQueue: props.eventDispatcherInboundQueue,
      logGroupKey: props.logGroupKey,
      logSubscriptionLambda: props.logSubscriptionLambda,
      kafkaSecurityGroup: props.kafkaSecurityGroup,
      kafkaParameters: props.kafkaParameters,
      commonEnvVars: {
        CORS_DOMAIN: props.corsDomain,
      },
    };

    // Create event api handler resources for every event key in the array
    // Make sure api handler code exists at `lambda/backend/process-steering/event-api/${eventKey}/index.ts` before deploying
    const eventApiConstructs = Object.fromEntries(
      allowedEventKeys.map((eventKey) => {
        const eventApiHandlerProps: ProcessSteeringEventApiConstructProps = {
          eventKey,
          ...commonEventApiHandlerProps,
          ...(eventKeysWithExternalAccessSecurityGroup.includes(eventKey) && {
            additionalSecurityGroups: [props.externalAccessSecurityGroup],
          }),
        };
        return [
          eventKey,
          new ProcessSteeringEventApiConstruct(this, `${eventKey}EventApiConstruct`, eventApiHandlerProps),
        ];
      }),
    ) as Record<OneVmsEventKey, ProcessSteeringEventApiConstruct>;

    const ncoResource = props.api.root.addResource('new-car-order');
    const ncoIdPath = ncoResource.addResource('{ncoId}');

    // POST /new-car-order
    const createNewCarOrderLambdaIntegration = new LambdaIntegration(
      eventApiConstructs[OneVmsEventKey.CREATE].eventApiHandler,
    );

    ncoResource.addMethod('POST', createNewCarOrderLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.querystring.mock_quota_api': false,
      },
    });

    // POST /new-car-order/{id}/copy
    if (props.featureFlags.copyOrder) {
      const copyPath = ncoIdPath.addResource('copy');
      const copyNewCarOrder = new KasNodejsFunction(this, 'CopyNewCarOrder', {
        runtime: ConstantsCdk.NODE_JS_VERSION,
        entry: 'lambda/backend/new-car-order/copy-new-car-order/index.ts',
        handler: 'handler',
        environment: {
          QUOTA_API_SECRET_ARN: props.quotaApiSecret.secretArn,
          APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
          TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
          TABLE_NAME_SC: props.scTable.tableName,
          TABLE_NAME_OT: props.otTable.tableName,
          TABLE_NAME_IMP: props.impTable.tableName,
          TABLE_NAME_DLR: props.dlrTable.tableName,
          TABLE_NAME_QUOTA: props.quotaTable.tableName,
          CORS_DOMAIN: props.corsDomain,
          STATUS_MAPPING_TABLE_NAME: props.statusMappingTable.tableName,
          AURORA_SECRET_ARN: props.auroraWriterSecret.secretArn,
          //for e2e and integration tests on dev only!
          ALLOW_QUOTA_API_MOCK: props.stage === 'dev' ? 'true' : 'false',
          EXPORT_NCO_SQS_URL: props.globalNcoExportQueue.queueUrl,
        },
        logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
        description: 'Copy a new car order',
        customManagedKey: props.logGroupKey,
        functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-copy-new-car-order`,
        memorySize: 512, // Example runs used ~236MB, bit too close to 256
        timeout: Duration.minutes(2), // Cold start of quota api takes up to 30 seconds. Therefore 1 minute is not enough.
        errHandlingLambda: props.logSubscriptionLambda,
        vpc: props.vpc,
        vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
        securityGroups: [props.auroraAccessSecurityGroup, props.externalAccessSecurityGroup],
        bundling: {
          externalModules: LambdaTypeOrmBundlingExternalModules,
        },
        layers: [props.typeormLayer],
        stage: props.stage,
      });

      props.quotaApiSecret.grantRead(copyNewCarOrder);
      props.auroraWriterSecret.grantRead(copyNewCarOrder);
      props.statusMappingTable.grantReadData(copyNewCarOrder);
      props.scTable.grantReadData(copyNewCarOrder);
      props.otTable.grantReadData(copyNewCarOrder);
      props.dlrTable.grantReadData(copyNewCarOrder);
      props.impTable.grantReadData(copyNewCarOrder);
      props.quotaTable.grantReadData(copyNewCarOrder);
      props.coraOrgRelTable.grantReadData(copyNewCarOrder);
      copyNewCarOrder.addToRolePolicy(
        new iam.PolicyStatement({
          sid: 'AllowAccessToIndex',
          effect: iam.Effect.ALLOW,
          actions: ['dynamodb:Query'],
          resources: [
            `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
          ],
        }),
      );
      props.globalNcoExportQueue.grantSendMessages(copyNewCarOrder);

      const copyNewCarOrderLambdaIntegration = new LambdaIntegration(copyNewCarOrder);
      copyPath.addMethod('POST', copyNewCarOrderLambdaIntegration, {
        authorizer: props.authorizer,
        requestParameters: {
          'method.request.path.ncoId': true,
          'method.request.querystring.mock_quota_api': false,
        },
      });
    }

    // GET /get-car-order/{id}
    const getNewCarOrder = new KasNodejsFunction(this, 'GetNewCarOrder', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/new-car-order/get-new-car-order/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_COR: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        STATUS_MAPPING_TABLE_NAME: props.statusMappingTable.tableName,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Get a new car order',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-get-new-car-order`,
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });

    props.auroraReaderSecret.grantRead(getNewCarOrder);
    props.coraOrgRelTable.grantReadData(getNewCarOrder);
    props.statusMappingTable.grantReadData(getNewCarOrder);

    getNewCarOrder.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [
          `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
        ],
      }),
    );

    const getNewCarOrderLambdaIntegration = new LambdaIntegration(getNewCarOrder);
    ncoIdPath.addMethod('GET', getNewCarOrderLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.ncoId': true,
      },
    });

    // PATCH /new-car-order/{id}
    const updateNewCarOrderLambdaIntegration = new LambdaIntegration(
      eventApiConstructs[OneVmsEventKey.UPDATE_NCO].eventApiHandler,
    );
    ncoIdPath.addMethod('PATCH', updateNewCarOrderLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.ncoId': true,
        'method.request.querystring.mock_quota_api': false,
      },
    });

    // PATCH /new-car-order/update-core-data
    if (props.featureFlags.updateNcoCoreData) {
      const updateCoreDataPath = ncoResource.addResource('update-core-data');
      const updateNewCarOrderCoreDataLambdaIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.UPDATE_CORE_DATA].eventApiHandler,
      );
      updateCoreDataPath.addMethod('PATCH', updateNewCarOrderCoreDataLambdaIntegration, {
        authorizer: props.authorizer,
      });
    }

    // PATCH /new-car-order/cancel
    const cancelPath = ncoResource.addResource('cancel');
    const cancelNewCarOrderApiLambdaIntegration = new LambdaIntegration(
      eventApiConstructs[OneVmsEventKey.CANCEL].eventApiHandler,
    );

    cancelPath.addMethod('PATCH', cancelNewCarOrderApiLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // PATCH /new-car-order/deallocate-quota
    if (props.featureFlags.deallocateQuota) {
      const deallocatePath = ncoResource.addResource('deallocate-quota');
      const deallocateNcoQuotasLambdaIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.DEALLOCATE_QUOTA].eventApiHandler,
      );
      deallocatePath.addMethod('PATCH', deallocateNcoQuotasLambdaIntegration, {
        authorizer: props.authorizer,
      });
    }

    // POST /new-car-order/total-loss/revoke and /new-car-order/total-loss/report
    if (props.featureFlags.reportRevokeTotalLoss) {
      const totalLossPath = ncoResource.addResource('total-loss');
      const revokeTotalLossPath = totalLossPath.addResource('revoke');
      const reportTotalLossPath = totalLossPath.addResource('report');

      const totalLossReportLambdaIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.TOTAL_LOSS_REPORT].eventApiHandler,
      );
      reportTotalLossPath.addMethod('POST', totalLossReportLambdaIntegration, {
        authorizer: props.authorizer,
      });
      const totalLossRevokeLambdaIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.TOTAL_LOSS_REVOKE].eventApiHandler,
      );
      revokeTotalLossPath.addMethod('POST', totalLossRevokeLambdaIntegration, {
        authorizer: props.authorizer,
      });
    }

    // POST /new-car-order/importer-transfer
    if (props.featureFlags.importerTransfer) {
      const importerTransferPath = ncoResource.addResource('importer-transfer');
      const importerTransferHandler = new KasNodejsFunction(this, 'importerTransferHandler', {
        runtime: ConstantsCdk.NODE_JS_VERSION,
        entry: 'lambda/backend/new-car-order/importer-transfer/index.ts',
        handler: 'handler',
        environment: {
          TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
          CORS_DOMAIN: props.corsDomain,
          AURORA_SECRET_ARN: props.auroraWriterSecret.secretArn,
          QUOTA_API_SECRET_ARN: props.quotaApiSecret.secretArn,
          TABLE_NAME_IMP: props.impTable.tableName,
          TABLE_NAME_SC: props.scTable.tableName,
          TABLE_NAME_OT: props.otTable.tableName,
          TABLE_NAME_DLR: props.dlrTable.tableName,
          ALLOW_QUOTA_API_MOCK: props.stage === 'dev' ? 'true' : 'false',
          EXPORT_NCO_SQS_URL: props.globalNcoExportQueue.queueUrl,
        },
        logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
        description: 'Importer Transfer API GW handler',
        customManagedKey: props.logGroupKey,
        functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-nco-importer-transfer`,
        timeout: Duration.seconds(30),
        errHandlingLambda: props.logSubscriptionLambda,
        vpc: props.vpc,
        vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
        securityGroups: [props.auroraAccessSecurityGroup, props.externalAccessSecurityGroup],
        bundling: {
          externalModules: LambdaTypeOrmBundlingExternalModules,
        },
        layers: [props.typeormLayer],
        stage: props.stage,
      });

      props.auroraWriterSecret.grantRead(importerTransferHandler);
      props.quotaApiSecret.grantRead(importerTransferHandler);
      props.statusMappingTable.grantReadData(importerTransferHandler);
      props.coraOrgRelTable.grantReadData(importerTransferHandler);
      props.impTable.grantReadData(importerTransferHandler);
      props.otTable.grantReadData(importerTransferHandler);
      props.scTable.grantReadData(importerTransferHandler);
      props.dlrTable.grantReadData(importerTransferHandler);
      importerTransferHandler.addToRolePolicy(
        new iam.PolicyStatement({
          sid: 'AllowAccessToIndex',
          effect: iam.Effect.ALLOW,
          actions: ['dynamodb:Query'],
          resources: [
            `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
          ],
        }),
      );

      props.globalNcoExportQueue.grantSendMessages(importerTransferHandler);

      const importerTransferIntegration = new LambdaIntegration(importerTransferHandler);

      //revoke
      importerTransferPath.addMethod('POST', importerTransferIntegration, {
        authorizer: props.authorizer,
      });
    }

    // PATCH /new-car-order/move-to-dealer-inventory and /new-car-order/remove-from-dealer-inventory
    if (props.featureFlags.handleDealerInventory) {
      const moveToDealerInventoryActionIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.MOVE_TO_INVENTORY].eventApiHandler,
      );
      const moveToDealerInventoryPath = ncoResource.addResource(EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY);
      moveToDealerInventoryPath.addMethod('PATCH', moveToDealerInventoryActionIntegration, {
        authorizer: props.authorizer,
      });
      const removeFromDealerInventoryActionIntegration = new LambdaIntegration(
        eventApiConstructs[OneVmsEventKey.REMOVE_FROM_INVENTORY].eventApiHandler,
      );
      const removeFromDealerInventoryPath = ncoResource.addResource(
        EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY,
      );
      removeFromDealerInventoryPath.addMethod('PATCH', removeFromDealerInventoryActionIntegration, {
        authorizer: props.authorizer,
      });
    }

    const fetchNewCarOrdersPath = ncoResource.addResource('new-car-order-ids');
    const fetchNewCarOrderIds = new KasNodejsFunction(this, 'FetchNewCarOrderIdList', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/fetch-order-ids/index.ts',
      handler: 'handler',
      environment: {
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        STAGE: props.stage,
        AURORA_SECRET_ARN: props.auroraWriterSecret.secretArn,
      },
      description: 'Fetch all new car order ids of a dealer',
      customManagedKey: props.logGroupKey,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-order-ids`,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      timeout: Duration.minutes(3),
      memorySize: 512,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      layers: [props.typeormLayer],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
    });

    props.coraOrgRelTable.grantReadData(fetchNewCarOrderIds);
    fetchNewCarOrderIds.addToRolePolicy(
      new aws_iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: aws_iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${props.coraOrgRelTable.tableArn}/index/${Constants.CORA_ORG_PERMISSON_CHECK_PARAMS.pPIDIndex}`],
      }),
    );

    props.auroraWriterSecret.grantRead(fetchNewCarOrderIds);
    const fetchNewCarOrdersLambdaIntegration = new LambdaIntegration(fetchNewCarOrderIds);
    fetchNewCarOrdersPath.addMethod('GET', fetchNewCarOrdersLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
