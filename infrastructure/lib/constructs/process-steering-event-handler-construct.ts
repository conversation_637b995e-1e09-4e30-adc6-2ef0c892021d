import { Construct } from 'constructs';

import {
  KasD<PERSON><PERSON>dbTable,
  KasKms<PERSON>ey,
  KasNodejsFunction,
  KasSec<PERSON>,
  KasSqsQueue,
  KasStage,
} from '@kas-resources/constructs';
import { Duration, aws_ec2 as ec2, aws_iam as iam, RemovalPolicy } from 'aws-cdk-lib';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { IQueue } from 'aws-cdk-lib/aws-sqs';
import { EventBridgeScheduleProps } from '../types_cdk/cdk-types';
import { Constants, OneVmsEventHandlerKey } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface ProcessSteeringEventHandlerConstructProps {
  eventDispatcherInboundQueue: IQueue;
  quotaApiSecret?: KasSecret;
  eventHandlerKey: OneVmsEventHandlerKey;
  globalEventHandlerInboundDlq: KasSqsQueue;
  handlerTimeout?: Duration;
  handlerMemory?: number;
  handlerBatchSize?: number;
  inboundConcurrency?: number;
  kafkaSecret: KasSecret;
  kafkaBrokers: string[];
  notificationTopic: string;
  typeORMLayer: ILayerVersion;
  stage: KasStage;
  logGroupKey: KasKmsKey;
  orgRelTable: KasDynamodbTable;
  scTable: KasDynamodbTable;
  otTable: KasDynamodbTable;
  dlrTable: KasDynamodbTable;
  impTable: KasDynamodbTable;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  kafkaSecurityGroup: ec2.ISecurityGroup;
  additionalSecurityGroups?: ec2.ISecurityGroup[];
  auroraWriterSecret: ISecret;
  globalSqsKmsKey: KasKmsKey;
  extraEnvs?: Record<string, string>;
  applicationNameToAuthorize: string;
  eventBridgeScheduleProps?: EventBridgeScheduleProps;
}

export class ProcessSteeringEventHandlerConstruct extends Construct {
  public readonly eventHandlerLambda: KasNodejsFunction;
  public readonly eventHandlerInboundQueue: KasSqsQueue;
  public readonly eventHandlerKey: OneVmsEventHandlerKey;
  public eventBridgeSchedulerRole: iam.Role;
  public globalEventbridgeSchedulerKmsKey: KasKmsKey;

  public constructor(scope: Construct, id: string, props: ProcessSteeringEventHandlerConstructProps) {
    super(scope, id);
    this.eventHandlerKey = props.eventHandlerKey;

    //Event handler inbound sqs queue
    this.eventHandlerInboundQueue = new KasSqsQueue(this, 'EventHandlerInboundQueue-' + props.eventHandlerKey, {
      queueName: 'EventHandlerInboundQueue-' + props.eventHandlerKey,
      encryptionMasterKey: props.globalSqsKmsKey,
      deadLetterQueue: { queue: props.globalEventHandlerInboundDlq, maxReceiveCount: 1 },
      visibilityTimeout: props.handlerTimeout ?? Duration.seconds(60), //must be >= lambda timeout
    });

    //Event handler lambda, code path is calculated from eventHandlerKey
    this.eventHandlerLambda = new KasNodejsFunction(this, 'EventHandlerLambda-' + props.eventHandlerKey, {
      vpc: props.vpc,
      securityGroups: [
        props.kafkaSecurityGroup,
        props.auroraAccessSecurityGroup,
        ...(props.additionalSecurityGroups ? props.additionalSecurityGroups : []),
      ],
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-event-handler-${props.eventHandlerKey}`,
      description: `Event handler for ${props.eventHandlerKey}`,
      entry: `lambda/backend/process-steering/event-handler/${props.eventHandlerKey}/index.ts`,
      handler: 'index.handler',
      timeout: props.handlerTimeout ?? Duration.seconds(60),
      memorySize: props.handlerMemory ?? 256,
      runtime: ConstantsCdk.NODE_JS_VERSION,
      customManagedKey: props.logGroupKey,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      environment: {
        ...props.extraEnvs,
        KAFKA_SECRET_ARN: props.kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaBrokers),
        KAFKA_TOPIC_NOTIFICATION: props.notificationTopic,
        AURORA_SECRET_ARN: props.auroraWriterSecret.secretArn,
        TABLE_NAME_ORG_RELS: props.orgRelTable.tableName,
        TABLE_NAME_IMP: props.impTable.tableName,
        TABLE_NAME_SC: props.scTable.tableName,
        TABLE_NAME_OT: props.otTable.tableName,
        TABLE_NAME_DLR: props.dlrTable.tableName,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        DISPATCHER_QUEUE_ARN: props.eventDispatcherInboundQueue.queueArn,
        ...(props.eventBridgeScheduleProps && {
          EVENT_BRIDGE_SCHEDULER_ROLE_ARN: props.eventBridgeScheduleProps.eventBridgeSchedulerRole.roleArn,
          EVENT_BRIDGE_SCHEDULER_KMS_KEY_ARN: props.eventBridgeScheduleProps.globalEventbridgeSchedulerKmsKey.keyArn,
        }),
      },
      layers: [props.typeORMLayer],
      stage: props.stage,
    });

    //Add required access to tables, secrets, etc
    props.kafkaSecret.grantRead(this.eventHandlerLambda);
    props.auroraWriterSecret.grantRead(this.eventHandlerLambda);
    props.orgRelTable.grantReadData(this.eventHandlerLambda);
    props.scTable.grantReadData(this.eventHandlerLambda);
    props.otTable.grantReadData(this.eventHandlerLambda);
    props.dlrTable.grantReadData(this.eventHandlerLambda);
    props.impTable.grantReadData(this.eventHandlerLambda);

    this.eventHandlerLambda.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${props.orgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`],
      }),
    );
    this.eventHandlerInboundQueue.grantConsumeMessages(this.eventHandlerLambda);

    if (props.quotaApiSecret) {
      props.quotaApiSecret.grantRead(this.eventHandlerLambda);
    }

    //Event source to consume sqs messages from eventHandlerInboundQueue by eventHandlerLambda
    const eventHandlerEventSource = new SqsEventSource(this.eventHandlerInboundQueue, {
      enabled: true,
      maxConcurrency: props.inboundConcurrency,
      batchSize: props.handlerBatchSize ?? 1, //TODO what should be the default here?
      reportBatchItemFailures: true,
    });
    this.eventHandlerLambda.addEventSource(eventHandlerEventSource);

    if (props.eventBridgeScheduleProps) {
      this.addSchedulerPolicies(this.eventHandlerLambda, props.eventBridgeScheduleProps);
    }
  }

  private addSchedulerPolicies(lambdaFn: KasNodejsFunction, scheduleProps: EventBridgeScheduleProps): void {
    lambdaFn.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowScheduleCreation',
        effect: iam.Effect.ALLOW,
        actions: ['scheduler:CreateSchedule', 'scheduler:UpdateSchedule', 'scheduler:ListSchedules'],
        resources: ['*'],
      }),
    );
    lambdaFn.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowScheduleRoleAssign',
        effect: iam.Effect.ALLOW,
        actions: ['iam:PassRole'],
        resources: [scheduleProps.eventBridgeSchedulerRole.roleArn],
      }),
    );
    lambdaFn.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowKmsKeyEncryption',
        effect: iam.Effect.ALLOW,
        actions: ['kms:DescribeKey', 'kms:GenerateDataKey', 'kms:Decrypt'],
        resources: [scheduleProps.globalEventbridgeSchedulerKmsKey.keyArn],
      }),
    );
  }
}
