import { Construct } from 'constructs';
import { RestApi, IAuthorizer } from 'aws-cdk-lib/aws-apigateway';
import { KasKms<PERSON>ey, KasStage } from '@kas-resources/constructs';
import { FetchFailedCarOrdersEndpointConstruct } from './fetch-failed-orders-endpoint-construct';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { aws_ec2 as ec2 } from 'aws-cdk-lib';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
export interface MonitoringConstructProps {
  api: RestApi;
  authorizer: IAuthorizer;
  stage: KasStage;
  logGroupKey: KasKmsKey;
  corsDomain: string;
  logSubscriptionLambda: IFunction;
  vpc: ec2.IVpc;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraReaderSecret: ISecret;
  layers: ILayerVersion[];
}
export class MonitoringEndpointsConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: MonitoringConstructProps) {
    super(scope, id);

    const listsResource = props.api.root.addResource('monitoring');

    //failed orders
    new FetchFailedCarOrdersEndpointConstruct(this, 'FetchFailedCarOrders', {
      api: props.api,
      authorizer: props.authorizer,
      stage: props.stage,
      logGroupKey: props.logGroupKey,
      corsDomain: props.corsDomain,
      logSubscriptionLambda: props.logSubscriptionLambda,
      parentResource: listsResource,
      vpc: props.vpc,
      vpcEndpointsSecurityGroup: props.vpcEndpointsSecurityGroup,
      auroraReaderSecret: props.auroraReaderSecret,
      auroraAccessSecurityGroup: props.auroraAccessSecurityGroup,
      layers: props.layers,
    });
  }
}
