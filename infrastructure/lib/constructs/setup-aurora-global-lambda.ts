import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';
import { Duration, RemovalPolicy } from 'aws-cdk-lib';
import { Constants } from '../utils/constants';
import { Kas<PERSON><PERSON>Key, KasNodejsFunction, KasSecret, KasStage } from '@kas-resources/constructs';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

export interface SetupAuroraGlobalLambdaProps {
  auroraAdminSecret: KasSecret;
  coraAuroraReaderSecret: KasSecret;
  coraAuroraWriterSecret: KasSecret;
  coraMdAuroraReaderSecret: KasSecret;
  coraMdAuroraWriterSecret: KasSecret;
  customManagedKey: Kas<PERSON><PERSON><PERSON><PERSON>;
  logSubscriptionLambda: IFunction;
  securityGroup: ec2.ISecurityGroup;
  stage: KasStage;
  typeormLayer: ILayerVersion;
  functionName: string;
  vpc: ec2.IVpc;
}

export class SetupAuroraGlobalLambda extends KasNodejsFunction {
  public constructor(scope: Construct, id: string, props: SetupAuroraGlobalLambdaProps) {
    super(scope, id, {
      description:
        'Setup Global AuroraDB with defined tables and users. This function will use the admin secret to perform all operations.',
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.securityGroup],
      runtime: ConstantsCdk.NODE_JS_VERSION,
      handler: 'handler',
      functionName: props.functionName,
      entry: 'lambda/backend/setup-aurora-db/index.ts',
      customManagedKey: props.customManagedKey,
      bundling: {
        externalModules: ['pg-native'].concat(LambdaTypeOrmBundlingExternalModules),
      },
      environment: {
        AURORA_ADMIN_SECRET_ARN: props.auroraAdminSecret.secretArn,
        CORA_AURORA_READER_SECRET_ARN: props.coraAuroraReaderSecret.secretArn,
        CORA_AURORA_WRITER_SECRET_ARN: props.coraAuroraWriterSecret.secretArn,
        CORA_MD_AURORA_READER_SECRET_ARN: props.coraMdAuroraReaderSecret.secretArn,
        CORA_MD_AURORA_WRITER_SECRET_ARN: props.coraMdAuroraWriterSecret.secretArn,
        STAGE: props.stage,
      },
      layers: [props.typeormLayer],
      timeout: Duration.seconds(30),
      memorySize: 1024,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: props.logSubscriptionLambda,
      stage: props.stage,
    });

    props.auroraAdminSecret.grantRead(this);
    props.coraAuroraReaderSecret.grantRead(this);
    props.coraAuroraWriterSecret.grantRead(this);
    props.coraMdAuroraReaderSecret.grantRead(this);
    props.coraMdAuroraWriterSecret.grantRead(this);
  }
}
