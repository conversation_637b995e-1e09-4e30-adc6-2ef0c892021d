import { Construct } from 'constructs';
import { LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { aws_iam as iam, RemovalPolicy } from 'aws-cdk-lib';
import { Kas<PERSON><PERSON><PERSON><PERSON>, KasNodejsFunction } from '@kas-resources/constructs';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';
import { BackendEndpointProps } from '../types_cdk/cdk-types';
import { GlobalStack } from '../stacks/global-stack';
import { replaceCorsPreflightOnApiResource } from '../utils/cdk_utils';

export interface NotificationCenterEndpointConstructProps extends BackendEndpointProps {
  coraGlobalDynamoKmsKey: KasKmsKey;
  paddockCorsDomain: string;
}

export class NotificationCenterEndpointConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: NotificationCenterEndpointConstructProps) {
    super(scope, id);

    const allowedOrigins = [props.corsDomain, props.paddockCorsDomain];

    const notificationCenterResource = props.parentResource.addResource('notification-center');
    replaceCorsPreflightOnApiResource(notificationCenterResource, allowedOrigins);

    const transactionResource = notificationCenterResource.addResource('transaction');
    replaceCorsPreflightOnApiResource(transactionResource, allowedOrigins);

    const transactionIdResource = transactionResource.addResource('{transactionId}');
    replaceCorsPreflightOnApiResource(transactionIdResource, allowedOrigins);

    const transactionTable = GlobalStack.getDynamoDbTable(
      scope,
      Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.tableName,
      props.stage,
      props.coraGlobalDynamoKmsKey,
    );
    const subTransactionTable = GlobalStack.getDynamoDbTable(
      scope,
      Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName,
      props.stage,
      props.coraGlobalDynamoKmsKey,
    );

    // GET /notification-center/transaction
    const getAllTransactionsLambda = new KasNodejsFunction(this, 'GetAllTransactions', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/notification-center/rest-api/get-all.ts',
      handler: 'handler',
      environment: {
        TRANSACTION_TABLE_NAME: transactionTable.tableName,
        TRANSACTION_TABLE_INDEX_NAME: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index,
        TRANSACTION_TABLE_INDEX_PK_NAME: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index_pk,
        TRANSACTION_TABLE_INDEX_SK_NAME: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index_sk,
        SUB_TRANSACTION_TABLE_NAME: subTransactionTable.tableName,
        SUB_TRANSACTION_TABLE_PK_NAME: Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.pk,
        CORS_DOMAIN: props.corsDomain,
        PADDOCK_CORS_DOMAIN: props.paddockCorsDomain,
      },
      description: 'Get all notification-center transactions a user is authorized for',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-nc-get-all-transactions`,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    transactionTable.grantReadData(getAllTransactionsLambda);
    subTransactionTable.grantReadData(getAllTransactionsLambda);
    // allow access to gsi to query action_by column
    getAllTransactionsLambda.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [`${transactionTable.tableArn}/index/${Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.action_index}`],
      }),
    );

    const getAllTransactionsLambdaIntegration = new LambdaIntegration(getAllTransactionsLambda);
    transactionResource.addMethod('GET', getAllTransactionsLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // GET /notification-center/transaction/{transactionId}
    const getSingleTransactionLambda = new KasNodejsFunction(this, 'GetSingleTransaction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/notification-center/rest-api/get-by-id.ts',
      handler: 'handler',
      environment: {
        TRANSACTION_TABLE_NAME: transactionTable.tableName,
        TRANSACTION_TABLE_PK_NAME: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.pk,
        SUB_TRANSACTION_TABLE_NAME: subTransactionTable.tableName,
        SUB_TRANSACTION_TABLE_PK_NAME: Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.pk,
        CORS_DOMAIN: props.corsDomain,
        PADDOCK_CORS_DOMAIN: props.paddockCorsDomain,
      },
      description: 'Gets a single transaction with subTransactions by transactionId',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-nc-get-single-transaction`,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    transactionTable.grantReadData(getSingleTransactionLambda);
    subTransactionTable.grantReadData(getSingleTransactionLambda);

    const getSingleTransactionsLambdaIntegration = new LambdaIntegration(getSingleTransactionLambda);
    transactionIdResource.addMethod('GET', getSingleTransactionsLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.transactionId': true,
      },
    });

    // PATCH /notification-center/transaction/{transactionId}
    const patchTransactionLambda = new KasNodejsFunction(this, 'PatchTransaction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/notification-center/rest-api/patch.ts',
      handler: 'handler',
      environment: {
        TRANSACTION_TABLE_NAME: transactionTable.tableName,
        TRANSACTION_TABLE_PK_NAME: Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.pk,
        CORS_DOMAIN: props.corsDomain,
        PADDOCK_CORS_DOMAIN: props.paddockCorsDomain,
      },
      description: 'Updates a transaction (currently attribute "hidden" only) without subTransactions by transactionId',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-nc-patch-transaction`,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    transactionTable.grantReadWriteData(patchTransactionLambda);

    const patchTransactionsLambdaIntegration = new LambdaIntegration(patchTransactionLambda);
    transactionIdResource.addMethod('PATCH', patchTransactionsLambdaIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.transactionId': true,
      },
    });
  }
}
