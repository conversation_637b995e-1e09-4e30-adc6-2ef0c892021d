import { CoraStackProps } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { getGlobalParameterNames } from './global-parameter-names';

const stage = 'int';

export const intProps: CoraStackProps = {
  stage,
  hostedZoneName: 'cora-int.dpp.porsche.com',
  paddockDomainName: 'paddock-int.dpp.porsche.com',
  mail: Constants.MAIL,
  owner: Constants.OWNER,
  useCase: Constants.USE_CASE,
  env: {
    account: '************',
    region: Constants.DEFAULT_REGION,
  },
  idp: {
    domain: 'https://ppnlite.porsche.com',
    loginUrl: 'https://ppnlite.porsche.com/as/authorization.oauth2',
    tokenUrl: 'https://ppnlite.porsche.com/as/token.oauth2',
    clientId: 'b0609d7f-c32c-4cec-b62b-7ce509fb288c',
    publicKeyUrl: 'https://ppnlite.porsche.com/pf/JWKS',
    issuer: 'https://ppnlite.porsche.com',
  },
  applicationNameToAuthorize: Constants.APPLICATION_NAME_TO_AUTHORIZE,
  ppnRolesWrite: [
    'ppn_approle_pag_operations',
    //'ppn_approle_boss_dev_basic_application_role',
    //'ppn_approle_kas_importer_dev',
    //'ppn_approle_kas_dealer_dev',
  ],
  kasAuthEndpointUrl: 'https://kasauth.kas-int.dpp.porsche.com',
  globalParameterNames: getGlobalParameterNames(stage),
  kafkaParameters: {
    brokers: ['pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092'],
    newCarOrderTopicOneVms: 'FRA_one_vms_cora_new_car_order',
    newCarOrderTopicPvms: 'FRA_one_vms_cora_new_car_order_pvms',
    newCarOrderTopicP06: 'FRA_one_vms_cora_new_car_order_p06',
    newCarOrderTopicConfigRequest: 'FRA_one_vms_kcc_translate_request',
    newCarOrderTopicConfigResponse: 'FRA_one_vms_kcc_translate_response',
    bossOrgTopic: 'FRA_kas_hub_boss_kas_organizations',
    hubModelTypeVisibilityTopic: 'FRA_kas_hub_boss_model_type_visibilites',
    quotaTopic: 'FRA_one_vms_quota_dealer_quotas',
    pvmsOrderDataTopic: 'FRA_kas_hub_new_car_order_pvms',
    p06InboundTopic: 'FRA_kas_hub_new_car_order_pia',
    pccdModelTypeTextTopic: 'FRA_one_vms_pccd_inbound_model_type_text',
    ncoNotificationTopic: 'FRA_one_vms_cora_notifications_new_car_order',
  },
  alerting: {
    alertingEmails: ['<EMAIL>', Constants.MONITORING_EMAIL],
  },
  featureFlags: {
    copyOrder: true,
    updateNcoCoreData: true,
    reportRevokeTotalLoss: true,
    deallocateQuota: true,
    importerTransfer: true,
    handleDealerInventory: true,
    buySellTransfer: true,
    ncoInvoiceMapping: true,
  },
};
