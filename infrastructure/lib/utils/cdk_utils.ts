import { IResource, Method } from 'aws-cdk-lib/aws-apigateway';

export const replaceCorsPreflightOnApiResource = (resource: IResource, allowedOrigins: string[]): void => {
  // CDK isn't the brightest bulb on the cake... Need to remove the existing OPTIONS method, to not get duplicate OPTIONS method error
  const optionsMethod = resource.node.tryFindChild('OPTIONS') as Method | undefined;
  if (optionsMethod) {
    resource.node.tryRemoveChild('OPTIONS');
  }
  resource.addCorsPreflight({
    statusCode: 200,
    allowOrigins: allowedOrigins,
    allowCredentials: true,
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'x-kas-request-id', //For correlationId
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token',
    ],
  });
};
