import { KasStage } from '@kas-resources/constructs';
import { PermissionToEndpointMapping } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';

export class Constants {
  public static readonly APPLICATION_NAME: string = 'Car Order Application';
  public static readonly APPLICATION_SHORT_NAME: string = 'cora';
  public static readonly CORA_MD_APPLICATION_SHORT_NAME: string = 'cora-md';
  public static readonly DEFAULT_REGION: string = 'eu-west-1';
  public static readonly RESOURCE_ACCOUNT: string = '************';
  public static readonly BACKUP_REGION: string = 'eu-central-1';
  public static readonly BACKUP_TARGET_VAULT_NAME: string = 'DisasterRecoveryVault';
  public static readonly REPOSITORY_NAME: string = 'Cora';
  public static readonly OWNER: string = '<PERSON><PERSON>';
  public static readonly USE_CASE: string =
    'Car Order Application, Auftragsanlage and Lists for Order change, cancel and deal convertion';
  public static readonly MAIL: string = 'elke.mue<PERSON><PERSON><PERSON>-<EMAIL>';
  public static readonly APPLICATION_NAME_TO_AUTHORIZE: string = 'cora';
  public static readonly INTEGRATION_TEST_ASSUME_ROLE: string = 'CoraIntegrationTestAssumeRole';
  public static readonly E2E_TEST_ROLE: string = 'CoraE2ERole';
  public static readonly MONITORING_EMAIL = '<EMAIL>';
  public static readonly MONITORING_PVMS_ORDER_DATA_ALARM = 'CoraPvmsOrderDataImportFailed';
  public static readonly LOG_BASED_ALERTING_LOG_SUBSCRIPTION_LAMBDA_ARN_PARAM_NAME =
    'CoraLogBasedAlertingLogSubscriptionLambdaArn';
  //RDS ssm parameters
  public static readonly SSM_AURORA_ADMIN_SECRET_ARN_PARAM = 'Aurora/auroraAdminSecretArn';
  public static readonly SSM_AURORA_WRITER_SECRET_ARN_PARAM = 'Aurora/coraAuroraWriterSecretArn';
  public static readonly SSM_AURORA_READER_SECRET_ARN_PARAM = 'Aurora/coraAuroraReaderSecretArn';
  public static readonly SSM_MD_AURORA_WRITER_SECRET_ARN_PARAM = 'Aurora/coraMdAuroraWriterSecretArn';
  public static readonly SSM_MD_AURORA_READER_SECRET_ARN_PARAM = 'Aurora/coraMdAuroraReaderSecretArn';

  //RDS tables
  public static readonly AURORA_FAILED_STATUS_MAPPING_ORDERS_TABLE = 'failed_status_mapping_orders';

  //RDS Schemas
  public static readonly CORA_MD_AURORA_SCHEMA = 'masterdata';

  //update kafka trigger state with cron job, more info found by KASHEARTBE-1332
  public static readonly KCC_CONFIG_RESPONSE_CONSUMER = 'kcc-config-response-consumer';

  //DynamoDB Table constants
  public static readonly DYNAMODB_NEW_CAR_ORDER_TABLE_PARAMS = {
    tableName: 'new-car-order',
    pk: 'pk_new_car_order_id',
    deal_id_Index: 'deal_id_index',
    deal_id_index_pk: 'deal_id',
  };
  public static readonly DYNAMODB_NEW_CAR_ORDER_FOR_CONFIG_REQUESTS_TABLE_PARAMS = {
    tableName: 'new-car-order-for-config-requests',
    pk: 'pk_new_car_order_id',
  };
  public static readonly DYNAMODB_PVMS_PURCHASE_INTENTIONS_LISTS_TABLE_PARAMS = {
    tableName: 'pvms-purchase-intentions-for-lists',
    pk: 'dealer_number',
    sk: 'deal_id',
  };
  public static readonly DYNAMODB_NEW_CAR_ORDER_ID_TABLE_PARAMS = { tableName: 'new-car-order-id', pk: 'id' };
  public static readonly DYNAMODB_BOSS_ORG_TABLE_PARAMS = { tableName: 'boss-org', pk: 'pk_ppn_id' };
  public static readonly DYNAMODB_CORA_ORG_REL_TABLE_PARAMS = {
    tableName: 'cora-org-rel',
    pk: 'pk_ppn_id',
    sk: 'parent_ppn_id',
    dealerNumber: 'dealer_number',
    pPIDIndex: 'ParentPpnIdIndex',
  };
  public static readonly DYNAMODB_CORA_PURCHASE_INTENTIONS_TABLE_PARAMS = {
    tableName: 'pvms-purchase-intentions',
    pk: 'pvms_deal_id',
  };

  public static readonly DYNAMODB_FAILED_STATUS_MAPPING_TABLE_PARAMS = {
    tableName: 'failed-status-mappings',
    pk: 'object_id',
  };

  public static readonly DYNAMODB_NEW_CAR_ORDER_AUDIT_TABLE_PARAMS = {
    tableName: 'new-car-order-audit',
    pk: 'pk_new_car_order_id',
  };

  public static readonly DYNAMODB_TRANSACTION_TABLE_PARAMS = {
    tableName: 'notification-transaction',
    pk: 'transaction_id',
    action_index: 'action_index',
    action_index_pk: 'action_by',
    action_index_sk: 'action_at',
  };

  public static readonly DYNAMODB_SUBTRANSACTION_TABLE_PARAMS = {
    tableName: 'notification-subtransaction',
    pk: 'transaction_id',
    sk: 'uuid',
  };

  public static readonly DYNAMODB_MODEL_TYPE_VISIBILITY_TABLE_NAME = 'model-type-visibility';

  // Constants for the master data api
  public static readonly MASTER_DATA_APP_NAME = 'cora-md';
  public static readonly DYNAMODB_BOSS_DEALER_TABLE_NAME = 'boss-dealer';
  public static readonly DYNAMODB_BOSS_IMPORTER_TABLE_NAME = 'boss-importer';
  public static readonly DYNAMODB_CORA_PORT_CODE_TABLE_NAME = 'port-code';
  public static readonly DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME = 'order-type';
  public static readonly DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME = 'shipping-code';
  public static readonly DYNAMODB_CORA_STATUS_MAPPING_TABLE_NAME = 'status-mapping';
  public static readonly DYNAMODB_QUOTA_TABLE_NAME = 'quotas';

  //kms keys
  public static readonly KMS_KEY_GLOBAL_SECRET_NAME = 'global-secret';
  public static readonly KMS_KEY_GLOBAL_LOG_NAME = 'global-log';
  public static readonly KMS_KEY_GLOBAL_DYNAMO_NAME = 'global-dynamodb';
  public static readonly KMS_KEY_GLOBAL_HEALTH_MONITORING_NAME = 'global-health-monitoring';
  public static readonly KMS_KEY_GLOBAL_SQS = 'global-sqs-key';
  public static readonly KMS_KEY_GLOBAL_S3 = 'global-s3-key';
  public static readonly KMS_KEY_GLOBAL_SQS_NAME = 'global-sqs-kms-key';
  public static readonly KMS_KEY_AURORA_DATABASE_NAME = 'aurora-db';
  public static readonly KMS_KEY_GLOBAL_SESSION_MANAGER_NAME = 'global-session-manager';
  public static readonly KMS_KEY_GLOBAL_EVENTBRIDGE_SCHEDULER_NAME = 'global-eventbridge-scheduler';

  public static readonly KAFKA_SECRET_NAME = 'cora-streamzilla-kafka-secret';
  public static readonly KAFKA_SECRET_NAME_INT = 'cora-streamzilla-kafka-secret-for-int';

  public static readonly CANCELLATION_REASONS = [
    {
      id: 'A1',
      beschreibung: 'other Porsche model/ quota purchased',
    },
    {
      id: 'A2',
      beschreibung: 'car not available/ waiting time too long',
    },
    {
      id: 'A3',
      beschreibung: 'non-availability of option/ colour',
    },
    {
      id: 'A4',
      beschreibung: 'price increase',
    },
    {
      id: 'A5',
      beschreibung: 'quality issues',
    },
    {
      id: 'A6',
      beschreibung: 'duplicate/ MY change/ placed by error',
    },
    {
      id: 'A7',
      beschreibung: 'customer financial/ private situation',
    },
    {
      id: 'A8',
      beschreibung: 'more attractive competitor offer',
    },
    {
      id: 'A9',
      beschreibung: 'other',
    },
  ];

  public static readonly ARTIFACTORY_USER_CERT_SECRETS_MANAGER_ARN: string =
    'arn:aws:secretsmanager:eu-west-1:************:secret:cora/pipeline-secret-0wMzCJ';
  public static readonly ARTIFACTORY_USER_CERT_KEY_SECRETS_MANAGER_ARN: string =
    'arn:aws:secretsmanager:eu-west-1:************:secret:cora/pipeline-secret-0wMzCJ';
  public static readonly VPC_CIDR: string = '**********/16';
  public static readonly VPC_NAME: string = 'CoraGlobalVPC';
  public static readonly PUBLIC_SUBNET_GROUPNAME: string = 'public-subnet';
  public static readonly PRIVATE_SUBNET_GROUPNAME_AUTH: string = 'private-subnet-auth';
  public static readonly PRIVATE_SUBNET_GROUPNAME_SERVICES: string = 'private-subnet-services';
  public static readonly PRIVATE_SUBNET_GROUPNAME_MD_SERVICES: string = 'private-subnet-md-services';
  public static readonly PRIVATE_SUBNET_GROUPNAME_RESERVED: string = 'private-subnet-reserved';
  public static readonly ISOLATED_SUBNET_GROUPNAME: string = 'isolated-subnet';
  //Aurora PostgreSQL constants
  public static readonly AURORA_DB_PORT: number = 5432;
  public static readonly AURORA_DB_NAME: string = 'coraDB';
  public static readonly AURORA_CLUSTER_NAME: string = 'cora-aurora-serverless-cluster';
  public static readonly SSM_AURORA_CLUSTER_ARN_PARAM = 'Aurora/auroraClusterArn';
  public static readonly DYNAMODB_CORA_NEW_CAR_ORDER_LISTS_TABLE_PARAMS = {
    tableName: 'new-car-orders-for-lists',
    pk: 'dealer_number',
    sk: 'pk_new_car_order_id',
  };
  public static readonly DYNAMODB_NEW_CAR_ORDER_TABLE = {
    tableName: 'new-car-order',
  };

  public static readonly CORA_ORG_PERMISSON_CHECK_PARAMS = {
    pk: 'pk_ppn_id',
    sk: 'parent_ppn_id',
    dealerNumber: 'dealer_number',
    pPIDIndex: 'ParentPpnIdIndex',
  };

  //Purchase Intention pvms Stati
  public static readonly PVMS_PURCHASE_INTENTION_STATUS_FOR_LIST = ['V070'];
  public static readonly PVMS_PURCHASE_INTENTION_STATUS_ALL = [
    ...Constants.PVMS_PURCHASE_INTENTION_STATUS_FOR_LIST,
    'V050',
    'V060',
    'VX98',
  ];

  //Purchase Intention css Stati
  public static readonly CSS_PURCHASE_INTENTION_STATUS_NEW = 'V070';

  // NCO Config Option
  public static readonly CORA_NEW_CAR_ORDER_COFIG_OPTION_LOCAL = 'Local';

  //NCO Onevms Stati
  public static readonly CORA_NEW_CAR_ORDER_STATUS_CANCELLED = 'OX0000';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED = 'PP1300';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR = 'FBP500';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_NEW = 'PP0000';
  public static readonly CORA_NEW_CAR_ORDER_INVOICE_STATUS_NEW = 'PI0000';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_CONVERT = 'PP0000';
  public static readonly CORA_NEW_CAR_ORDER_INVOICE_STATUS_CONVERT = 'PI0000';
  public static readonly CORA_NEW_CAR_ORDER_INVOICE_STATUS_PAG_ORDER_INVOICED = 'PI4000';
  public static readonly CORA_NEW_CAR_ORDER_INVOICE_STATUS_MARKET_ORDER_INVOICED = 'MI9000';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS = 'OX1100';
  public static readonly CORA_NEW_CAR_ORDER_STATUS_PERSIST_TOTAL_LOSS = 'OX1000';

  public static buildKmsKeyId(stage: string, aliasName: string): string {
    return `${Constants.APPLICATION_SHORT_NAME}-${stage}-${aliasName}`;
  }

  public static buildCoraMDKmsKeyId(stage: string, aliasName: string): string {
    return `${Constants.CORA_MD_APPLICATION_SHORT_NAME}-${stage}-${aliasName}`;
  }

  public static buildResourceName(stage: string, resourceName: string): string {
    return `${Constants.APPLICATION_SHORT_NAME}-${stage}-${resourceName}`;
  }

  public static buildCancellationScheduleName(stage: string, ncoId: string): string {
    return `${this.buildCancellationSchedulePrefix(stage)}-${ncoId}`;
  }

  public static buildCancellationSchedulePrefix(stage: string): string {
    return `${Constants.APPLICATION_SHORT_NAME}-${stage}-nco-cancellation-schedule`;
  }

  public static buildTotalLossScheduleName(stage: string, ncoId: string): string {
    return `${this.buildTotalLossSchedulePrefix(stage)}-${ncoId}`;
  }

  public static buildTotalLossSchedulePrefix(stage: string): string {
    return `${Constants.APPLICATION_SHORT_NAME}-${stage}-nco-total-loss-schedule`;
  }
}

export const APPLICATION_NAMES = {
  CORA: 'cora',
} as const;
export const EXISTING_PERMISSIONS = {
  NCO_CREATE: 'create-new-car-order',
  NCO_COPY: 'copy-new-car-order',
  NCO_EDIT: 'update-new-car-order',
  NCO_MULTI_EDIT: 'multi-update-new-car-orders',
  NCO_CANCEL: 'cancel-new-car-orders',
  NCO_DEALLOCATE_QUOTA: 'deallocate-quota-new-car-orders',
  NCO_LIST: 'list-new-car-orders',
  NCO_TRANSFER_IMPORTER: 'nco-importer-transfer',
  NCO_REPORT_TOTAL_LOSS: 'report-total-loss',
  NCO_REVOKE_TOTAL_LOSS: 'revoke-total-loss',
  PI_LIST: 'list-deals',
  MON_AUDIT_TRAIL: 'audit-trail',
  MON_ORDER_STATUS: 'order-status-monitoring',
  NCO_MOVE_TO_DEALER_INVENTORY: 'move-to-dealer-inventory',
  NCO_REMOVE_FROM_DEALER_INVENTORY: 'remove-from-dealer-inventory',
  BUY_SELL_TRANSFER: 'buy-sell-transfer',
} as const;

export class PermissionEndpointMapping {
  public static readonly PERMISSIONS: PermissionToEndpointMapping[] = [
    {
      permissionName: EXISTING_PERMISSIONS.NCO_CREATE,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/*', //maps to "resource" in the event of the authorizer. Path would be /new-car-order/123
          method: 'GET',
        },
        {
          endpointRegex: '/purchase-intention/*',
          method: 'GET',
        },
        {
          endpointRegex: '/purchase-intention/*',
          method: 'PATCH',
        },
        {
          endpointRegex: '/boss/*',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/oc-dealer-importer',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/order-type',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/port-code',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/shipping-code',
          method: 'GET',
        },
        {
          endpointRegex: '/new-car-order',
          method: 'POST',
        },
        {
          endpointRegex: '/quota',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_COPY,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/*/copy',
          method: 'POST',
        },
        {
          endpointRegex: '/boss/*',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/oc-dealer-importer',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/order-type',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/port-code',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/shipping-code',
          method: 'GET',
        },
        {
          endpointRegex: '/quota',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_EDIT,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/*', //maps to "resource" in the event of the authorizer. Path would be /new-car-order/123
          method: 'GET',
        },
        {
          endpointRegex: '/new-car-order/*',
          method: 'PATCH',
        },
        {
          endpointRegex: '/boss/*',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/*',
          method: 'GET',
        },
        {
          endpointRegex: '/new-car-order',
          method: 'POST',
        },
        {
          endpointRegex: '/quota',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_MULTI_EDIT,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/*',
          method: 'GET',
        },
        {
          endpointRegex: '/new-car-order/*',
          method: 'PATCH',
        },
        {
          endpointRegex: '/new-car-order/*',
          method: 'POST',
        },
        {
          endpointRegex: '/boss/*',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/*',
          method: 'GET',
        },
        {
          endpointRegex: '/quota',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_CANCEL,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/cancel',
          method: 'PATCH',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_DEALLOCATE_QUOTA,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/deallocate-quota',
          method: 'PATCH',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_LIST,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/lists/new-car-order/get',
          // AgGrid uses POST for SSRM
          method: 'POST',
        },
        {
          endpointRegex: '/boss/model-type/text/*',
          method: 'GET',
        },
        {
          endpointRegex: '/masterdata/onevms-status',
          method: 'GET',
        },
        {
          endpointRegex: '/rbam-permissions',
          method: 'GET',
        },
        // TODO does this belong here, or maybe we need rights you always have somewhere?
        {
          endpointRegex: '/notification-center/transaction*',
          method: 'GET',
        },
        {
          endpointRegex: '/notification-center/transaction/*',
          method: 'PATCH',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.PI_LIST,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/lists/purchase-intention',
          method: 'GET',
        },
        {
          endpointRegex: '/lists/purchase-intention/get',
          // AgGrid uses POST for SSRM
          method: 'POST',
        },
        {
          endpointRegex: '/boss/model-type/text/*',
          method: 'GET',
        },
        {
          endpointRegex: '/rbam-permissions',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.MON_AUDIT_TRAIL,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/audit/*',
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.MON_ORDER_STATUS,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/monitoring/fetch-failed-order-data', //maps to "resource" in the event of the authorizer. Path would be /monitoring
          method: 'GET',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_REPORT_TOTAL_LOSS,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/total-loss/report',
          method: 'POST',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_REVOKE_TOTAL_LOSS,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/total-loss/revoke',
          method: 'POST',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_TRANSFER_IMPORTER,
      applicationName: APPLICATION_NAMES.CORA,
      endpoints: [
        {
          endpointRegex: '/new-car-order/importer-transfer',
          method: 'POST',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY,
      applicationName: 'cora',
      endpoints: [
        {
          endpointRegex: '/new-car-order/' + EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY,
          method: 'PATCH',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY,
      applicationName: 'cora',
      endpoints: [
        {
          endpointRegex: '/new-car-order/' + EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY,
          method: 'PATCH',
        },
      ],
    },
    {
      permissionName: EXISTING_PERMISSIONS.BUY_SELL_TRANSFER,
      applicationName: 'cora',
      endpoints: [
        {
          endpointRegex: '/special-actions/buy-sell-transfer',
          method: 'POST',
        },
      ],
    },
  ];
}

//grace period config for cr (customer related) and nocr (not customer related) order types
export interface GracePeriodConfig {
  months: number; // 30 Days
  days: number;
  hours: number;
  minutes: number;
  flexible_window_min?: number;
}

export const GRACE_PERIODS_QUOTA_DEALLOCATION_NON_CUSTOMER_RELATED: Record<KasStage, GracePeriodConfig> = {
  dev: {
    months: 0,
    days: 0,
    hours: 0,
    minutes: 5,
  },
  int: {
    months: 0,
    days: 0,
    hours: 1,
    minutes: 0,
  },
  prod: {
    months: 1,
    days: 0,
    hours: 0,
    minutes: 0,
    flexible_window_min: 60,
  },
};

export const GRACE_PERIODS_QUOTA_DEALLOCATION_CUSTOMER_RELATED: Record<KasStage, GracePeriodConfig> = {
  dev: {
    months: 0,
    days: 0,
    hours: 0,
    minutes: 10,
  },
  int: {
    months: 0,
    days: 0,
    hours: 2,
    minutes: 0,
  },
  prod: {
    months: 6,
    days: 0,
    hours: 0,
    minutes: 0,
    flexible_window_min: 60,
  },
};

export const GRACE_PERIODS_TOTAL_LOSS: Record<KasStage, GracePeriodConfig> = {
  dev: {
    months: 0,
    days: 0,
    hours: 0,
    minutes: 1,
  },
  int: {
    months: 0,
    days: 0,
    hours: 2,
    minutes: 0,
  },
  prod: {
    months: 0,
    days: 3,
    hours: 0,
    minutes: 0,
    flexible_window_min: 60,
  },
};

/*
 * Keep values consistent with coraMD constants
 * To add an event handler, simply add it to the enum and make sure the lambda code exists at
 * `lambda/backend/process-steering/event-handler/eventHandlerEnumValue/index.ts`
 * eg `lambda/backend/process-steering/event-handler/update-nco-core/index.ts`
 *
 * Important: Only add a new event handler to the enum if it's implemented, this is important for the inbound validation.
 */
export enum OneVmsEventHandlerKey {
  DEALLOCATE_QUOTA = 'deallocate-quota',
  UPDATE_NCO_CORE = 'update-nco-core',
  UPDATE_NCO = 'update-nco',
  UPDATE_NCO_FROM_PI = 'update-nco-from-pi',
  TOTAL_LOSS_REPORT = 'total-loss-report',
  TOTAL_LOSS_PERSIST = 'total-loss-persist',
  TOTAL_LOSS_REVOKE = 'total-loss-revoke',
  CANCEL_NCO = 'cancel-nco',
  CANCEL_NCO_SCHEDULED = 'cancel-nco-scheduled',
  PVMS_UPDATE_NCO = 'pvms-update-nco',
  P06_UPDATE_NCO = 'p06-update-nco',
  MOVE_TO_INVENTORY = 'move-nco-to-inventory',
  REMOVE_FROM_INVENTORY = 'remove-nco-from-inventory',
  CONVERT_PI = 'convert-pi',
  CREATE_NCO = 'create-nco',
}
