import fs from 'fs';
import path from 'path';
import readline from 'readline';
import ora from 'ora';
import { createGenerator, Config, Schema } from 'ts-json-schema-generator';

/* eslint @typescript-eslint/no-unsafe-member-access: 0 */
/* eslint @typescript-eslint/no-explicit-any: 0 */
/* eslint @typescript-eslint/no-unsafe-argument: 0 */
/* eslint @typescript-eslint/no-unsafe-assignment: 0 */

// Path to Data Contract directory
const DATA_CONTRACT_DIR = path.resolve(__dirname, '../../../data/datacontract');

// Default contract metadata
const DEFAULT_OWNER = 'Heinz Alexy';
const DEFAULT_CONTACT = {
  name: 'Team Heartbeat',
  url: 'https://skyway.porsche.com/confluence/display/ARTKAST/Team+HEARTBEAT',
  email: '<EMAIL>',
  department: 'PSD',
};
const DEFAULT_VERSION = '1.0.0';

// CLI input helper
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const askQuestion = async (query: string, defaultValue?: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(`${query} ${defaultValue ? `(default: ${defaultValue})` : ''}: `, (answer) => {
      resolve((answer || defaultValue) ?? '');
    });
  });
};

// Version upgrader
const upgradeVersion = (version: string): string => {
  const parts = version.split('.').map(Number);
  if (parts.length === 3) {
    if (parts[2] < 9) {
      parts[2] += 1;
    } else {
      parts[1] += 1;
      parts[2] = 0;
    }
    return parts.join('.');
  }
  return DEFAULT_VERSION;
};

// Store descriptions from the existing schema
const descriptionCache: Record<string, string> = {};

// Recursively extract existing descriptions
const cacheDescriptions = (schema: Schema, objectPath: string = ''): void => {
  if (typeof schema !== 'object') return;

  if ('description' in schema && typeof schema.description === 'string') {
    descriptionCache[objectPath] = schema.description;
  }

  if ('properties' in schema && typeof schema.properties === 'object') {
    for (const [key, property] of Object.entries(schema.properties)) {
      if (property) cacheDescriptions(property as Schema, `${objectPath}.${key}`);
    }
  }
};

// Convert TypeScript to JSON Schema
const typeScriptToJsonSchema = (
  absoluteTsPath: string,
  tsType: string,
  newId: string,
  dataUsage: string,
  existingSchema?: Record<string, any>,
): Record<string, any> => {
  const config: Config = {
    path: absoluteTsPath,
    type: tsType,
  };

  const generator = createGenerator(config);
  const schemaRaw = generator.createSchema(config.type);

  const schema = JSON.parse(JSON.stringify(schemaRaw).replace(/#\/definitions\//gm, '')) as unknown as Schema;

  if (!schema.definitions?.[tsType]) {
    throw new Error(`❌ Type "${tsType}" not found in TypeScript file.`);
  }

  if (existingSchema?.models) {
    for (const [key, model] of Object.entries(existingSchema.models)) {
      cacheDescriptions(model as Schema, key);
    }
  }

  const models: Record<string, Schema> = {};
  const mainType = Object.keys(schema.definitions)[0];

  for (const [key, value] of Object.entries(schema.definitions)) {
    const isTopLevel = key === mainType;
    const transformedKey = isTopLevel ? newId : key;
    models[transformedKey] = transformSchema(value as Schema, dataUsage, transformedKey, isTopLevel);
  }

  return models;
};

// Recursively process schema to add descriptions & required fields
const transformSchema = (schema: Schema, dataUsage: string, objectPath: string, isTopLevel?: boolean): any => {
  if (typeof schema !== 'object') return schema;

  const newSchema: any = { ...schema };

  if (descriptionCache[objectPath]) {
    newSchema.description = descriptionCache[objectPath];
  } else if (!newSchema.description) {
    newSchema.description = '';
  }

  const requiredFields = new Set<string>(Array.isArray(newSchema.required) ? newSchema.required : []);
  delete newSchema.required;

  if ('properties' in newSchema && typeof newSchema.properties === 'object') {
    for (const [key, property] of Object.entries(newSchema.properties)) {
      if (property) {
        const propertySchema = transformSchema(property as Schema, dataUsage, `${objectPath}.${key}`);
        propertySchema.required = requiredFields.has(key) || false;
        newSchema.properties[key] = propertySchema;
      }
    }
  }

  delete newSchema.additionalProperties;

  if (isTopLevel && !newSchema.securityclassification) {
    newSchema.securityclassification = dataUsage;
  }

  return newSchema;
};

// Update an existing data contract
const updateDataContract = (
  existingData: Record<string, any>,
  newModels: Record<string, any>,
  newId: string,
  dataUsage: string,
  version: string,
): Record<string, any> => {
  if (!existingData.models) existingData.models = {};

  existingData.models = Object.fromEntries(
    Object.entries(newModels).map(([key, value]) => [
      key === Object.keys(existingData.models)[0] ? newId : key,
      { ...value, securityclassification: dataUsage },
    ]),
  );

  existingData.info.version = version;
  return existingData;
};

// Main function
const createDataContract = async (): Promise<void> => {
  console.log('✨ Creating or updating a Data Contract...');

  const tsType = await askQuestion('TypeScript Type Name (e.g., NewCarOrderKafkaObject)');
  const newId = await askQuestion('New Data Contract ID (leave blank to keep from TS type)', tsType);

  const title = await askQuestion('Title', newId);
  const description = await askQuestion('Description', `Data contract for ${newId}.`);
  const domain = 'Auftragsmanagement';
  const community = 'Kundenauftragssteuerung';
  const status = await askQuestion('Status of the contract', `active`);
  const dataUsage = await askQuestion('Data Usage (internal/external)', 'external');

  const absoluteTsPath = await askQuestion('Absolute path to TypeScript file');
  if (!fs.existsSync(absoluteTsPath)) {
    console.error(`❌ File not found: ${absoluteTsPath}`);
    return;
  }

  const servers: Record<string, any> = {};
  for (;;) {
    const validEnvironments = ['dev', 'int', 'prod'];
    const env = await askQuestion(
      'Server Environment (e.g., dev, int, prod) or ENTER to skip/keep existing definition',
    );
    if (!env) break;
    if (!validEnvironments.includes(env)) {
      console.log('❌ Invalid environment. Please choose between dev, int, or prod.');
      continue;
    }

    const host = await askQuestion(`Host for ${env}`);
    const topic = await askQuestion(`Topics for ${env}`);
    const format = await askQuestion(`Format for ${env}`, 'json');

    servers[env] = { type: 'kafka', host, topic, format };
  }

  const spinner = ora('Generating Data Contract...').start();

  const filePath = path.join(DATA_CONTRACT_DIR, `${newId}.datacontract.json`);
  let existingData: Record<string, any> | undefined;
  let version = DEFAULT_VERSION;

  if (fs.existsSync(filePath)) {
    existingData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    version = upgradeVersion(existingData?.info?.version || DEFAULT_VERSION);
  }

  const newModels = typeScriptToJsonSchema(absoluteTsPath, tsType, newId, dataUsage, existingData);

  const dataContract = existingData
    ? updateDataContract(existingData, newModels, newId, dataUsage, version)
    : {
        dataContractSpecification: 'artkas-0.10.0',
        id: newId,
        info: {
          title,
          version,
          status,
          description,
          owner: DEFAULT_OWNER,
          contact: DEFAULT_CONTACT,
          community,
          domain,
          datausage: dataUsage,
        },
        servers,
        models: newModels,
        examples: [],
      };

  fs.writeFileSync(filePath, JSON.stringify(dataContract, null, 2));
  spinner.succeed('Data Contract Generation completed!');
  console.log(`📁 Data Contract saved at: ${filePath}`);

  rl.close();
};

createDataContract().catch((error) => console.error('❌ Error while creating Data Contract:', error));
