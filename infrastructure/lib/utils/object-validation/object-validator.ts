import { ErrorObject, ValidateFunction } from 'ajv';
import * as validations from './schemas/validations';

/**
 * Validates Objects against the schema of the type Definition
 * Needs both T and objectName to work unfortunately (should be the same in all cases)
 * @param T Type of the returned validated Object
 * @param objectName Type as String of the returned validated Object
 *
 */
export class ObjectValidator<T> {
  private readonly _validate: ValidateFunction<T>;
  public constructor(objectName: keyof typeof validations) {
    this._validate = validations[objectName] as ValidateFunction<T>;
  }
  public validate(data: unknown): [T | null, ErrorObject[] | null | undefined] {
    const valid = this._validate(data);
    if (valid) {
      return [data, null];
    }
    return [null, this._validate.errors];
  }
}
