#!/bin/bash
secret_value=$(aws secretsmanager get-secret-value --secret-id cora-aurora-admin-secret --query 'SecretString' --output text --region eu-west-1)
echo $secret_value
password=$(echo "$secret_value" | jq -r '.password')
dbname=$(echo "$secret_value" | jq -r '.dbname')
username=$(echo "$secret_value" | jq -r '.username')

export PGPASSWORD=$password && psql -h localhost -p 5432 -U $username -d $dbname
