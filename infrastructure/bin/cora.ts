#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { Constants } from '../lib/utils/constants';
import { devProps, intProps, prodProps } from '../lib/context';
import { CoraStack } from '../lib/pipeline/stages/cora-stack';

const app = new cdk.App();

const cdkAccount = process.env.CDK_DEFAULT_ACCOUNT ?? 'undefined';
switch (cdkAccount) {
  case intProps.env.account:
    new CoraStack(app, `${Constants.APPLICATION_SHORT_NAME}-int`, intProps);
    break;
  case prodProps.env.account:
    new CoraStack(app, `${Constants.APPLICATION_SHORT_NAME}-prod`, prodProps);
    break;
  default:
    new CoraStack(app, `${Constants.APPLICATION_SHORT_NAME}-dev`, devProps);
    break;
}

// This stack is not deployed by the pipeline and is only used by developers to deploy a feature stage.
// const developerFeatureStage = new AppStage(
//   app,
//   `${Constants.APPLICATION_SHORT_NAME}-${featureProps.stage}`,
//   featureProps
// );
// cdk.Tags.of(developerFeatureStage).add('Application', Constants.APPLICATION_NAME);
