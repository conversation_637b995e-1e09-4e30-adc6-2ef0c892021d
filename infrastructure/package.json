{"name": "cora", "version": "1.2.0", "bin": {"cora": "bin/cora.ts"}, "scripts": {"build:frontend": "cd ../frontend && npm ci && npm run build", "build": "tsc", "watch": "tsc -w", "test:unit": "jest --testPathPattern=unit --config=jest.unit.cjs --silent --coverage", "test:int": "chmod +x connectToBastionHostForIntTest.sh && ./connectToBastionHostForIntTest.sh && jest --testPathPattern=integration --config=jest.int.cjs", "test:local": "jest --testPathPattern=local --config=jest.local.cjs", "cdk": "cdk", "prettier": "npm run format:check", "format:check": "prettier --check .", "prettier:write": "npm run format:write", "format:write": "prettier --write .", "lint": "eslint .", "lint:fix": "eslint . --fix", "generateDataContract": "ts-node lib/utils/data-contracts/create_data_contracts.ts", "createSchemas": "ts-node lib/utils/object-validation/create_schemas.ts && cp -r lib/utils/object-validation ../frontend/src/utils && rm ../frontend/src/utils/object-validation/create_schemas.ts && rm -r ../frontend/src/utils/object-validation/schemas && mv ../frontend/src/utils/object-validation/frontend/schemas ../frontend/src/utils/object-validation", "postinstall": "npm run createSchemas", "build-layer": "cd lambda/layer && npm install --no-save &&  npm ls pg typeorm typeorm-aurora-data-api-driver > versions.txt && rm -rf ../../dist/typeorm-pg-layer/* && mkdir -p ../../dist/typeorm-pg-layer/ && pwd && rm -rf nodejs && mkdir -p nodejs && mv node_modules versions.txt nodejs/ && zip -r ../../dist/typeorm-pg-layer/typeorm-pg-layer.zip nodejs -x 'nodejs/package*.json'", "typeorm": "typeorm-ts-node-commonjs", "schema:sync": "npx typeorm-ts-node-commonjs schema:sync", "typeorm:cache": "npx typeorm-ts-node-commonjs cache:clear", "schema:drop": "npx typeorm-ts-node-commonjs -d ./schema-migrations/data/datasource.ts", "migration:create": "typeorm migration:create ./schema-migrations/migrations/schema-update", "migration:generate": "npx typeorm-ts-node-commonjs migration:generate ./schema-migrations/migrations/schema-update -d ./schema-migrations/data/datasource.ts", "migration:show": "npx typeorm-ts-node-commonjs migration:show -d ./schema-migrations/data/datasource.ts", "migration:run": "npx typeorm-ts-node-commonjs migration:run -d  ./schema-migrations/data/datasource.ts", "migration:revert": "npx typeorm-ts-node-commonjs migration:revert -d ./schema-migrations/data/datasource.ts"}, "devDependencies": {"@smithy/types": "3.0.0", "@types/aws-lambda": "^8.10.137", "@types/jest": "^29.5.12", "@types/jwk-to-pem": "^2.0.3", "@types/node": "^20.12.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "aws-cdk": "^2.1014.0", "aws-sdk-client-mock": "^4.0.0", "aws-sdk-client-mock-jest": "^4.0.1", "esbuild": "0.20.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "ora": "^8.2.0", "prettier": "^3.2.5", "reflect-metadata": "^0.2.2", "ts-jest": "^29.1.2", "ts-json-schema-generator": "^1.5.1", "ts-node": "^10.9.2", "typeorm": "^0.3.21", "typeorm-aurora-data-api-driver": "^3.0.1", "typescript": "^5.4.5"}, "dependencies": {"@aws-sdk/client-api-gateway": "^3.650.0", "@aws-sdk/client-cloudwatch": "^3.650.0", "@aws-sdk/client-dynamodb": "^3.650.0", "@aws-sdk/client-lambda": "^3.650.0", "@aws-sdk/client-rds": "^3.761.0", "@aws-sdk/client-s3": "^3.650.0", "@aws-sdk/client-scheduler": "^3.699.0", "@aws-sdk/client-secrets-manager": "^3.650.0", "@aws-sdk/client-sfn": "^3.758.0", "@aws-sdk/client-sqs": "^3.650.0", "@aws-sdk/lib-dynamodb": "^3.650.0", "@aws-sdk/util-dynamodb": "^3.650.0", "@kas-resources/constructs": "^0.34.2", "@kas-resources/constructs-rbam": "^0.34.2", "@moia-oss/bastion-host-forward": "^2.1.0", "@types/jsonwebtoken": "^9.0.6", "@types/pg": "^8.6.6", "ajv": "^8.13.0", "ajv-formats": "^3.0.1", "aws-cdk-lib": "^2.195.0", "class-transformer": "^0.5.1", "cloudevents": "^8.0.0", "constructs": "^10.4.2", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "kafkajs": "^2.2.4", "node-fetch": "^3.3.2", "pg": "^8.13.1", "source-map-support": "^0.5.21", "uuid": "^9.0.1", "zip": "^1.2.0"}, "overrides": {"@smithy/types": "3.0.0", "typeorm-aurora-data-api-driver": "^3.0.1"}}