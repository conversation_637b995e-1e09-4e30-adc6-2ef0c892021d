@echo off
REM Retrieve the first instance ID (modify if needed for specific instance selection)
for /f "usebackq delims=" %%i in (`aws ec2 describe-instances --query "Reservations[].Instances[0].InstanceId" --output text --region eu-west-1`) do set target=%%i

REM Retrieve the RDS cluster writer endpoint
for /f "usebackq delims=" %%i in (`aws rds describe-db-cluster-endpoints --query "DBClusterEndpoints[0].Endpoint" --output text --region eu-west-1`) do set host=%%i

REM Start the SSM session with correct variable interpolation
aws ssm start-session ^
  --target "%target%" ^
  --document-name AWS-StartPortForwardingSessionToRemoteHost ^
  --parameters "{\"host\":[\"%host%\"],\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" ^
  --region eu-west-1
