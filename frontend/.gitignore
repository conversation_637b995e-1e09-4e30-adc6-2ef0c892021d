# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
**/nohup.out

# production
/build
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.gitalt
.history
.npmrc

# JSON server database file.
**/tools/db.json

# cypress
e2e.sh
e2e.env
/cypress/screenshots
/cypress/videos