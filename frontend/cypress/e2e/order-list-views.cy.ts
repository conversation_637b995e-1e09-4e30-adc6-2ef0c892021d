import { USERNAME_WRITE } from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';

const displayOrdersTab = '[data-e2e="display_orders"]';
const ncoPrefixThisTest = 'OLV';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP0000');
const preproductionOrderMtvs = generateMtvs(preproductionOrders);

describe('Order Lists Tests', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Display preproduction order list', () => {
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="pk_new_car_order_id"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="business_partner_id"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="order_type"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="quota_month"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="model_year"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="model_text"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="model_type"]').should('be.visible');
    //scroll table to right so that the rest of the columns is visible
    cy.get('[row-index="0"]').find('[col-id="order_status_onevms_error_code"]').scrollIntoView();
    cy.get('[row-index="0"]').find('[col-id="dealer_number"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="dealer_name"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="order_status_onevms_code"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="order_status_onevms_error_code"]').should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="actions"]').should('be.visible');
  });

  it('Sort by order id', () => {
    // Filter for this tests NcoIds
    cy.get('input[aria-label="New Car Order ID Filter Input"]')
      .type(`E2E${ncoPrefixThisTest}`, { delay: 100 })
      .should('have.value', `E2E${ncoPrefixThisTest}`);

    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    assert(preproductionOrders.length >= 2, 'Preproduction Orders must be at least 2 long');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .should('have.text', preproductionOrders[0].pk_new_car_order_id);

    // click again to make it desc
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .should('not.have.text', preproductionOrders[0].pk_new_car_order_id);
  });

  it('Filter by order id', () => {
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');

    assert(preproductionOrders.length >= 2, 'Preproduction Orders must be at least 2 long');
    const newCarOrderId = preproductionOrders[1].pk_new_car_order_id;
    cy.get('input[aria-label="New Car Order ID Filter Input"]')
      .type(newCarOrderId, { delay: 100 })
      .should('have.value', newCarOrderId);

    cy.get('[row-index="0"]', { timeout: 5000 }).should('be.visible');
    cy.get('[row-index="0"]').find('[col-id="pk_new_car_order_id"]').should('have.text', newCarOrderId);
  });
});
