import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import {
  CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED,
  CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
  NOTIFICATION_CENTER_TRANSACTION_BASE_URL,
  USERNAME_WRITE,
} from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { retryableBefore } from '../support/retry';
import { checkTransactionResponse } from '../support/utils';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const deallocateQuotaButton = '[data-e2e="deallocate_quota"]';
const deallocateQuotaMultiButton = '[data-e2e="deallocate_quota"]';
const deallocateQuotaModal = `[data-e2e="${OneVmsEventKey.DEALLOCATE_QUOTA}_order_modal"]`;
const multiDetailsContainer = `[data-e2e="${OneVmsEventKey.DEALLOCATE_QUOTA}_multi_details"]`;
const submitOrderLocator: string = '[data-e2e="accept"]';
const closeBtn = '[data-e2e="close"]';

const ordersEndpointURL = '**/new-car-order**';
const ncoPrefixThisTest = 'DQ';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP2000', 3);
const preproductionOrderMtvs = generateMtvs(preproductionOrders);

describe('Order List Actions Deallocate', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Deallocate quota from single order in list, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 15000 }).should('be.visible');
    //Sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //Deallocate quota from first order
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="quota_month"]').contains(preproductionOrders[0].quota_month!);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(deallocateQuotaButton, { timeout: 3000 }).should('be.visible').click();

    cy.get(deallocateQuotaModal).should('exist').and('not.be.empty');
    cy.get(deallocateQuotaModal).find(submitOrderLocator).shadow().find('[aria-disabled="true"]').should('not.exist');

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('PATCH', `${ordersEndpointURL}/deallocate-quota`).as('deallocateQuota');
    cy.get(deallocateQuotaModal).find(submitOrderLocator).click();
    cy.wait('@deallocateQuota')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //Close the dialog and check if table was updated correctly
    cy.get(deallocateQuotaModal).find(closeBtn).click();
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="quota_month"]').contains(preproductionOrders[0].quota_month!).should('not.exist');
    cy.get('[row-index="0"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED);
    cy.get('[row-index="0"] [col-id="order_status_onevms_error_code"]').contains(
      CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
    );

    //Try to allocate a quota again via usual change order process
    const editOrderButton = '[data-e2e="edit_order"]';
    const changeOrderModal = `[data-e2e="change_order_modal"]`;
    const changeOrderButton = '[data-e2e="submit_order"]';
    const saveKccBtn = '[data-e2e="SaveKcc"]';
    const quotaMonthLocator: string = '[data-e2e="SelectQuotaMonth"]';

    //Click on edit on deallocated order
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(editOrderButton, { timeout: 3000 }).should('be.visible').click();

    //Check iframe and click on kcc save to open cora modal
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 10000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get(changeOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');

    //Try to save without selecting quota month
    cy.get(changeOrderButton).click();
    cy.get(changeOrderModal).find('p-inline-notification', { timeout: 3000 }).should('exist');

    //Select quota month and save
    cy.get(quotaMonthLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(quotaMonthLocator, 1);
    cy.intercept('PATCH', `${ordersEndpointURL}/${preproductionOrders[0].pk_new_car_order_id}`).as('updateNco');
    cy.get(changeOrderButton).click();
    cy.wait('@updateNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //Check that cancellation schedule was created
    cy.task<boolean>('checkNcoCancellationSchedule', preproductionOrders[0].pk_new_car_order_id);
    cy.task('cleanupNcoCancellationSchedules', [preproductionOrders[0].pk_new_car_order_id]);
  });

  it('Deallocate quota from single order from list, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 15000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //deallocate quota from first order
    cy.get('[row-index="0"]').should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 10000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(deallocateQuotaButton, { timeout: 3000 }).should('be.visible').click();

    cy.get(deallocateQuotaModal).should('exist').and('not.be.empty');
    //confirm and intercept cora api cancel call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/deallocate-quota`, (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    });

    cy.get(deallocateQuotaModal).find(submitOrderLocator).click();
    cy.get(deallocateQuotaModal)
      .find(`.header-error-${OneVmsEventKey.DEALLOCATE_QUOTA}`, { timeout: 10000 })
      .should('be.visible');
  });

  it('Deallocate Quota from single order from list, error case order changed (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]').should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 10000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(deallocateQuotaButton, { timeout: 3000 }).should('be.visible').click();

    cy.get(deallocateQuotaModal, { timeout: 20000 }).should('exist').and('not.be.empty');

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/deallocate-quota`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('deallocateQuota');
    cy.get(submitOrderLocator).click({ force: true });
    cy.wait('@deallocateQuota')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });

    //close the dialog (since call was mocked, do not check if table was updated)
    cy.get(deallocateQuotaModal).find(closeBtn).click();
  });

  it('Deallocate quota from multiple orders from list', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 15000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //check table items, mark first 2 items and click multi cancel
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="1"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
    cy.get(deallocateQuotaMultiButton).should('not.exist');
    cy.wait(1000);

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get('[row-index="1"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();

    cy.wait(1000);
    cy.get(deallocateQuotaMultiButton).should('exist');
    cy.get(deallocateQuotaMultiButton).click({ force: true });

    cy.get(deallocateQuotaModal).should('exist').and('not.be.empty');
    cy.get(deallocateQuotaModal).find(multiDetailsContainer).should('be.visible');
    cy.get(deallocateQuotaModal).find(multiDetailsContainer).find('.col-container>.field-col').should('have.length', 2);
    cy.get(deallocateQuotaModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(deallocateQuotaModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);

    cy.intercept('PATCH', `${ordersEndpointURL}/deallocate-quota`).as('deallocateQuota');
    cy.get(deallocateQuotaModal).find(submitOrderLocator).click();
    cy.wait('@deallocateQuota')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });
    cy.get(deallocateQuotaModal).find(closeBtn).click();
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="quota_month"]').contains(preproductionOrders[0].quota_month!).should('not.exist');
    cy.get('[row-index="1"] [col-id="quota_month"]').contains(preproductionOrders[1].quota_month!).should('not.exist');
    cy.get('[row-index="0"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED);
    cy.get('[row-index="0"] [col-id="order_status_onevms_error_code"]').contains(
      CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
    );
    cy.get('[row-index="1"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED);
    cy.get('[row-index="1"] [col-id="order_status_onevms_error_code"]').contains(
      CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR,
    );
  });
});
