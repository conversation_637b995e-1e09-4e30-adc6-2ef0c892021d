import { ModelTypeVisibilityModel } from '../../../infrastructure/lib/entities/model-type-visibility-model';
import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { retryableBefore } from '../support/retry';
import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import {
  checkTransactionResponseHasAnyStatus,
  fetchTransactionsGrouped,
  getTransactionResponseObjectId,
} from '../support/utils';

//use same quota as is mocked in backend
const now = new Date();
const qm = new Date(now.setMonth(now.getMonth())).toISOString().slice(0, 7);

//expected created order
const expectedOrder = {
  dealer_number: '4500940',
  cnr: 'C00',
  importer_code: 'USM',
  importer_number: '4500000',
  model_type: 'E2EMT1',
  model_year: '2024',
  quota_month: qm,
  requested_dealer_delivery_date: '2099-10-01', //beware next generation of coders!
  shipping_code: '1',
};

//add mtv for quota api fetch
const mtvs: ModelTypeVisibilityModel[] = [
  {
    cnr: expectedOrder.cnr,
    importer_number: expectedOrder.importer_number,
    model_type: expectedOrder.model_type,
    my4: expectedOrder.model_year,
    role: 'IMP',
    valid_from: '2023-12-21',
    created_by: 'E2E_TEST',
    modified_by: 'E2E_TEST',
  },
];

describe('Create order and change order', () => {
  retryableBefore(() => {
    cy.login(USERNAME_WRITE);
  });

  beforeEach(() => {
    cy.task('prepareMtvRds', { objs: mtvs }, { timeout: 10000 });
    cy.visit('/');
  });

  after(() => {
    cy.task('cleanupMtvRds', { objs: mtvs });
  });

  it('Create an order directly from CORA without Quotadashboard', () => {
    const createOrderModal = `[data-e2e="create_order_modal"]`;
    const saveKccBtn = '[data-e2e="SaveKcc"]';
    //Create order modal inputs
    const orderTypeLocator: string = '[data-e2e="SelectOrderType"]';
    const shippingCodeLocator: string = '[data-e2e="SelectShippingCode"]';
    const deliveryDateLocator: string = '[data-e2e="DeliveryDate"]';
    const submitOrderLocator: string = '[data-e2e="submit_order"]';
    const ordersEndpointURL = '**/new-car-order**';

    const resultOrderModal = `[data-e2e="${OneVmsEventKey.CREATE}_order_modal"]`;
    const closeBtn = '[data-e2e="close"]';

    //call order creation url with valid query params
    cy.visit(
      `/orders/create?dealer_number=${expectedOrder.dealer_number}&importer_number=${expectedOrder.importer_number}&importer_code=${expectedOrder.importer_code}&model_type=${expectedOrder.model_type}&model_year=${expectedOrder.model_year}&cnr=${expectedOrder.cnr}&quota_month=${expectedOrder.quota_month}`,
    );

    //Click save in KCC and wait for CORA Modal
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find('body').should('not.be.undefined');
    cy.get('iframe', { timeout: 20000 }).its('0.contentDocument').find(saveKccBtn).click();
    cy.get(createOrderModal, { timeout: 20000 }).should('exist').and('not.be.empty');

    //Check Modal view, select some props and save
    cy.get(orderTypeLocator, { timeout: 5000 }).should('be.visible');
    cy.selectOptionFromDropdown(orderTypeLocator, 1);
    cy.selectOptionFromDropdown(shippingCodeLocator, 1);
    cy.get(deliveryDateLocator).type(expectedOrder.requested_dealer_delivery_date);

    cy.intercept('POST', `${ordersEndpointURL}`).as('createNco');
    cy.get(submitOrderLocator).click();

    cy.wait('@createNco')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => {
            const groupedTransactions = fetchTransactionsGrouped(res);
            const hasStatus = checkTransactionResponseHasAnyStatus(groupedTransactions, NotificationStatus.EXPORTED);
            if (hasStatus) {
              // Extract the ncoId when the status is found and alias it.
              const currentNcoId = getTransactionResponseObjectId(groupedTransactions, NotificationStatus.EXPORTED);
              cy.wrap(currentNcoId).as('ncoId');
            }
            // Return the boolean result synchronously.
            return hasStatus;
          },
        );
      });

    cy.get(resultOrderModal).find(closeBtn).click();

    cy.get('@ncoId').then((ncoIdValue) => {
      // Intercept fetch order request and filter orders by the ncoId.
      const resolvedNcoId = ncoIdValue as unknown as string;
      cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
      cy.filterNewCarOrder(resolvedNcoId);
      cy.wait('@fetchOrders');
      cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
      cy.get('[row-index="0"]').find('[col-id="pk_new_car_order_id"]').contains(resolvedNcoId);

      // Delete the created NewCarOrder
      cy.task('cleanupNcoRds', {
        ids: [ncoIdValue],
      });
    });
  });
});
