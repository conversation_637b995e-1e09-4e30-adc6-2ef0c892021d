import { NOTIFICATION_CENTER_TRANSACTION_BASE_URL, USERNAME_WRITE } from '../support/constants';
import { OrderActionStatusCodes } from '../../../infrastructure/lambda/backend/new-car-order/shared-order-action-status-result/types';
import { NotificationStatus, OneVmsEventKey } from '../../../infrastructure/lib/types/process-steering-types';
import { generateNewCarOrders, generateMtvs } from '../support/order-lists-test-data';
import { checkTransactionResponse } from '../support/utils';

const displayOrdersTab = '[data-e2e="display_orders"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';

const moveBtn = '[data-e2e="move_to_dealer_inventory"]';
const rmBtn = '[data-e2e="remove_from_dealer_inventory"]';
const confirmBtn = '[data-e2e="accept"]';
const closeBtn = '[data-e2e="close"]';
const mvModal = `[data-e2e="${OneVmsEventKey.MOVE_TO_INVENTORY}_order_modal"]`;
const rmModal = `[data-e2e="${OneVmsEventKey.REMOVE_FROM_INVENTORY}_order_modal"]`;
const mvOrdersBtn: string = '[data-e2e="move_orders_to_dealer_inventory"]';

const ordersEndpointURL = '**/new-car-order**';
const mvEndpointURL = 'move-to-dealer-inventory';
const rmEndpointURL = 'remove-from-dealer-inventory';

const ncoPrefixThisTest = 'DLI';
const preproductionOrders = generateNewCarOrders(ncoPrefixThisTest, 'PP2000', 3);
const preproductionOrderMtvs = generateMtvs(preproductionOrders);

describe('Order List Actions move-to-dealer-inventory/remove-from-dealer-inventory', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
    cy.task('prepareMtvRds', { objs: preproductionOrderMtvs }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: preproductionOrders }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: preproductionOrders.map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: preproductionOrderMtvs });
  });

  it('Move and Remove for a single order, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //move
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(moveBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(mvModal).should('exist').and('not.be.empty');
    cy.get(mvModal).find('.display-details-single-container').should('be.visible');

    //confirm and check result

    //confirm and check result
    cy.intercept('PATCH', `${ordersEndpointURL}/${mvEndpointURL}`).as('moveToInventory');
    cy.get(mvModal).find(confirmBtn).click();
    cy.wait('@moveToInventory')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    cy.get(mvModal).find(closeBtn).click();

    //remove
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(rmBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(rmModal).should('exist').and('not.be.empty');
    cy.get(rmModal).find('.display-details-single-container').should('be.visible');

    //confirm and check result
    cy.intercept('PATCH', `${ordersEndpointURL}/${rmEndpointURL}`).as('removeFromInventory');
    cy.get(rmModal).find(confirmBtn).click();
    cy.wait('@removeFromInventory')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    cy.get(rmModal).find(closeBtn).click();

    //Check result
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);
  });

  it('Move single order with invalid request body, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //move
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"]')
      .find('[col-id="pk_new_car_order_id"]')
      .contains(preproductionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(moveBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(mvModal).should('exist').and('not.be.empty');
    cy.get(mvModal).find('.display-details-single-container').should('be.visible');

    //confirm and intercept cora api cancel call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/${mvEndpointURL}`, (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    }).as('moveToInventory');
    cy.get(mvModal).find(confirmBtn).click();
    cy.get(mvModal).find(`.header-error-${OneVmsEventKey.MOVE_TO_INVENTORY}`, { timeout: 10000 }).should('be.visible');

    cy.get(mvModal).find(closeBtn).click();
  });

  it('Move single order from list, error case (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get(orderActionsBtn, { timeout: 20000 }).should('be.visible');

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(moveBtn, { timeout: 3000 }).should('be.visible').click();

    cy.get(mvModal).should('exist').and('not.be.empty');
    cy.get(mvModal).find('.display-details-single-container').should('be.visible');

    //confirm and intercept cora api call to be able to let it fail
    cy.intercept('PATCH', `${ordersEndpointURL}/${mvEndpointURL}`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('moveToInventory');

    cy.get(mvModal).find(confirmBtn).click();
    cy.wait('@moveToInventory')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });

    cy.get(mvModal).find(closeBtn).click();
  });

  it('Move multiple orders from list', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayOrdersTab, { timeout: 5000 }).should('be.visible');
    //sort asc by nco id
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');

    //check table items, mark first 2 items and click multi move
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="1"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(preproductionOrders[1].pk_new_car_order_id);
    cy.get(mvOrdersBtn).should('not.exist');
    cy.wait(1000);

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get('[row-index="1"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();

    cy.wait(1000);
    cy.get(mvOrdersBtn).should('exist');
    cy.get(mvOrdersBtn).click({ force: true });

    const multiDetailsContainer = `[data-e2e="${OneVmsEventKey.MOVE_TO_INVENTORY}_multi_details"]`;
    cy.get(mvModal).should('exist').and('not.be.empty');
    cy.get(mvModal).find(multiDetailsContainer).should('be.visible');
    cy.get(mvModal).find(multiDetailsContainer).find('.col-container>.field-col').should('have.length', 2);
    cy.get(mvModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(mvModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);

    //confirm and intercept cora api call (1. order succeed and 2. fail)
    cy.intercept('PATCH', `${ordersEndpointURL}/${mvEndpointURL}`).as('moveToInventory');
    cy.get(mvModal).find(confirmBtn).click();
    cy.wait('@moveToInventory')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //close the dialog
    cy.get(mvModal).find(closeBtn).click();
  });
});
