import { TOTP } from 'totp-generator';
import { USERNAME_READ, USERNAME_WRITE } from './constants';
export {};

Cypress.Commands.add('login', (username: typeof USERNAME_READ | typeof USERNAME_WRITE) => {
  cy.then(Cypress.session.clearCurrentSessionData);
  cy.logout();

  const users = {
    [USERNAME_READ]: {
      password: Cypress.env('USER_PASSWORD_READ'),
      tokenSecret: Cypress.env('TOKEN_SECRET_READ'),
    },
    [USERNAME_WRITE]: {
      password: Cypress.env('USER_PASSWORD_WRITE'),
      tokenSecret: Cypress.env('TOKEN_SECRET_WRITE'),
    },
  };

  const user = users[username];

  assert(user, 'User is known');

  cy.session(
    username,
    () => {
      cy.visit('https://kasauth.kas-dev.dpp.porsche.com/refreshauth');

      cy.elementExists('[id=username]').then((unInput) => {
        if (unInput) {
          cy.get('[id=username]').should('be.visible').type(username);
          cy.get('[id=password]').should('be.visible').type(user.password).type('{enter}');

          const { otp } = TOTP.generate(user.tokenSecret);
          cy.origin('https://authenticator.pingone.eu', { args: { otp } }, ({ otp }) => {
            cy.get('.passcode-input').should('be.visible').type(otp, { delay: 100 });

            cy.get('input[type="submit"]').should('not.be.disabled');

            cy.get('.passcode-input').should('be.visible').type('{enter}');
          });

          cy.url({ timeout: 15000 }).should('include', 'paddock-dev');
        }
      });
    },
    {
      validate() {
        cy.request('https://api.cora-dev.dpp.porsche.com/rbam-permissions').its('status').should('eq', 200);
      },
      cacheAcrossSpecs: true,
    },
  );
});

Cypress.Commands.add('logout', () => {
  cy.request({ url: 'https://kasauth.kas-dev.dpp.porsche.com/logout', failOnStatusCode: false });
});

Cypress.Commands.add('getUserMenuBtn', () => {
  return cy.get('.nav-user').find('[data-e2e="userMenu"]');
});

Cypress.Commands.add('getUserMenuDropdown', () => {
  return cy.get('.nav-user').find('.dropdown-content>div>div');
});

Cypress.Commands.add('getUserMenuDropdownRow', (row: number) => {
  return cy.getUserMenuDropdown().eq(row).find('>p-text');
});

Cypress.Commands.add('selectOptionFromDropdown', (locator: string, option: string | number) => {
  cy.get(locator, { timeout: 20000 }).click().get(`${locator} > select`).select(option, { force: true });
});

Cypress.Commands.add('searchForText', (locator: string, toSearchText: string) => {
  cy.get(locator).click().get(`${locator} > input`).type(toSearchText);
  cy.wait(1000);
  cy.get(`${locator} > input`).type('{enter}');
});

Cypress.Commands.add('elementExists', (selector: string) => {
  return cy.window().then(($window) => $window.document.querySelector(selector));
});

Cypress.Commands.add('clickOrderActions', (orderActionsBtn: string, rowIndex?: number) => {
  const tippyDropdown = '[data-tippy-root]';
  const rowIndexSelector = `[row-index="${rowIndex ?? 0}"]`;

  cy.get(rowIndexSelector)
    .find('[col-id="model_text"]', { timeout: 10000 })
    .should(($el) => {
      chai.expect($el.text().toLowerCase()).not.to.include('loading');
    });

  cy.wait(500);
  cy.get(rowIndexSelector).find(orderActionsBtn).click({ scrollBehavior: 'center' });

  cy.get(tippyDropdown, { timeout: 10000 }).should('be.visible');
});

Cypress.Commands.add('getSelectorInIframe', (selectorInIframe: string, timeout?: number) => {
  return cy
    .get('selectorInIframe')
    .its('0.contentDocument.body')
    .should('not.be.empty')
    .then(cy.wrap)
    .find(selectorInIframe, { timeout: timeout ? timeout : 0 });
});

Cypress.Commands.add('getIframeBody', (iframeSelector?: string) => {
  return cy
    .get(iframeSelector ? iframeSelector : 'iframe')
    .its('0.contentDocument.body')
    .should('not.be.empty')
    .then(cy.wrap);
});

Cypress.Commands.add('filterNewCarOrder', (ncoPrefix: string) => {
  cy.get('input[aria-label="New Car Order ID Filter Input"]')
    .clear()
    .type(ncoPrefix, { delay: 100 })
    .should('have.value', ncoPrefix);
});

Cypress.Commands.add('filterPIs', (piPrefix: string) => {
  cy.get('input[aria-label="Purchase Intention ID Filter Input"]')
    .clear()
    .type(piPrefix, { delay: 100 })
    .should('have.value', piPrefix);
});

Cypress.Commands.add('pollApiUntilCheckFnSucceeds', (url, checkFn, options = {}) => {
  const { retries = 10, delay = 1000 } = options;

  let attempt = 0;

  function poll(): Cypress.Chainable<JQuery<any>> {
    attempt++;
    Cypress.log({ name: 'pollApi', message: `Attempt ${attempt}` });

    return cy.request({ url: url, failOnStatusCode: false }).then((response) => {
      if (checkFn(response)) {
        return cy.wrap(response.body, { log: false });
      }

      if (attempt >= retries) {
        throw new Error('Max retries reached. Value not found.');
      }

      return cy.wait(delay, { log: false }).then(poll);
    });
  }

  return poll();
});

declare global {
  namespace Cypress {
    interface Chainable<Subject> {
      login(username: typeof USERNAME_READ | typeof USERNAME_WRITE): Chainable<void>;
      logout(): Chainable<void>;
      elementExists(selector: string): Chainable<Subject>;
      getUserMenuBtn(): Chainable<Subject>;
      getUserMenuDropdown(): Chainable<Subject>;
      getUserMenuDropdownRow(row: number): Chainable<Subject>;
      filterNewCarOrder(ncoPrefix: string): Chainable<Subject>;
      filterPIs(piPrefix: string): Chainable<Subject>;
      selectOptionFromDropdown(locator: string, option: string | number): Chainable<Subject>;
      searchForText(locator: string, toSearchText: string): Chainable<Subject>;
      getSelectorInIframe(selectorInIframe: string, timeout?: number): Chainable<Subject>;
      getIframeBody(iframeSelector?: string): Chainable<Subject>;
      clickOrderActions(orderActionsBtn: string, rowIndex?: number): Chainable<Subject>;
      pollApiUntilCheckFnSucceeds(
        url: string,
        checkFn: (response: Cypress.Response<unknown>) => boolean,
        options?: { retries?: number; delay?: number },
      ): Chainable<JQuery<any>>;
    }
  }
}
