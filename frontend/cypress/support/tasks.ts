import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { BatchWriteCommand, BatchWriteCommandInput } from '@aws-sdk/lib-dynamodb';
import { NativeAttributeValue } from '@aws-sdk/util-dynamodb';
import { DataSource, In } from 'typeorm';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';
import {
  APPLICATION_SHORT_NAME,
  SSM_AURORA_WRITER_SECRET_ARN_PARAM,
  buildCancellationScheduleName,
  buildTotalLossScheduleName,
} from './constants';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../infrastructure/lib/entities/new-car-order-model';
import { CoraPurchaseIntentionModel } from '../../../infrastructure/lib/entities/purchase-intention-model';
import { NewCarOrderAuditTrailModel } from '../../../infrastructure/lib/entities/new-car-order-audit-trail-model';
import {
  DeleteScheduleCommand,
  DeleteScheduleCommandInput,
  GetScheduleCommand,
  GetScheduleCommandInput,
  SchedulerClient,
} from '@aws-sdk/client-scheduler';
import { ModelTypeVisibilityModel } from '../../../infrastructure/lib/entities/model-type-visibility-model';

export interface DynamoCleanupProps {
  tableName: string;
  pks: Record<string, NativeAttributeValue>[];
}

export interface DynamoPrepareProps {
  tableName: string;
  objs: Record<string, Record<string, unknown>>[];
}

const dynamoDb = new DynamoDBClient({ region: 'eu-west-1' });

//cleanup all given pks from tableName
export const cleanupDynamodb = (props: DynamoCleanupProps) => {
  const params = {
    RequestItems: {
      [props.tableName]: props.pks.map((sc) => ({
        DeleteRequest: {
          Key: sc, //eg { pk_shipping_code: sc }
        },
      })),
    },
  };
  return dynamoDb.send(new BatchWriteCommand(params));
};

//insert all given objs into tableName
export const prepareDynamodb = async (props: DynamoPrepareProps) => {
  const params: BatchWriteCommandInput = {
    RequestItems: {
      [props.tableName]: props.objs.map((sc) => ({
        PutRequest: {
          Item: sc,
        },
      })),
    },
  };
  return dynamoDb.send(new BatchWriteCommand(params));
};

let rdsDataSource: DataSource | undefined;

//helper functions for nco rds db setup
export interface RdsPrepareNcoProps {
  objs: NewCarOrderModel[];
}

export interface RdsCleanupNcoProps {
  ids: string[];
}

export const prepareNcoRds = async (props: RdsPrepareNcoProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    await rdsDataSource?.getRepository(NewCarOrderModel).save(props.objs);
    return null;
  } catch (error) {
    console.error(error);
  }
};

export const cleanupNcoRds = async (props: RdsCleanupNcoProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    await rdsDataSource?.manager.delete(NewCarOrderModel, { pk_new_car_order_id: In(props.ids) });
    await rdsDataSource?.manager.delete(NewCarOrderAuditTrailModel, { pk_new_car_order_id: In(props.ids) });
    return null;
  } catch (error) {
    console.error(error);
  }
};

export interface RdsPrepareMtvProps {
  objs: ModelTypeVisibilityModel[];
}
export interface RdsCleanupMtvProps {
  objs: ModelTypeVisibilityModel[];
}
export const prepareMtvRds = async (props: RdsPrepareMtvProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    await rdsDataSource
      ?.getRepository(ModelTypeVisibilityModel)
      .upsert(props.objs, ['role', 'importer_number', 'my4', 'model_type', 'cnr']);
    return null;
  } catch (error) {
    console.error(error);
  }
};

export const cleanupMtvRds = async (props: RdsCleanupMtvProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    const promises: Promise<unknown>[] = [];
    for (const obj of props.objs) {
      promises.push(
        rdsDataSource!.manager.delete(ModelTypeVisibilityModel, {
          role: obj.role,
          importer_number: obj.importer_number,
          my4: obj.my4,
          model_type: obj.model_type,
          cnr: obj.cnr,
        }),
      );
    }
    await Promise.all(promises);
    return null;
  } catch (error) {
    console.error(error);
  }
};

const schedulerEBClient = new SchedulerClient({ region: 'eu-west-1' });

export const cleanupNcoCancellationSchedules = async (ncoIds: string): Promise<null | undefined> => {
  try {
    for (const ncoId of ncoIds) {
      const deleteInput: DeleteScheduleCommandInput = {
        Name: buildCancellationScheduleName('dev', ncoId),
      };
      await schedulerEBClient.send(new DeleteScheduleCommand(deleteInput));
    }
    return null;
  } catch (error) {
    console.error('Error deleting Schedule', error);
  }
};

export const checkNcoCancellationSchedule = async (ncoId: string): Promise<null | undefined> => {
  const getInput: GetScheduleCommandInput = {
    Name: buildCancellationScheduleName('dev', ncoId),
  };
  try {
    await schedulerEBClient.send(new GetScheduleCommand(getInput));
    return null;
  } catch (error) {
    console.error('Error getting Schedule', error);
    return undefined;
  }
};

export const cleanupNcoPersistTotalLossSchedules = async (ncoIds: string): Promise<null | undefined> => {
  try {
    for (const ncoId of ncoIds) {
      const deleteInput: DeleteScheduleCommandInput = {
        Name: buildTotalLossScheduleName('dev', ncoId),
      };
      await schedulerEBClient.send(new DeleteScheduleCommand(deleteInput));
    }
    return null;
  } catch (error) {
    console.error('Error deleting Schedule', error);
  }
};

export const checkNcoPersistTotalLossSchedule = async (ncoId: string): Promise<boolean | undefined> => {
  const getInput: GetScheduleCommandInput = {
    Name: buildTotalLossScheduleName('dev', ncoId),
  };
  try {
    await schedulerEBClient.send(new GetScheduleCommand(getInput));
    return true; // Schedule exists
  } catch (error: any) {
    if (error.name === 'ResourceNotFoundException') {
      return false; // Schedule does not exist
    }
    return undefined;
  }
};

//helper functions for pi rds db setup
export interface RdsPreparePiProps {
  objs: CoraPurchaseIntentionModel[];
}

export interface RdsCleanupPiProps {
  key: string;
  ids: string[];
}

export const preparePiRds = async (props: RdsPreparePiProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    await rdsDataSource?.getRepository(CoraPurchaseIntentionModel).save(props.objs);
    return null;
  } catch (error) {
    console.error(error);
  }
};

export const cleanupPiRds = async (props: RdsCleanupPiProps): Promise<null | undefined> => {
  try {
    await initDataSource();
    await rdsDataSource?.manager.delete(CoraPurchaseIntentionModel, { purchase_intention_id: In(props.ids) });
    return null;
  } catch (error) {
    console.error(error);
  }
};

const initDataSource = async (): Promise<void> => {
  if (!rdsDataSource) {
    const aurora_writer_secret_arn = await fetchSSMParameter(
      `/${APPLICATION_SHORT_NAME.toLocaleLowerCase()}/dev/${SSM_AURORA_WRITER_SECRET_ARN_PARAM}`,
    );
    const rdsSecret = await fetchRdsSecret(aurora_writer_secret_arn);
    rdsDataSource = await new DataSource({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: (rdsSecret as RdsSecret).username,
      password: (rdsSecret as RdsSecret).password,
      database: (rdsSecret as RdsSecret).dbname,
      synchronize: false,
      ssl: {
        rejectUnauthorized: false,
      },
      logging: ['error'], // ['error'] to remove log spam | true for log spam
      entities: [
        NewCarOrderModel,
        NcoConfigurationModel,
        NcoConfigOrderedOptionsModel,
        CoraPurchaseIntentionModel,
        NewCarOrderAuditTrailModel,
        ModelTypeVisibilityModel,
      ],
    }).initialize();
  }
};

async function fetchSSMParameter(parameterName: string): Promise<string> {
  const ssmClient = new SSMClient({ region: 'eu-west-1' });
  try {
    const command = new GetParameterCommand({
      Name: parameterName,
      WithDecryption: true,
    });
    const response = await ssmClient.send(command);
    const parameterValue = response.Parameter?.Value;
    console.log('Fetched SSM Parameter:', parameterValue);
    if (parameterValue) {
      return parameterValue;
    } else {
      throw new Error(`Could not find the parameter ${parameterName}`);
    }
  } catch (error) {
    console.error('Failed to fetch SSM Parameter:', error);
    throw error;
  }
}

interface RdsSecret {
  host: string;
  port: string;
  username: string;
  password: string;
  dbname: string;
}

async function fetchRdsSecret(secretArn: string): Promise<RdsSecret | Uint8Array | undefined> {
  try {
    const sm = new SecretsManagerClient({ region: 'eu-west-1' });
    const secretValue = await sm.send(
      new GetSecretValueCommand({
        SecretId: secretArn,
      }),
    );
    return secretValue.SecretString
      ? (JSON.parse(escape_newlines_in_data(secretValue.SecretString)) as RdsSecret | Uint8Array)
      : (secretValue.SecretBinary as RdsSecret | Uint8Array);
  } catch (error) {
    console.error('error fetching rds secret', error);
  }
}

/**
 * Escape newlines in json data fields, e.g. in Certificate Secret used for AWS Lambda triggers
 * @param json_str json in stringformat with potentially malformed data (containing \n)
 */
function escape_newlines_in_data(json_str: string): string {
  let _tmp_string = json_str.split('"');
  _tmp_string = _tmp_string.map((s, index) => {
    if (index % 2 !== 0) {
      s = s.replace(/\n/g, '\\\\n');
    }
    return s;
  });
  return _tmp_string.join('"');
}

declare global {
  namespace Cypress {
    interface Chainable {
      task(event: 'prepareDynamodb', arg: DynamoPrepareProps): Chainable<void>;
      task(event: 'cleanupDynamodb', arg: DynamoCleanupProps): Chainable<void>;
    }
  }
}

export const tasks = {
  prepareDynamodb,
  cleanupDynamodb,
  prepareNcoRds,
  cleanupNcoRds,
  prepareMtvRds,
  cleanupMtvRds,
  preparePiRds,
  cleanupPiRds,
  cleanupNcoCancellationSchedules,
  checkNcoCancellationSchedule,
  cleanupNcoPersistTotalLossSchedules,
  checkNcoPersistTotalLossSchedule,
};
