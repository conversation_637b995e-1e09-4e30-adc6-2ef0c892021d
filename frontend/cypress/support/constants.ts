export const USERNAME_READ = 'kastechcora';
export const FULLNAME_READ = 'Cora Technical User KAS';

export const USERNAME_WRITE = 'kastechcora2';
export const FULLNAME_WRITE = 'Cora2 Technical User KAS';
export const USER_WRITE_ORG_ID = '4a143ab0-3331-11e2-ba48-fb232678f3db';

export const APPLICATION_SHORT_NAME = 'cora';
export const SSM_AURORA_WRITER_SECRET_ARN_PARAM = 'Aurora/coraAuroraWriterSecretArn';

export const DYNAMODB_NEW_CAR_ORDER_TABLE_NAME = 'new-car-order';
export const DYNAMODB_MODEL_TYPE_VISIBILITY_TABLE_NAME = 'model-type-visibility';
export const DYNAMODB_PVMS_PURCHASE_INTENTIONS = 'pvms-purchase-intentions';
export const DYNAMODB_NEW_CAR_ORDER_ID_TABLE_NAME = 'new-car-order-id';
export const DYNAMODB_QUOTAS_TABLE_NAME = 'quotas';
export const DYNAMODB_FAILED_ORDERS = 'failed-status-mappings';

export const NOTIFICATION_CENTER_TRANSACTION_BASE_URL =
  'https://api.cora-dev.dpp.porsche.com/notification-center/transaction/';

export const buildTableName = (stage: string, tableName: string): string => {
  return `${APPLICATION_SHORT_NAME}-${stage}-${tableName}`;
};
export const DYNAMODB_NEW_CAR_ORDER_LISTS_TABLE_NAME = 'new-car-orders-for-lists';

export const DYNAMODB_PVMS_PURCHASE_INTENTIONS_FOR_LISTS = 'pvms-purchase-intentions-for-lists';

export const CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED = 'PP1300';
export const CORA_NEW_CAR_ORDER_STATUS_QUOTA_DEALLOCATED_ERROR = 'FBP500';
export const CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS = 'OX1100';
export const CORA_NEW_CAR_ORDER_STATUS_PERSIST_TOTAL_LOSS = 'OX1000';

export const buildCancellationScheduleName = (stage: string, ncoId: string): string => {
  return `${buildCancellationSchedulePrefix(stage)}-${ncoId}`;
};

const buildCancellationSchedulePrefix = (stage: string): string => {
  return `${APPLICATION_SHORT_NAME}-${stage}-nco-cancellation-schedule`;
};

export const buildTotalLossScheduleName = (stage: string, ncoId: string): string => {
  return `${buildTotalLossSchedulePrefix(stage)}-${ncoId}`;
};

const buildTotalLossSchedulePrefix = (stage: string): string => {
  return `${APPLICATION_SHORT_NAME}-${stage}-nco-total-loss-schedule`;
};
