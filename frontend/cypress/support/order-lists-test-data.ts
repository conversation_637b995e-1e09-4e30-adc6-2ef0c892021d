import { ModelTypeVisibilityModel } from '../../../infrastructure/lib/entities/model-type-visibility-model';
import { NewCarOrderModel } from '../../../infrastructure/lib/entities/new-car-order-model';
import { CoraPurchaseIntentionModel } from '../../../infrastructure/lib/entities/purchase-intention-model';
import { OneVmsSourceSystemKey } from '../../../infrastructure/lib/types/process-steering-types';

//use same quota as is mocked in backend
const now = new Date();
export const validMockedQuotaMonth = new Date(now.setMonth(now.getMonth())).toISOString().slice(0, 7);

export const preproductionOrders: NewCarOrderModel[] = [
  {
    pk_new_car_order_id: '00E2E001',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT1',
    model_year: '2024',
    order_status_onevms_code: 'PP0000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
    order_invoice_onevms_code: 'PI4000',
    order_type: 'VF',
    quota_month: '2024-04',
    requested_dealer_delivery_date: '2999-10-01',
    shipping_code: '1',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
    modified_at: new Date().toISOString(),
  },
  {
    pk_new_car_order_id: '00E2E002',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT2',
    model_year: '2024',
    order_status_onevms_code: 'PP0000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
    order_invoice_onevms_code: 'PI4000',
    order_type: 'KF',
    quota_month: '2024-04',
    requested_dealer_delivery_date: '2024-10-01',
    shipping_code: '2',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
    modified_at: new Date().toISOString(),
  },
  {
    pk_new_car_order_id: '00E2E003',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT3',
    model_year: '2024',
    order_status_onevms_code: 'PP2000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
    order_invoice_onevms_code: 'PI4000',
    order_type: 'LF',
    quota_month: '2024-05',
    requested_dealer_delivery_date: '2999-10-01',
    shipping_code: '2',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
    modified_at: new Date().toISOString(),
  },
];

export const inDistributionOrders: NewCarOrderModel[] = [
  {
    pk_new_car_order_id: 'IDE2E001',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT911',
    model_year: '2025',
    order_status_onevms_code: 'ID0000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2025-01-10T08:10:48.193Z',
    order_invoice_onevms_code: 'dunno',
    order_type: 'KF',
    quota_month: '2025-04',
    requested_dealer_delivery_date: '2999-10-01',
    shipping_code: '4',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
  },
  {
    pk_new_car_order_id: 'IDE2E002',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT912',
    model_year: '2025',
    order_status_onevms_code: 'ID0000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2025-01-10T08:10:48.193Z',
    order_invoice_onevms_code: 'dunno',
    order_type: 'KF',
    quota_month: '2024-04',
    requested_dealer_delivery_date: '2024-10-01',
    shipping_code: '2',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
  },
];

//mtvs for every order/pi so that orders/pis are visible in lists
export const generateMtvs = (orders: (NewCarOrderModel | CoraPurchaseIntentionModel)[]): ModelTypeVisibilityModel[] =>
  orders.map((order) => ({
    role: 'IMP',
    valid_from: '2024-01-01',
    cnr: order.cnr,
    importer_number: order.importer_number,
    model_type: order.model_type,
    my4: order.model_year,
    created_by: 'E2E_TEST',
    modified_by: 'E2E_TEST',
  }));

// MTVs for every kind of order
export const preproductionOrderMtvs = generateMtvs(preproductionOrders);
export const inDistributionOrderMtvs = generateMtvs(inDistributionOrders);

/**
 *
 * @param ncoPrefix Should only be 2-3 characters long
 * @param oneVmsStatus OneVms Status e.g. (PP0000, ID5000)
 * @param n Amount of ncos to generate (default 3)
 * @param ncoBase different default base obj
 *
 * @description ModelType is set based on the ncoPrefix and n i.e. `E2E${ncoPrefix}MT${i}`
 */
export const generateNewCarOrders = (
  ncoPrefix: string,
  oneVmsStatus: string = 'PP0000',
  n = 3,
  ncoBase?: Partial<NewCarOrderModel>,
): NewCarOrderModel[] => {
  const baseNco = {
    pk_new_car_order_id: '00E2E001',
    dealer_number: '4500940',
    business_partner_id: '0020000866',
    cnr: 'C00',
    created_by: 'kastechcora2',
    importer_code: 'USM',
    importer_number: '4500000',
    model_type: 'E2EMT1',
    model_year: '2024',
    order_status_onevms_code: 'PP0000',
    order_status_onevms_error_code: 'null',
    order_status_onevms_timestamp_last_change: '2022-12-06T10:21:02.876Z',
    order_invoice_onevms_code: 'PI4000',
    order_type: 'VF',
    quota_month: '2024-04',
    requested_dealer_delivery_date: '2999-10-01',
    shipping_code: '1',
    configuration: {
      created_by: 'kastechcora2',
      modified_by: 'kastechcora2',
      ordered_options: [],
      technical_options: [],
    },
    configuration_expire: { dummyProp: 'iAmDummy' },
    changed_by_system: OneVmsSourceSystemKey.CORA,
    modified_by: 'kastechcora2',
    modified_at: new Date().toISOString(),
  };
  const res: NewCarOrderModel[] = [];
  for (let i = 1; i <= n; i++) {
    res.push({
      ...baseNco,
      ...ncoBase,
      pk_new_car_order_id: `E2E${ncoPrefix}${String(i).padStart(3, '0')}`,
      model_type: `E2E${ncoPrefix}MT${i}`,
      order_status_onevms_code: oneVmsStatus,
    });
  }
  return res;
};

/**
 *
 * @param piPrefix Should only be 2-3 characters long
 * @param n Amount of pis to generate (default 3)
 *
 * @description ModelType is set based on the ncoPrefix and n i.e. `E2E${ncoPrefix}MT${i}`
 */
export const generatePIs = (piPrefix: string, n = 3): CoraPurchaseIntentionModel[] => {
  const now = new Date();
  const qm = new Date(now.setMonth(now.getMonth())).toISOString().slice(0, 7);
  const basePi = {
    dealer_number: '4500940',
    importer_code: 'USM',
    importer_number: '4500000',
    model_year: '2024',
    order_type: 'KF',
    quota_month: qm,
    shipping_code: '2',
    created_by: 'kastechcora2',
    modified_by: 'kastechcora2',
    cnr: 'C00',
    vehicle_status_code: 'V070',
    seller: '123456',
    business_partner_id: 'dummy',
    vehicle_configuration_pvmsnext: { testProp: 'Test' },
    vehicle_configuration_onevms: null,
    modified_at: new Date().toISOString(),
  };

  const res: CoraPurchaseIntentionModel[] = [];
  for (let i = 1; i <= n; i++) {
    res.push({
      ...basePi,
      purchase_intention_id: `E2E${piPrefix}${String(i).padStart(3, '0')}`,
      model_type: `E2E${piPrefix}MT${i}`,
    });
  }
  return res;
};
