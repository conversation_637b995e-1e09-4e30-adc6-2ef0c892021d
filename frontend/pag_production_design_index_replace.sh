#!/bin/sh

alias platform_agnostic_sed="sed -i"
if [[ "$OSTYPE" == "darwin"* ]]; then
  alias platform_agnostic_sed="sed -i ''"
fi
placeholder='<!--PLACEHOLDER_FONT_LINKS-->' &&
partial=$placeholder$(node -e 'console.log(require("@porsche-design-system/components-react/partials").getFontLinks())') &&
regex=$placeholder'.*' &&
platform_agnostic_sed -E -e "s^$regex^$partial^" index.html;
placeholder='<!--PLACEHOLDER_INITIAL_STYLES-->' &&
partial=$placeholder$(node -e 'console.log(require("@porsche-design-system/components-react/partials").getInitialStyles())') &&
regex=$placeholder'.*' &&
platform_agnostic_sed -E -e "s^$regex^$partial^" index.html
