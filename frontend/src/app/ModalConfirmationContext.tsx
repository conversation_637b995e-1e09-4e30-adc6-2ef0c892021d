import React, { useContext, useRef, useState } from 'react';
import ConfirmationModal from '../components/confirmation-modal/ConfirmationModal';

interface UseModalShowReturnType {
  show: boolean;
  setShow: (value: boolean) => void;
  onHide: () => void;
}

const useModalShow = (): UseModalShowReturnType => {
  const [show, setShow] = useState<boolean>(false);

  const handleOnHide = (): void => {
    setShow(false);
  };

  return {
    show,
    setShow,
    onHide: handleOnHide,
  };
};

interface ModalContextType {
  showConfirmation: (title: string, message: string | JSX.Element) => Promise<boolean>;
}

interface ConfirmationModalContextProviderProps {
  children: React.ReactNode;
}

const ConfirmationModalContext = React.createContext<ModalContextType>({} as ModalContextType);

const ConfirmationModalContextProvider: React.FC<ConfirmationModalContextProviderProps> = (props) => {
  const { setShow, show, onHide } = useModalShow();
  const [content, setContent] = useState<{
    title: string;
    message: string | JSX.Element;
  } | null>();
  const resolver = useRef<(arg: boolean) => unknown>();

  const handleShow = async (title: string, message: string | JSX.Element): Promise<boolean> => {
    setContent({
      title,
      message,
    });
    setShow(true);
    return new Promise(function (resolve) {
      resolver.current = resolve;
    });
  };

  const modalContext: ModalContextType = {
    showConfirmation: handleShow,
  };

  const handleOk = (): void => {
    resolver.current?.(true);
    onHide();
  };

  const handleCancel = (): void => {
    resolver.current?.(false);
    onHide();
  };

  return (
    <ConfirmationModalContext.Provider value={modalContext}>
      {props.children}
      {content && (
        <ConfirmationModal
          isOpen={show}
          onClose={onHide}
          title={content.title}
          message={content.message}
          handleCancel={handleCancel}
          handleOk={handleOk}
        />
      )}
    </ConfirmationModalContext.Provider>
  );
};

const useConfirmationModalContext = (): ModalContextType => useContext(ConfirmationModalContext);

export { useModalShow, useConfirmationModalContext };

export default ConfirmationModalContextProvider;
