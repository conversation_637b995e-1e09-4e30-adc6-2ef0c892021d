import { PInlineNotification } from '@porsche-design-system/components-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDeallocateQuotaMutation } from '../../store/api/NewCarOrderApi';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import ProcessSteeringOrderActionModal from '../shared/process-steering-components/order-action-common-modal/ProcessSteeringOrderActionModal';
import {
  DefaultApiRequest,
  NcoIdWithModifiedAt,
  OneVmsEventKey,
} from '../../../../infrastructure/lib/types/process-steering-types';

const DeallocateQuotaModal: React.FC<CommonOrderActionProps> = (props: CommonOrderActionProps) => {
  const { t } = useTranslation();
  const [DeallocateQuota, deallocateResult] = useDeallocateQuotaMutation();

  function handleSubmit(): void {
    const nco_ids_with_modified_at: NcoIdWithModifiedAt[] = props.orders.map((order) => ({
      pk_new_car_order_id: order.pk_new_car_order_id,
      modified_at: order.modified_at!,
    }));
    const apiRequest: DefaultApiRequest = {
      nco_ids_with_modified_at,
    };
    DeallocateQuota({
      data: apiRequest,
    });
  }

  return (
    <ProcessSteeringOrderActionModal
      orders={props.orders}
      isLoading={deallocateResult.isLoading}
      isSubmitDisabled={deallocateResult.isLoading}
      isError={deallocateResult.isError}
      isSuccess={deallocateResult.isSuccess}
      error={deallocateResult.error}
      result={deallocateResult.data}
      actionType={props.actionType}
      closeModal={props.closeModal}
      handleSubmit={handleSubmit}
      CustomComponent={() => {
        return (
          <PInlineNotification style={{ marginBottom: '20px' }} dismissButton={false}>
            {t(`${OneVmsEventKey.DEALLOCATE_QUOTA}_info`)}
          </PInlineNotification>
        );
      }}
    ></ProcessSteeringOrderActionModal>
  );
};

export default DeallocateQuotaModal;
