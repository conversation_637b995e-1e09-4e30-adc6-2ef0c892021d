import { PSelectWrapper } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthContext } from '../../../app/AuthContext';
import { AuthorizedDealer, AuthorizedImporter } from '../../../store/types';

interface ImporterDealerChangeFormProps {
  setError: (message: string | undefined) => void;
  importersFiltered: AuthorizedImporter[];
  setDealerNumberSource: (message: string) => void;
  setDealerNameSource: (message: string) => void;
  setDealerNameTarget: (message: string) => void;
  setDealerNumberTarget: (message: string) => void;
  setImporterNumber: (message: string) => void;
  setImporterName: (message: string) => void;
  importerNumber: string;
  dealerNumberSource: string;
  dealerNumberTarget: string;
}

export const ImporterDealerChange: React.FC<ImporterDealerChangeFormProps> = ({
  importersFiltered,
  setDealerNumberSource,
  setDealerNameSource,
  setDealerNameTarget,
  setDealerNumberTarget,
  setImporterNumber,
  setImporterName,
  importerNumber,
  dealerNumberSource,
  dealerNumberTarget,
}) => {
  const { t } = useTranslation();
  const [dealersSource, setDealersSource] = useState<AuthorizedDealer[]>([]);
  const [dealersTarget, setDealersTarget] = useState<AuthorizedDealer[]>([]);

  const [disableButtons, setDisableButtons] = useState<boolean>(true);
  const [disableDealersDropdown, setDisableDealersDropdown] = useState<boolean>(true);
  const authContext = useAuthContext();

  useEffect(() => {
    if (importersFiltered.length === 0) {
      setDisableButtons(true);
    }
    if (importersFiltered.length === 1) {
      setImporterNumber(importersFiltered[0].importer_number);
    }
    if (importersFiltered.length > 1) {
      setDisableButtons(false);
    }
  }, [importersFiltered]);
  useEffect(() => {
    if (importerNumber) {
      const filteredDealers = authContext.dealers.filter((dealer) => dealer.importer_number === importerNumber);
      setDealersSource(filteredDealers);
    }
    if (importerNumber === '-') {
      setDealersSource([]); // clean dealers
      setDisableDealersDropdown(true); // disable dropdowns
    } else {
      setDisableDealersDropdown(false);
    }
  }, [importerNumber]);
  useEffect(() => {
    if (dealerNumberSource) {
      const filteredDealers = dealersSource.filter((dealer) => dealer.dealer_number !== dealerNumberSource);
      setDealersTarget(filteredDealers);
    }
  }, [dealerNumberSource]);

  return (
    <>
      <div className="form-container1" style={{ paddingBottom: '20px' }}>
        <div className="field-row1">
          <div className="field-col1">
            <PSelectWrapper data-e2e="SelectImporter" filter={true} label={t('buy_sell_transfer_importer')}>
              <select
                value={importerNumber}
                disabled={disableButtons}
                onChange={(e) => {
                  const selectedImporterNumber = e.target.value;
                  setImporterNumber(selectedImporterNumber);
                  const selectedImporter = importersFiltered.find(
                    (importer) => importer.importer_number === selectedImporterNumber,
                  );
                  setImporterName(selectedImporter?.display_name || '');
                  setDealerNumberSource('');
                  setDealerNumberTarget('');
                }}
              >
                <option value="">{t('buy_sell_transfer_importer')}</option>
                {importersFiltered.map((importer, index) => (
                  <option key={index} value={importer.importer_number}>
                    {`${importer.importer_number} - ${importer.display_name}`}
                  </option>
                ))}
              </select>
            </PSelectWrapper>
          </div>
          <div className="field-col1"></div>
        </div>
        <div className="field-row1">
          <div className="field-col1">
            <PSelectWrapper data-e2e="SelectSourceDealer" filter={true} label={t('buy_sell_transfer_source_dealer')}>
              <select
                value={dealerNumberSource}
                disabled={disableButtons || disableDealersDropdown}
                onChange={(e) => {
                  const selectedDealerNumber = e.target.value;
                  setDealerNumberSource(selectedDealerNumber);
                  const selectedDealer = dealersSource.find((dealer) => dealer.dealer_number === selectedDealerNumber);
                  setDealerNameSource(selectedDealer?.display_name || '');
                  setDealerNumberTarget('');
                }}
              >
                <option value="">{t('importer_transfer_select_dealer')}</option>
                {dealersSource.map((dealer, index) => (
                  <option key={index} value={dealer.dealer_number}>
                    {`${dealer.dealer_number} - ${dealer.display_name}`}
                  </option>
                ))}
              </select>
            </PSelectWrapper>
          </div>
          <div className="field-col1">
            <PSelectWrapper data-e2e="SelectDestDealer" filter={true} label={t('buy_sell_transfer_target_dealer')}>
              <select
                value={dealerNumberTarget}
                disabled={disableButtons || dealersTarget.length === 1 || disableDealersDropdown}
                onChange={(e) => {
                  const selectedDealerNumber = e.target.value;
                  setDealerNumberTarget(selectedDealerNumber);
                  const selectedDealer = dealersTarget.find((dealer) => dealer.dealer_number === selectedDealerNumber);
                  setDealerNameTarget(selectedDealer?.display_name || '');
                }}
              >
                <option value="">{t('importer_transfer_select_dealer')}</option>
                {dealersTarget.map((dealer, index) => (
                  <option key={index} value={dealer.dealer_number}>
                    {`${dealer.dealer_number} - ${dealer.display_name}`}
                  </option>
                ))}
              </select>
            </PSelectWrapper>
          </div>
        </div>
      </div>
    </>
  );
};
