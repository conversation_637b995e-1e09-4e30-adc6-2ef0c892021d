import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  PIcon,
  PInlineNotification,
  PLink,
  PModal,
  PSpinner,
  PText,
} from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraNCOBuySellTransferApiRequest } from '../../../../../infrastructure/lib/types/new-car-order-types';
import { useAuthContext } from '../../../app/AuthContext';
import { useConfirmationModalContext } from '../../../app/ModalConfirmationContext';
import { routes } from '../../../Constants';
import { useGetNcoIdsListMutation } from '../../../store/api/NewCarOrderApi';
import { useBuySellTransferOrdersMutation } from '../../../store/api/SpecialActionsApi';
import { useGetStageConfigQuery } from '../../../store/api/StaticJsonApi';
import { BuySellTransferSteps, ModalState } from '../../../store/types';
import './BuySellTransferModal.css';
import { ImporterDealerChange } from './ImporterDealerChange';
import TransferOrdersList from './TranferOrdersList';

export interface BuySellTransferModalProps {
  closeModal: (state: ModalState) => void;
}

const BuySellTransferModal: React.FC<BuySellTransferModalProps> = (props: BuySellTransferModalProps) => {
  const { t } = useTranslation();
  const [modalState, setModalState] = useState<ModalState>('preview');
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
  const [dealerNumberSource, setDealerNumberSource] = useState<string | undefined>(undefined);
  const [dealerNameSource, setDealerNameSource] = useState<string | undefined>(undefined);
  const [dealerNameTarget, setDealerNameTarget] = useState<string | undefined>(undefined);
  const [dealerNumberTarget, setDealerNumberTarget] = useState<string | undefined>(undefined);
  const [importerNumber, setImporterNumber] = useState<string | undefined>(undefined);
  const [importerName, setImporterName] = useState<string | undefined>(undefined);
  const [total, setTotal] = useState<string | undefined>(undefined);
  const [successes, setSuccesses] = useState<string | undefined>(undefined);
  const [wasFail, setWasFail] = useState<boolean>(false);
  const [orderIds, setOrderIds] = useState<string[]>([]);
  const authContext = useAuthContext();
  const importers = authContext.importers;
  const [getIds, { data: orders, isLoading: isOrdersLoading, isError: isOrdersErrors }] = useGetNcoIdsListMutation();
  const [step, setStep] = useState<BuySellTransferSteps>(BuySellTransferSteps.SELECTION);
  const { data: stageConfig } = useGetStageConfigQuery(undefined);
  function handleNextStep(): void {
    if (dealerNumberSource && importerNumber) {
      getIds({ dealer_number: dealerNumberSource });
    }
  }
  useEffect(() => {
    if (orders && isOrdersLoading === false && dealerNumberSource) {
      setTotal(orders.new_car_order_ids.length.toString());
    }
    if (isOrdersErrors) {
      setTotal('0');
    }
  }, [isOrdersLoading, orders]);
  const [
    buySellTransferOrders,
    {
      isLoading: isTransferingOrders,
      isError: isTransferingError,
      isSuccess: isTransferingSuccess,
      data: transferResult,
      error,
    },
  ] = useBuySellTransferOrdersMutation();

  function handleSubmit(): void {
    if (dealerNumberSource && dealerNumberTarget && orders?.new_car_order_ids) {
      const apiRequest: CoraNCOBuySellTransferApiRequest = {
        source_dealer_number: dealerNumberSource,
        target_dealer_number: dealerNumberTarget,
        new_car_order_ids: orders.new_car_order_ids,
      };
      buySellTransferOrders({ data: apiRequest });
    }
  }

  function handleCancel(): void {
    setStep(BuySellTransferSteps.SELECTION);
    setDealerNameSource(undefined);
    setDealerNameTarget(undefined);
    setDealerNumberSource(undefined);
    setDealerNumberTarget(undefined);
    setTotal(undefined);
    setOrderIds([]);
    props.closeModal(modalState);
  }

  useEffect(() => {
    if (isTransferingError || isTransferingSuccess) {
      if (isTransferingSuccess && transferResult) {
        setSuccesses(transferResult.new_car_order_ids.length.toString());
        const ids: string[] = transferResult.new_car_order_ids.map((item) => item).filter((id) => id !== undefined);
        setOrderIds(ids);
        setWasFail(!isTransferingSuccess);
      }
      if (error && 'status' in error && error.data && !isTransferingSuccess) {
        const errorMessage = (error.data as { message?: string })?.message || 'An unknown error occurred';
        setWasFail(isTransferingError);
        setErrorMessage(errorMessage);
      }
      setModalState('result');
    }
  }, [isTransferingError, isTransferingSuccess]);
  const { showConfirmation } = useConfirmationModalContext();

  const handleConfirmationModal = async () => {
    const message = t('buy_sell_transfer_confirm_message', {
      amount: total,
      dealerNumber: dealerNumberTarget,
      dealerName: dealerNameTarget,
    });
    const confirmed = await showConfirmation(t('buy_sell_transfer_confirm'), message);

    if (confirmed) {
      handleSubmit();
      setStep(BuySellTransferSteps.FINAL);
    } else {
      handleCancel();
    }
  };

  return (
    <PModal data-e2e="BuySellModal" open dismissButton={false} onDismiss={handleCancel}>
      <div slot="header" style={{ minWidth: '500px', paddingBottom: '10px' }}>
        {modalState === 'preview' && <PHeading size="large">{t('buy_sell_transfer_header')}</PHeading>}
        {modalState === 'result' && !wasFail && (
          <div className="header-success-container">
            <PIcon size="medium" name="success-filled" color="notification-success"></PIcon>
            <PHeading data-e2e="BuySellSuccessHeading" size="large">
              {t('buy_sell_transfer_success_header', { successes, total })}
            </PHeading>
          </div>
        )}
        {modalState === 'result' && wasFail && (
          <div className="header-error-container">
            <PInlineNotification data-e2e={'noti_error_head_multi'} state="error" dismissButton={false}>
              {t('buy_sell_transfer_fail_noti')}
            </PInlineNotification>
            <PHeading data-e2e="BuySellErrorHeading" style={{ paddingTop: '15px' }} size="large">
              {t('buy_sell_transfer_fail_head', { successes: '0', total })}
            </PHeading>
            <div style={{ marginTop: '20px' }}>
              <PText data-e2e="BuySellErrorMsg" size={'small'}>
                {errorMessage}
              </PText>
            </div>
          </div>
        )}
      </div>
      {(isOrdersLoading || isTransferingOrders) && <PSpinner className="centered-foreground-spinner" size="large" />}

      {!isOrdersLoading && modalState === 'preview' && step === BuySellTransferSteps.SUMMARY && (
        <div>
          <div data-e2e="BuySellDetailsContainer" className="buy-sell-transfer-details-single-container">
            <div className="field-col">
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_importer_number')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {importerNumber ?? '-'}
                </PText>
              </div>
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_source_dealer_number')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {dealerNumberSource ?? '-'}
                </PText>
              </div>
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_source_dealer_name')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {dealerNameSource ?? '-'}
                </PText>
              </div>
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_source_total_orders')}
                </PText>
                <PText data-e2e="totalOrders" weight={'bold'} size={'x-small'}>
                  {total ?? '-'}
                </PText>
              </div>
            </div>
            <div className="field-col">
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_importer_name')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {importerName ?? '-'}
                </PText>
              </div>

              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_target_dealer_number')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {dealerNumberTarget ?? '-'}
                </PText>
              </div>
              <div className="field-row">
                <PText color="contrast-medium" size={'xx-small'}>
                  {t('buy_sell_transfer_target_dealer_name')}
                </PText>
                <PText weight={'bold'} size={'x-small'}>
                  {dealerNameTarget ?? '-'}
                </PText>
              </div>
            </div>
          </div>
          <PDivider></PDivider>
        </div>
      )}

      {modalState === 'preview' && step === BuySellTransferSteps.SELECTION && (
        <ImporterDealerChange
          setError={setErrorMessage}
          importersFiltered={importers}
          setDealerNumberSource={setDealerNumberSource}
          setDealerNameSource={setDealerNameSource}
          setDealerNameTarget={setDealerNameTarget}
          setDealerNumberTarget={setDealerNumberTarget}
          setImporterNumber={setImporterNumber}
          setImporterName={setImporterName}
          importerNumber={importerNumber || '-'}
          dealerNumberSource={dealerNumberSource || '-'}
          dealerNumberTarget={dealerNumberTarget || '-'}
        ></ImporterDealerChange>
      )}
      {modalState === 'result' && (
        <TransferOrdersList
          orders={orderIds}
          heading={t('buy_sell_transfer_success')}
          dataE2e={'buy_sell_transfer_result_list'}
        ></TransferOrdersList>
      )}

      {modalState === 'preview' ? (
        <div className="cancel-footer-btn-container">
          <PButton
            data-e2e="accept_transfer"
            variant="primary"
            disabled={
              (step === BuySellTransferSteps.SELECTION &&
                (!importerNumber?.trim() || !dealerNumberSource?.trim() || !dealerNumberTarget?.trim())) ||
              (step === BuySellTransferSteps.SUMMARY && total === '0') ||
              isOrdersLoading ||
              isTransferingOrders
            }
            onClick={(): void => {
              if (step === BuySellTransferSteps.SELECTION) {
                handleNextStep();
                setStep(BuySellTransferSteps.SUMMARY);
              }
              if (step === BuySellTransferSteps.SUMMARY) {
                handleConfirmationModal();
              }
            }}
          >
            {step === BuySellTransferSteps.SELECTION ? t('buy_sell_transfer_next') : t('buy_sell_transfer_confirm')}
          </PButton>
          <PButton
            data-e2e="cancel"
            variant="tertiary"
            disabled={isTransferingOrders || isOrdersLoading}
            title={t('cancel')}
            aria-label={t('cancel')}
            onClick={handleCancel}
          >
            {t('cancel')}
          </PButton>
        </div>
      ) : (
        <div className="cancel-footer-btn-container">
          <PLink
            data-e2e="go_to_paddock_main_page"
            variant="primary"
            href={routes.external_urls.paddockFullPath(stageConfig?.stage)}
            target={'_top'}
          >
            {t('buy_sell_transfer_finish_main')}
          </PLink>
        </div>
      )}
    </PModal>
  );
};

export default BuySellTransferModal;
