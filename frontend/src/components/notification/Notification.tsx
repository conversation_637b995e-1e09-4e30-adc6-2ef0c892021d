import { PBanner } from '@porsche-design-system/components-react';
import { useAppSelector, useAppDispatch } from '../../app/hooks';
import {
  selectNotifications,
  dropNotification,
  NotificationAction,
  INotification,
} from '../../store/slices/NotificationSlice';
import { useTranslation } from 'react-i18next';
import { JSX, useEffect, useState } from 'react';

export function Notification(): JSX.Element {
  const notifications = useAppSelector(selectNotifications);
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const [shownNotification, setShownNotifications] = useState<(INotification & { timeoutValue: number })[]>([]);
  useEffect(() => {
    const _shownNotifications = notifications
      .map((n) => {
        const timeoutValue = (n.showUntil ?? new Date().getTime() + 60000) - new Date().getTime();
        if (timeoutValue < 0) {
          dispatch(dropNotification(n));
        }
        return { ...n, timeoutValue };
      })
      .filter((n_filter) => n_filter.timeoutValue > 0);
    setShownNotifications(_shownNotifications);
  }, [dispatch, notifications]);

  return (
    <>
      {notifications.length > 0 &&
        shownNotification.map((notification, index) => {
          return (
            <PBanner
              data-e2e={'notification-' + notification.state}
              open={true}
              className="over-everything"
              state={notification.state}
              onTransitionEnd={(): void => {
                setTimeout(() => {
                  dispatch(dropNotification(notification));
                }, notification.timeoutValue);
              }}
              onDismiss={(): NotificationAction => {
                return dispatch(dropNotification(notification));
              }}
              key={`${index}-${notification.notificationId}`}
            >
              <span slot="header" hidden={notification.title === ''}>
                {t(notification.title ?? '')}
              </span>
              <span slot="description">
                {typeof notification.msg === 'object' ? JSON.stringify(notification.msg) : t(notification.msg)}
              </span>
            </PBanner>
          );
        })}
    </>
  );
}
