import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  PIcon,
  PInlineNotification,
  PModal,
  PSpinner,
  PText,
} from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraNCOImporterTransferApiRequest } from '../../../../infrastructure/lib/types/new-car-order-types';
import { useGetImporterForDealerQuery } from '../../store/api/MasterdataApi';
import { useImporterTransferOrdersMutation } from '../../store/api/NewCarOrderApi';
import { ModalState } from '../../store/types';
import { matchMttForOrderOrPurchaseIntention } from '../../utils/utils';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import { ImporterDealer } from './ImporterDealerTable';
import { ImporterMultiTransferForm } from './ImporterMultiTransferForm';
import { ImporterSingleTransferForm } from './ImporterSingleTransferForm';
import ImporterTransferList from './ImporterTransferList';
import ImporterTransferListResult from './ImporterTransferListResult';
import './ImporterTransferModal.css';

const ImporterTransferModal: React.FC<CommonOrderActionProps> = (props: CommonOrderActionProps) => {
  const { t } = useTranslation();
  const [modalState, setModalState] = useState<ModalState>('preview');
  const [errorMessage, setErrorMessage] = useState<string>();
  const [dealerNumberNew, setDealerNumberNew] = useState<string | undefined>('');
  const [importerNumberNew, setImporterNumberNew] = useState<string | undefined>('');
  const [dealerNameNew, setDealerNameNew] = useState<string | undefined>('');
  const [importerNameNew, setImporterNameNew] = useState<string | undefined>('');
  const [step, setStep] = useState<string>('1');
  const [orderTypeSelect, setOrderTypeSelect] = useState<string>('');
  const [shippingCodeSelect, setShippingCodeSelect] = useState<string>('');
  const [portCodeSelect, setPortCodeSelect] = useState<string>('');
  const [subFormError, setSubFormError] = useState<string>(); //TODO subFormError is never read and the child components SETS it, but READS a local subFormError state var...
  const [successes, setSuccesses] = useState<string>();
  const [total, setTotal] = useState<string>();
  const [hasAccess, setHasAccess] = useState<boolean>(false);

  const checkStep1 = (): boolean => {
    return !dealerNumberNew?.trim() || !importerNumberNew?.trim();
  };
  const checkStep2 = (): boolean => {
    const isOrderTypeFilled = !!orderTypeSelect?.trim();
    const isShippingCodeFilled = !!shippingCodeSelect?.trim();
    const isPortCodeFilled = !!portCodeSelect?.trim();
    const isButtonDisabled =
      !isOrderTypeFilled || !isShippingCodeFilled || (hasAccess && !isPortCodeFilled) || isImporterTransferLoading;
    return isButtonDisabled;
  };

  const [
    triggerImporterTransfer,
    {
      data: importerTransferResult,
      isLoading: isImporterTransferLoading,
      isError: isImporterTransferError,
      error: importerTransferError,
      isSuccess: isImporterTransferSuccess,
    },
  ] = useImporterTransferOrdersMutation();
  const { data: currentDealerRelationData, isLoading: currentDealerLoading } = useGetImporterForDealerQuery(
    props.orders[0].dealer_number ?? '',
    { skip: !props.orders[0].dealer_number },
  );
  const isMulti = props.orders.length > 1;
  function handleCancel(): void {
    setStep('');
    setOrderTypeSelect('');
    setShippingCodeSelect('');
    setPortCodeSelect('');
    setDealerNumberNew('');
    setImporterNumberNew('');
    setDealerNameNew('');
    setImporterNameNew('');
    props.closeModal(modalState);
  }
  useEffect(() => {
    if (isImporterTransferError || isImporterTransferSuccess) {
      setModalState('result');
    }
  }, [isImporterTransferError, isImporterTransferSuccess]);
  function handleSubmit(): void {
    const ids_and_modified_at_map: Record<string, string> = Object.fromEntries(
      props.orders.map((order) => [
        order.pk_new_car_order_id,
        order.modified_at ? order.modified_at : '', // empty if it s never modified
      ]),
    );
    if (importerNumberNew && dealerNumberNew) {
      const apiRequest: CoraNCOImporterTransferApiRequest = {
        new_car_order_ids: ids_and_modified_at_map,
        to_importer_number: importerNumberNew,
        to_dealer_number: dealerNumberNew,
        new_nco_attributes: {
          order_type: orderTypeSelect,
          shipping_code: shippingCodeSelect,
          receiving_port_code: portCodeSelect,
        },
      };
      triggerImporterTransfer({ data: apiRequest });
    }
  }
  const successfulTransfers = importerTransferResult?.filter((item) => item.isSuccess);
  const failedTransfers = importerTransferResult?.filter((item) => !item.isSuccess);
  const isPartialFail = failedTransfers && failedTransfers?.length >= 1;
  const isSuccessfulTransfered = successfulTransfers && successfulTransfers?.length === 1;
  useEffect(() => {
    if (props.orders) setTotal(props.orders?.length.toString());
  }, [props.orders]);
  useEffect(() => {
    if (successfulTransfers) setSuccesses(successfulTransfers?.length.toString());
  }, [successfulTransfers]);
  return (
    <PModal data-e2e="importer_transfer_order_modal" open dismissButton={false} onDismiss={handleCancel}>
      <div slot="header" style={{ minWidth: '500px', paddingBottom: '10px' }}>
        {modalState === 'preview' && (
          <PHeading size="large">
            {t(isMulti ? 'importer_transfer_orders_modal' : 'importer_transfer_order_modal')}
          </PHeading>
        )}
        {modalState === 'result' && isMulti && isPartialFail && (
          <div className="header-error-container">
            <PHeading data-e2e="importer_transfer_fail_head" size="large">
              {t('importer_transfer_partial_fail', { successes, total })}
            </PHeading>
            <PInlineNotification dismissButton={false} state="info">
              {t('importer_transfer_user_guide')}
            </PInlineNotification>
          </div>
        )}
        {modalState === 'result' && isMulti && !isPartialFail && (
          <div className="header-success-container">
            <PIcon
              data-e2e="importer_transfer_success_icon"
              size="medium"
              name="success-filled"
              color="notification-success"
            ></PIcon>
            <PHeading data-e2e="importer_transfer_fail_head" size="large">
              {t('importer_transfer_successful_transfer_multi_header', { successes, total })}
            </PHeading>
          </div>
        )}
        {modalState === 'result' && !isMulti && !isSuccessfulTransfered && (
          <div className="header-error-container">
            <PHeading data-e2e="importer_transfer_fail_head" size="large">
              {t('importer_transfer_failed_transfer_single')}
            </PHeading>
            {failedTransfers && (
              <PInlineNotification data-e2e={'noti_error_head_multi'} state="error" dismissButton={false}>
                {t('importer_transfer_failed_noti_single')}
                <br></br>
                {failedTransfers[0].error_msg}
              </PInlineNotification>
            )}
          </div>
        )}
        {modalState === 'result' && !isMulti && isSuccessfulTransfered && (
          <div className="header-success-container">
            <PIcon
              data-e2e="importer_transfer_success_icon"
              size="medium"
              name="success-filled"
              color="notification-success"
            ></PIcon>
            <PHeading data-e2e="importer_transfer_fail_head" size="large">
              {t('importer_transfer_successful_transfer_single', { successes, total })}
            </PHeading>
          </div>
        )}
      </div>
      {step === '1' &&
        (isMulti ? (
          <ImporterTransferList
            dataE2e="import_multi_details"
            orders={props.orders}
            heading={t('selected_orders')}
          ></ImporterTransferList>
        ) : (
          <div>
            <div className="importer-transfer-details-single-container">
              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('pk_new_car_order_id')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].pk_new_car_order_id}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('order_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].order_type}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_text')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {matchMttForOrderOrPurchaseIntention(props.modelTypeTexts, props.orders[0], t)}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_type}
                  </PText>
                </div>
              </div>

              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('quota_month')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].quota_month}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_year')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_year}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('dealer_name')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].dealer_name ?? '-'}
                  </PText>
                </div>
              </div>
            </div>
            <PDivider></PDivider>
          </div>
        ))}
      {step === '2' &&
        (isMulti ? (
          <ImporterTransferList
            dataE2e="import_multi_details"
            orders={props.orders}
            heading={t('selected_orders')}
          ></ImporterTransferList>
        ) : (
          <div>
            <div className="importer-transfer-details-single-container">
              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('pk_new_car_order_id')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].pk_new_car_order_id}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('order_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].order_type}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_text')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {matchMttForOrderOrPurchaseIntention(props.modelTypeTexts, props.orders[0], t)}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_type}
                  </PText>
                </div>
              </div>

              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_year')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_year}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_dealer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {dealerNameNew ?? '-'}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_importer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {importerNameNew ?? '-'}
                  </PText>
                </div>
              </div>
            </div>
            <PDivider></PDivider>
          </div>
        ))}
      {currentDealerLoading && <PSpinner className="centered-foreground-spinner" size="large" />}
      {step === '1' && !currentDealerLoading && (
        <ImporterDealer
          dealer_number={props.orders[0].dealer_number}
          importer_number={props.orders[0].importer_number}
          model_type={props.orders[0].model_type}
          model_year={props.orders[0].model_year}
          dealer_name={props.orders[0].dealer_name}
          importer_name={currentDealerRelationData?.importer.display_name}
          errorInput={errorMessage}
          isMulti={isMulti}
          setDealerNumberNew={setDealerNumberNew}
          setImporterNumberNew={setImporterNumberNew}
          setDealerNameNew={setDealerNameNew}
          setImporterNameNew={setImporterNameNew}
          dealerNumberNew={dealerNumberNew}
          importerNew={importerNumberNew}
          setError={setErrorMessage}
          orders={props.orders}
        ></ImporterDealer>
      )}
      {step === '2' && !isMulti && (
        <ImporterSingleTransferForm
          setError={setErrorMessage}
          order={props.orders[0]}
          dealer_number={dealerNumberNew || ''}
          setOrderTypeSelect={setOrderTypeSelect}
          setShippingCodeSelect={setShippingCodeSelect}
          setPortCodeSelect={setPortCodeSelect}
          setSubFormError={setSubFormError}
          orderTypeSelect={orderTypeSelect}
          shippingCodeSelect={shippingCodeSelect}
          quotaMonth={props.orders[0].quota_month || ''}
          setHasAccess={setHasAccess}
        ></ImporterSingleTransferForm>
      )}
      {step === '2' && isMulti && (
        <ImporterMultiTransferForm
          setError={setErrorMessage}
          dealer_number={dealerNumberNew || ''}
          setOrderTypeSelect={setOrderTypeSelect}
          setShippingCodeSelect={setShippingCodeSelect}
          setPortCodeSelect={setPortCodeSelect}
          setSubFormError={setSubFormError}
          orderTypeSelect={orderTypeSelect}
          shippingCodeSelect={shippingCodeSelect}
          setHasAccess={setHasAccess}
        ></ImporterMultiTransferForm>
      )}
      {isImporterTransferLoading && <PSpinner className="centered-foreground-spinner" size="large" />}
      {step === '3' &&
        modalState === 'result' &&
        (isMulti ? (
          successfulTransfers && successfulTransfers?.length >= 1 ? (
            <ImporterTransferListResult
              orders={successfulTransfers}
              isSuccess={true}
              heading={t('importer_transfer_successful_transfer_multi')}
              dataE2e={''}
            ></ImporterTransferListResult>
          ) : (
            <div></div>
          )
        ) : successfulTransfers && successfulTransfers?.length === 1 ? (
          <div>
            <div className="importer-transfer-details-single-container">
              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('pk_new_car_order_id')}
                  </PText>
                  <PText data-e2e="new_pk_new_car_order_id" weight={'bold'} size={'x-small'}>
                    {successfulTransfers[0].new_new_car_order_id}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('order_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {orderTypeSelect}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_text')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {matchMttForOrderOrPurchaseIntention(props.modelTypeTexts, props.orders[0], t)}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_importer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {importerNameNew ?? '-'}
                  </PText>
                </div>
              </div>

              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_old_id')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {successfulTransfers[0].old_new_car_order_id}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('quota_month')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].quota_month}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_year')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_year}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_dealer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {dealerNameNew ?? '-'}
                  </PText>
                </div>
              </div>
            </div>
            <PDivider></PDivider>
          </div>
        ) : (
          <div></div>
        ))}
      {step === '3' &&
        modalState === 'result' &&
        (isMulti ? (
          failedTransfers && failedTransfers?.length > 0 ? (
            <ImporterTransferListResult
              orders={failedTransfers}
              isSuccess={false}
              heading={t('importer_transfer_failed_transfer')}
              dataE2e={''}
            ></ImporterTransferListResult>
          ) : (
            <div></div>
          )
        ) : failedTransfers && failedTransfers?.length === 1 ? (
          <div>
            <div className="importer-transfer-details-single-container">
              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('pk_new_car_order_id')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].pk_new_car_order_id}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('order_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].order_type}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_text')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {matchMttForOrderOrPurchaseIntention(props.modelTypeTexts, props.orders[0], t)}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_type')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_type}
                  </PText>
                </div>
              </div>

              <div className="field-col">
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('quota_month')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].quota_month}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('model_year')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {props.orders[0].model_year}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_importer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {dealerNameNew ?? '-'}
                  </PText>
                </div>
                <div className="field-row">
                  <PText color="contrast-medium" size={'xx-small'}>
                    {t('importer_transfer_new_dealer')}
                  </PText>
                  <PText weight={'bold'} size={'x-small'}>
                    {importerNameNew ?? '-'}
                  </PText>
                </div>
              </div>
            </div>
            <PDivider></PDivider>
          </div>
        ) : (
          <div></div>
        ))}
      {modalState === 'preview' ? (
        <div className="importer-transfer-footer-btn-container">
          <PButton
            data-e2e="accept"
            variant="primary"
            disabled={
              step === '1' ? checkStep1() : step === '2' ? (!isImporterTransferLoading ? checkStep2() : false) : true
            }
            title={step === '1' ? t('importer_transfer_next_step') : t('importer_transfer_order_yes')}
            aria-label={step === '1' ? t('importer_transfer_next_step') : t('importer_transfer_order_yes')}
            onClick={(): void => {
              if (step === '1') {
                setStep('2');
              }
              if (step === '2') {
                handleSubmit();
                setStep('3');
              }
            }}
          >
            {step === '1' ? t('importer_transfer_next_step') : t('importer_transfer_order_yes')}
          </PButton>
          <PButton
            data-e2e="cancel"
            variant="tertiary"
            disabled={isImporterTransferLoading}
            title={t('cancel')}
            aria-label={t('cancel')}
            onClick={handleCancel}
          >
            {t('cancel')}
          </PButton>
        </div>
      ) : (
        <PButton
          data-e2e="close"
          variant="primary"
          title={t('importer_transfer_order_finish')}
          aria-label={t('importer_transfer_order_finish')}
          onClick={handleCancel}
        >
          {t('importer_transfer_order_finish')}
        </PButton>
      )}
    </PModal>
  );
};

export default ImporterTransferModal;
