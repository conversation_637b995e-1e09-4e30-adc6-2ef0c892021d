.header-success-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-success-container p-icon {
  padding-right: 5px;
}

.header-error-container {
  display: flex;
  flex-direction: column;
}

.importer-transfer-form-container {
  padding-bottom: 30px;
}

.importer-transfer-footer-btn-container {
  display: flex;
  justify-content: flex-start;
}

.importer-transfer-footer-btn-container p-button {
  padding-right: 10px;
}

.importer-transfer-details-single-container {
  display: flex;
  flex-direction: row;
}

.importer-transfer-details-single-container .field-col {
  display: flex;
  flex-grow: 10;
  flex-direction: column;
}

.importer-transfer-details-single-container .field-row > p-text:nth-child(2) {
  padding-bottom: 7px;
}
