import { <PERSON>pinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { CoraMdPortCodeResponseItem } from '../../../../infrastructure/lib/types/masterdata-types';
import { OrderTypeForm } from '../../pages/car-order-flows/form-elements/OrderTypeForm';
import { PortCodeForm } from '../../pages/car-order-flows/form-elements/PortCodeForm';
import { ShippingCodeForm } from '../../pages/car-order-flows/form-elements/ShippingCodeForm';
import {
  useGetOrderTypesForDealerQuery,
  useGetPortCodesForDealerQuery,
  useGetShippingCodesForDealerQuery,
} from '../../store/api/MasterdataApi';
import { AppDispatch } from '../../store/configureStore';
import './ImporterMultiTransferForm.css';

interface ImporterMultiTransferFormProps {
  setError: (err?: string) => void;
  dealer_number: string;
  setOrderTypeSelect: (value: string) => void;
  setShippingCodeSelect: (value: string) => void;
  setPortCodeSelect: (value: string) => void;
  setSubFormError: (value: string) => void;
  orderTypeSelect: string;
  shippingCodeSelect: string;
  setHasAccess?: (access: boolean) => void;
}

export const ImporterMultiTransferForm: React.FC<ImporterMultiTransferFormProps> = ({
  setError,
  dealer_number,
  setOrderTypeSelect,
  setShippingCodeSelect,
  setPortCodeSelect,
  setSubFormError,
  orderTypeSelect,
  shippingCodeSelect,
  setHasAccess,
}) => {
  const { t, i18n } = useTranslation();
  const [subFormErrors, setSubFormErrors] = useState<Record<string, string | undefined>>({}); //never set but read
  const dispatch = useDispatch<AppDispatch>();

  const [standardPortCode, setStandardPortCode] = useState<CoraMdPortCodeResponseItem | undefined>(undefined);
  const { isLoading: isOrderTypeLoading } = useGetOrderTypesForDealerQuery(dealer_number);
  const { isLoading: isShippingCodeLoading } = useGetShippingCodesForDealerQuery(dealer_number);
  const { data: portCodes, isLoading: isPortCodesLoading } = useGetPortCodesForDealerQuery(dealer_number);
  const [alternativePortCodes, setAlternativePortCodes] = useState<CoraMdPortCodeResponseItem[] | undefined>(undefined);

  useEffect(() => {
    if (Object.values(subFormErrors).some((v) => !!v)) {
      setError(t('err_in_form'));
    } else {
      setError(undefined);
    }
  }, [subFormErrors]);

  useEffect(() => {
    if (portCodes?.length === 0) {
      return;
    } else {
      setStandardPortCode(portCodes?.find((p) => p.standard));
      setAlternativePortCodes(portCodes?.filter((p) => !p.standard));
    }
  }, [portCodes]);
  const initialPortCode =
    standardPortCode?.pk_port_code ??
    (alternativePortCodes && alternativePortCodes.length > 0 ? alternativePortCodes[0].pk_port_code : '');
  setPortCodeSelect(initialPortCode);
  return (
    <>
      {isOrderTypeLoading && isShippingCodeLoading && isPortCodesLoading && (
        <PSpinner className="centered-foreground-spinner" size="large" />
      )}
      {!isOrderTypeLoading && !isShippingCodeLoading && !isPortCodesLoading && (
        <div className="form-container1" style={{ paddingBottom: '20px' }}>
          <div className="field-row1">
            <div className="field-col1">
              <OrderTypeForm
                dealer_number={dealer_number}
                isCustomerRelated={false}
                selectedOrderType={orderTypeSelect}
                handleSelect={setOrderTypeSelect}
                setError={setSubFormError}
              />
            </div>
            <div className="field-col1"></div>
          </div>

          <div className="field-row1">
            <div className="field-col1">
              <ShippingCodeForm
                dealer_number={dealer_number}
                selectedShippingCode={shippingCodeSelect}
                handleShippingCodeSelect={setShippingCodeSelect}
                setError={setSubFormError}
              />
            </div>
            <div className="field-col1">
              <PortCodeForm
                dealer_number={dealer_number}
                selectedReceivingPortCode={undefined}
                handleReceivingPortCodeSelect={setPortCodeSelect}
                setError={setSubFormError}
                setHasAccess={setHasAccess}
              />
            </div>
          </div>
        </div>
      )}
      <div className="field-col"></div>
    </>
  );
};
