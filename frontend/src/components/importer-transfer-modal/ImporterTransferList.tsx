import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ider, PText } from '@porsche-design-system/components-react';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CarOrderInList } from '../../store/types';
import './ImporterTransferList.css';

export interface ImporterTransferMultiContainerProps {
  orders: CarOrderInList[];
  heading: string;
  error?: string;
  dataE2e: string;
}

const ImporterTransferList: React.FC<ImporterTransferMultiContainerProps> = (
  props: ImporterTransferMultiContainerProps,
) => {
  const { t } = useTranslation();
  const [showAllOrders, setShowAllOrders] = useState<boolean>(false);
  const [isCopyBannerOpen, setIsCopyBannerOpen] = useState(false);
  const accordionBtnRef: React.Ref<HTMLElement> = useRef(null);

  //used to make accordion button show label and icon on the right
  const style = document.createElement('style');
  style.innerHTML = 'button { justify-content: flex-end !important; }';

  useEffect(() => {
    accordionBtnRef.current?.shadowRoot?.appendChild(style);
  }, [style]);

  const copyOrderIds = () => {
    navigator.clipboard.writeText(props.orders.map((order) => order.pk_new_car_order_id).join(', '));
    setIsCopyBannerOpen(true);
  };

  const MAX_DEFAULT_VISIBLE_ROWS = 5;

  //split orders array in half because UI/UX bullshit
  const halfPoint = Math.ceil(props.orders.length / 2);
  let ordersFirstRow = props.orders.slice(0, halfPoint);
  let ordersSecondRow = props.orders.slice(halfPoint);

  //cut off after MAX if accordion is collapsed
  if (!showAllOrders) {
    ordersFirstRow = ordersFirstRow.slice(0, MAX_DEFAULT_VISIBLE_ROWS);
    ordersSecondRow = ordersSecondRow.slice(0, MAX_DEFAULT_VISIBLE_ROWS);
  }

  return props.orders.length > 0 ? (
    <div data-e2e={props.dataE2e} className="importer-transfer-list-container">
      <PText size="x-small" weight={'semi-bold'}>
        {props.heading}
      </PText>
      {props.error && (
        <PText size="x-small" weight={'semi-bold'} color="notification-error">
          {props.error}
        </PText>
      )}
      <PButtonPure
        size="x-small"
        alignLabel="start"
        icon="copy"
        onClick={() => {
          copyOrderIds();
        }}
      >
        {t('copy_ids')}
      </PButtonPure>
      <PBanner
        open={isCopyBannerOpen}
        dismissButton={false}
        description={t('copy_ids_success')}
        state="info"
        onTransitionEnd={(): void => {
          setTimeout(() => {
            setIsCopyBannerOpen(false);
          }, 1000);
        }}
      />
      <PText color="contrast-medium" size={'x-small'} weight={'regular'} style={{ paddingTop: '15px' }}>
        {t('pk_new_car_order_id')}
      </PText>
      <div className="col-container">
        <div className="field-col">
          {ordersFirstRow.map((order, i) => (
            <PText size={'x-small'} weight={'bold'} key={i}>
              {order.pk_new_car_order_id}
            </PText>
          ))}
        </div>
        <div className="field-col">
          {ordersSecondRow.map((order, i) => (
            <PText size={'x-small'} weight={'bold'} key={i}>
              {order.pk_new_car_order_id}
            </PText>
          ))}
        </div>
      </div>
      {halfPoint > MAX_DEFAULT_VISIBLE_ROWS && (
        <PButtonPure
          ref={accordionBtnRef}
          className="accordion-btn"
          size="x-small"
          alignLabel="start"
          icon={`arrow-head-${showAllOrders ? 'up' : 'down'}`}
          onClick={() => {
            setShowAllOrders(!showAllOrders);
          }}
        >
          {t(showAllOrders ? 'show_less' : 'show_all')}{' '}
        </PButtonPure>
      )}
      <PDivider style={{ paddingTop: '10px', paddingBottom: '10px' }}></PDivider>
    </div>
  ) : (
    <div></div>
  );
};

export default ImporterTransferList;
