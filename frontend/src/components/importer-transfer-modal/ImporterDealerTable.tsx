import { PSelectWrapper } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthContext } from '../../app/AuthContext';
import { useGetAuthorizedDealersQuery } from '../../store/api/BossApi';
import { AuthorizedDealer, AuthorizedImporter, CarOrderInList } from '../../store/types';

interface ImporterDealerFormProps {
  dealer_number: string;
  importer_number: string;
  dealer_name?: string;
  importer_name?: string;
  model_type: string;
  model_year: string;
  errorInput: string | undefined;
  isMulti: boolean;
  setError: (message: string | undefined) => void;
  setDealerNumberNew: (message: string) => void;
  setImporterNumberNew: (message: string | undefined) => void;
  setDealerNameNew: (message: string) => void;
  setImporterNameNew: (message: string | undefined) => void;
  dealerNumberNew: string | undefined;
  importerNew: string | undefined;
  orders: CarOrderInList[];
}

export const ImporterDealer: React.FC<ImporterDealerFormProps> = ({
  dealer_number,
  importer_number,
  dealer_name,
  importer_name,
  model_year,
  errorInput,
  isMulti,
  setDealerNumberNew,
  setImporterNumberNew,
  setDealerNameNew,
  setImporterNameNew,
  dealerNumberNew,
  importerNew,
  orders,
}) => {
  const { t } = useTranslation();
  const { data: dealers } = useGetAuthorizedDealersQuery(undefined);
  const [dealersFiltered, setDealersFiltered] = useState<AuthorizedDealer[]>([]);
  const [importersFiltered, setImportersFiltered] = useState<AuthorizedImporter[]>([]);
  const authContext = useAuthContext();
  useEffect(() => {
    if (isMulti) {
      const importerNumbersFromOrders = new Set(
        orders.map((order) => order.importer_number).filter((num): num is string => !!num),
      );
      const filteredList = authContext.importers.filter(
        (importer) => importer.importer_number && !importerNumbersFromOrders.has(importer.importer_number),
      );
      setImportersFiltered(filteredList);
    } else {
      const importerList = authContext.importers
        .filter((dealer) => dealer.importer_number && dealer.importer_number !== importer_number)
        .map((dealer) => ({
          ...dealer,
        }));
      setImportersFiltered(importerList);
    }
  }, [isMulti]);
  useEffect(() => {
    if (importerNew) {
      const dealerList = authContext.dealers
        .filter((dealer) => dealer.importer_number === importerNew && dealer.dealer_ppn_status === 'OPERATIVE')
        .map((dealer) => ({
          ...dealer,
        }));
      setDealersFiltered(dealerList);
    }
  }, [importerNew]);
  return (
    <>
      <div className="form-container1" style={{ paddingBottom: '20px' }}>
        {!isMulti ? (
          <div className="field-row1">
            <div className="field-col1">
              <PSelectWrapper label={t('Current Importer')}>
                <select disabled={true}>
                  <option value={importer_number}>{`${importer_number} - ${importer_name}`}</option>
                </select>
              </PSelectWrapper>
              <PSelectWrapper label={t('importer_transfer_current_dealer')}>
                <select disabled={true}>
                  <option value={dealer_number}>{`${dealer_number} - ${dealer_name}`}</option>
                </select>
              </PSelectWrapper>
            </div>
            <div className="field-col1">
              <PSelectWrapper data-e2e="SelectImporter" label={t('importer_transfer_select_importer')} filter={true}>
                <select
                  value={importerNew}
                  onChange={(e) => {
                    const selectedImporterNumber = e.target.value;
                    setImporterNumberNew(selectedImporterNumber);
                    const selectedImporter = importersFiltered.find(
                      (importer) => importer.importer_number === selectedImporterNumber,
                    );
                    setImporterNameNew(selectedImporter?.display_name || '');
                    setDealerNumberNew('');
                  }}
                >
                  <option value="">{t('importer_transfer_select_importer')}</option>
                  {importersFiltered.map((importer, index) => (
                    <option key={index} value={importer.importer_number}>
                      {`${importer.importer_number} - ${importer.display_name}`}
                    </option>
                  ))}
                </select>
              </PSelectWrapper>

              <PSelectWrapper data-e2e="SelectDealer" label={t('importer_transfer_select_dealer')} filter={true}>
                <select
                  value={dealerNumberNew}
                  onChange={(e) => {
                    const selectedDealerNumber = e.target.value;
                    setDealerNumberNew(selectedDealerNumber);
                    const selectedDealer = dealersFiltered.find(
                      (dealer) => dealer.dealer_number === selectedDealerNumber,
                    );
                    setDealerNameNew(selectedDealer?.display_name || '');
                  }}
                >
                  <option value="">{t('importer_transfer_select_dealer')}</option>
                  {dealersFiltered.map((dealer, index) => (
                    <option key={index} value={dealer.dealer_number}>
                      {`${dealer.dealer_number} - ${dealer.display_name}`}
                    </option>
                  ))}
                </select>
              </PSelectWrapper>
            </div>
          </div>
        ) : (
          <div className="field-row1">
            <div className="field-col1">
              <PSelectWrapper data-e2e="SelectImporter" label={t('importer_transfer_select_importer')} filter={true}>
                <select
                  value={importerNew}
                  onChange={(e) => {
                    setImporterNumberNew(e.target.value);
                    setDealerNumberNew('');
                  }}
                >
                  <option value="">{t('importer_transfer_select_importer')}</option>
                  {importersFiltered.map((importer, index) => (
                    <option key={index} value={importer.importer_number}>
                      {`${importer.importer_number} - ${importer.display_name}`}
                    </option>
                  ))}
                </select>
              </PSelectWrapper>
            </div>
            <div className="field-col1">
              <PSelectWrapper data-e2e="SelectDealer" label={t('importer_transfer_select_dealer')} filter={true}>
                <select value={dealerNumberNew} onChange={(e) => setDealerNumberNew(e.target.value)}>
                  <option value="">{t('importer_transfer_select_dealer')}</option>
                  {dealersFiltered.map((dealer, index) => (
                    <option key={index} value={dealer.dealer_number}>
                      {`${dealer.dealer_number} - ${dealer.display_name}`}
                    </option>
                  ))}
                </select>
              </PSelectWrapper>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
