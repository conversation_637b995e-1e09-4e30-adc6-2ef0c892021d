import {
  <PERSON><PERSON><PERSON><PERSON>,
  PButtonGroup,
  PH<PERSON>ing,
  PIcon,
  PInlineNotification,
  PModal,
  PSpinner,
} from '@porsche-design-system/components-react';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraNCOCopyApiRequest } from '../../../../infrastructure/lib/types/new-car-order-types';
import { CoraQuota } from '../../../../infrastructure/lib/types/quota-api-types';
import { useCopyOrdersMutation } from '../../store/api/NewCarOrderApi';
import { CopyResultFailed, ModalState } from '../../store/types';
import { AuthorizedDealerForm } from '../authorized-dealer/AuthorizedDealerForm';
import OrderActionSingleDetails from '../shared/order-action-single-details/OrderActionSingleDetails';
import CopyOrdersList from './CopyOrdersList';
import './CopyOrdersModal.css';
import { QuotaTable } from './QuotaTable';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';

const CopyOrdersModal: React.FC<CommonOrderActionProps> = (props: CommonOrderActionProps) => {
  const { t } = useTranslation();
  const [orderToCopy] = useState(props.orders[0]);
  const [modalState, setModalState] = useState<ModalState>('preview');
  const [isModified, setIsModified] = useState(false);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [errMessageForm, setErrMessageForm] = useState<string | undefined>(undefined);
  const [dealer, setDealer] = useState<string>(orderToCopy.dealer_number);
  const [dealerUpdated, setDealerUpdated] = useState<boolean>(false);
  const [modifiedQuotas, setModifiedQuotas] = useState<CoraNCOCopyApiRequest[]>([]);
  const [nextSixMonthsQuotas, setNextSixMonthsQuotas] = useState<CoraQuota[]>([]);
  const [
    triggerCopyOrders,
    {
      data: orderCopyResult,
      isLoading: isOrdersCopyingLoading,
      isError: isOrdersCopyingError,
      error: orderCopyingError,
      isSuccess: isOrdersCopyingSuccess,
    },
  ] = useCopyOrdersMutation();
  const copiedOrders: string[] = [];
  const failedCopyOrders: CopyResultFailed[] = [];
  if (orderCopyResult) {
    orderCopyResult.forEach((result) => {
      if (result.quota_failed) {
        failedCopyOrders.push({
          quota_month: result.quota_month,
          quota_failed: result.quota_failed.toString(),
        });
      }

      if (result.new_car_order_ids && result.new_car_order_ids.length > 0) {
        copiedOrders.push(...result.new_car_order_ids);
      }
    });
  }

  const wasPartialFail = failedCopyOrders.length > 0;
  const [totalQuota, setTotalQuota] = useState(0);
  const [totalConsumedQuota, setTotalConsumedQuota] = useState(0);
  useEffect(() => {
    if (isOrdersCopyingSuccess || isOrdersCopyingError) {
      const totalQuotaConsumed = orderCopyResult?.reduce((total, quota) => {
        return total + quota.quota_consumed;
      }, 0);
      setTotalConsumedQuota(totalQuotaConsumed ? totalQuotaConsumed : 0);
      setModalState('result');
    }
  }, [isOrdersCopyingSuccess, isOrdersCopyingError]);

  function handleSubmit(): void {
    const resultObject: CoraNCOCopyApiRequest = modifiedQuotas.reduce((acc, item) => {
      return { ...acc, ...item };
    }, {} as CoraNCOCopyApiRequest);
    const totalQuotaAmount = Object.values(resultObject).reduce((sum, value) => sum + value, 0);
    setTotalQuota(totalQuotaAmount);
    triggerCopyOrders({
      car_order_id: orderToCopy.pk_new_car_order_id,
      newSelectedDealer: dealerUpdated ? dealer : undefined, // Optional, if needed
      quotas: resultObject,
    });
    setDealerUpdated(false);
    setDealer('');
    setErrMessage(undefined);
    setNextSixMonthsQuotas([]);
    setModifiedQuotas([]);
  }
  useEffect(() => {
    const resultObject: CoraNCOCopyApiRequest = modifiedQuotas.reduce((acc, item) => {
      return { ...acc, ...item };
    }, {} as CoraNCOCopyApiRequest);
    const totalQuotaAmount = Object.values(resultObject).reduce((sum, value) => sum + value, 0);
    if (totalQuotaAmount === 0) {
      setIsModified(false);
    }
  }, [modifiedQuotas]);

  function handleCancel(): void {
    setErrMessage(undefined);
    setNextSixMonthsQuotas([]);
    setModifiedQuotas([]);
    setDealerUpdated(false);
    setDealer('');
    props.closeModal(modalState);
  }
  useEffect(() => {
    if (dealer !== orderToCopy.dealer_number) setDealerUpdated(true);
  }, [dealer]);
  return (
    <PModal open dismissButton={false} onDismiss={handleCancel}>
      <div slot="header" style={{ minWidth: '500px', paddingBottom: '10px' }}>
        {modalState === 'preview' && (
          <>
            {errMessageForm && (
              <PInlineNotification state="error" dismissButton={false}>
                {errMessageForm}
              </PInlineNotification>
            )}
            <PHeading data-e2e="copy_order_modal" size="large">
              {t('copy_order_modal')}
            </PHeading>
          </>
        )}
        {modalState === 'result' && !wasPartialFail && !isOrdersCopyingError && (
          <div className="header-success-container">
            <PIcon
              data-e2e="copy_order_success_icon"
              size="medium"
              name="success-filled"
              color="notification-success"
            ></PIcon>
            <PHeading data-e2e="copy_order_head" size="large">
              {t('copy_order_head', { successes: totalConsumedQuota, total: totalQuota })}
            </PHeading>
          </div>
        )}
        {modalState === 'result' && wasPartialFail && !isOrdersCopyingError && (
          <div className="header-error-container">
            <PInlineNotification data-e2e="copy_order_fail_noti" state="error" dismissButton={false}>
              {t('copy_order_fail_noti')}
            </PInlineNotification>
            <PHeading data-e2e="copy_order_fail_head" style={{ paddingTop: '15px' }} size="large">
              {t('copy_order_fail_head', { successes: totalConsumedQuota, total: totalQuota })}
            </PHeading>
          </div>
        )}
        {modalState === 'result' && isOrdersCopyingError && (
          <div className="header-error-container">
            <PInlineNotification state="error" dismissButton={false}>
              {t('copy_orders_error')}
            </PInlineNotification>

            <PHeading style={{ paddingTop: '15px' }} size="large">
              {t('copy_orders_error')}
            </PHeading>
          </div>
        )}
      </div>
      {errMessage && <PInlineNotification state="error" description={errMessageForm} dismissButton={false} />}
      {copiedOrders.length > 0 || wasPartialFail ? (
        <CopyOrdersList
          orders={orderCopyResult}
          heading={t('copied_orders')}
          type={true}
          totalQuota={totalQuota}
          totalQuotaConsumed={totalConsumedQuota}
        ></CopyOrdersList>
      ) : (
        <OrderActionSingleDetails order={orderToCopy} modelTypeTexts={props.modelTypeTexts}></OrderActionSingleDetails>
      )}
      {isOrdersCopyingLoading && <PSpinner className="centered-foreground-spinner" size="large" />}

      {modalState === 'result' && wasPartialFail && (
        <CopyOrdersList
          orders={orderCopyResult}
          heading={t('failed_copy_orders')}
          error={t('failed_copy_orders_error')}
          type={false}
          totalQuota={totalQuota}
          totalQuotaConsumed={totalConsumedQuota}
        ></CopyOrdersList>
      )}

      {modalState === 'preview' && !isOrdersCopyingLoading && (
        <>
          <div className="copy-form-container">
            <QuotaTable
              selectedQuotaMonth={orderToCopy.quota_month}
              dealer_number={dealer}
              importer_number={orderToCopy.importer_number}
              model_type={orderToCopy.model_type}
              model_year={orderToCopy.model_year}
              setError={setErrMessageForm}
              setIsModified={setIsModified}
              setModifiedQuotas={setModifiedQuotas}
              modifiedQuotas={modifiedQuotas}
              nextSixMonthsQuotas={nextSixMonthsQuotas}
              setNextSixMonthsQuotas={setNextSixMonthsQuotas}
              errorInput={errMessageForm}
            />
          </div>
          <AuthorizedDealerForm
            selectedAuthorizedDealer={dealer}
            handleSelect={setDealer}
            currentDealerNumber={orderToCopy.dealer_number}
            enableImporterFilter={true}
          ></AuthorizedDealerForm>
        </>
      )}

      {modalState === 'preview' ? (
        <PButtonGroup slot="footer">
          {!errMessage && (
            <PButton
              data-e2e="accept"
              variant="primary"
              disabled={isOrdersCopyingLoading || totalQuota > 100 || !isModified || errMessageForm !== undefined}
              title={t('copy_order_yes')}
              aria-label={t('copy_order_yes')}
              onClick={(): void => handleSubmit()}
            >
              {t('copy_order_yes')}
            </PButton>
          )}
          <PButton
            variant="tertiary"
            title={t('copy_order_no')}
            aria-label={t('copy_order_no')}
            onClick={handleCancel}
            disabled={isOrdersCopyingLoading}
          >
            {t('copy_order_no')}
          </PButton>
        </PButtonGroup>
      ) : (
        <PButton
          data-e2e="close"
          variant="primary"
          title={t('copy_order_finish')}
          aria-label={t('copy_order_finish')}
          onClick={handleCancel}
        >
          {t('copy_order_finish')}
        </PButton>
      )}
    </PModal>
  );
};

export default CopyOrdersModal;
