import React from 'react';
import { useMoveToDealerInventoryMutation, useRemoveFromDealerInventoryMutation } from '../../store/api/NewCarOrderApi';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import {
  NcoIdWithModifiedAt,
  DefaultApiRequest,
  OneVmsEventKey,
} from '../../../../infrastructure/lib/types/process-steering-types';
import ProcessSteeringOrderActionModal from '../shared/process-steering-components/order-action-common-modal/ProcessSteeringOrderActionModal';

const HandleDealerInventoryActionModal: React.FC<CommonOrderActionProps> = (props: CommonOrderActionProps) => {
  const [handleDealerInventoryOrders, actionResult] =
    props.actionType === OneVmsEventKey.MOVE_TO_INVENTORY
      ? useMoveToDealerInventoryMutation()
      : useRemoveFromDealerInventoryMutation();

  function handleSubmit(): void {
    const nco_ids_with_modified_at: NcoIdWithModifiedAt[] = props.orders.map((order) => ({
      pk_new_car_order_id: order.pk_new_car_order_id,
      modified_at: order.modified_at!,
    }));
    const apiRequest: DefaultApiRequest = {
      nco_ids_with_modified_at,
    };
    handleDealerInventoryOrders({
      data: apiRequest,
    });
  }

  return (
    <ProcessSteeringOrderActionModal
      isLoading={actionResult.isLoading}
      isError={actionResult.isError}
      isSuccess={actionResult.isSuccess}
      orders={props.orders}
      error={actionResult.error}
      result={actionResult.data}
      actionType={props.actionType}
      closeModal={props.closeModal}
      handleSubmit={handleSubmit}
      isSubmitDisabled={actionResult.isLoading}
    />
  );
};

export default HandleDealerInventoryActionModal;
