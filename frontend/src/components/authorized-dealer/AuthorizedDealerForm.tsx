import { <PERSON><PERSON>ct<PERSON><PERSON><PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { FetchError } from '../../pages/errors/FetchErrors';
import { useGetAuthorizedDealersQuery } from '../../store/api/BossApi';
import { useGetImporterForDealerQuery } from '../../store/api/MasterdataApi';

interface AuthorizedDealerFormProps {
  selectedAuthorizedDealer?: string;
  handleSelect: (element: string) => void;
  currentDealerNumber?: string;
  enableImporterFilter?: boolean;
}
export const AuthorizedDealerForm: React.FC<AuthorizedDealerFormProps> = ({
  selectedAuthorizedDealer,
  handleSelect,
  currentDealerNumber,
  enableImporterFilter = false,
}) => {
  const area = 'authorized_dealer' as const;
  const { t } = useTranslation();

  const { data: dealers, error, isLoading } = useGetAuthorizedDealersQuery(undefined);
  const { data: currentDealerRelationData, isLoading: currentDealerLoading } = useGetImporterForDealerQuery(
    currentDealerNumber ?? '',
    { skip: !currentDealerNumber },
  );

  if (isLoading || currentDealerLoading) {
    return <PSpinner />;
  }

  if (!dealers) {
    return <FetchError custom_error={error} error_area={area} />;
  }
  // Extract importer number of current dealer and filter selection according to importer number
  const filteredDealers = enableImporterFilter
    ? dealers.filter((dealer) => dealer.importer_number === currentDealerRelationData?.importer.pk_importer_number)
    : dealers;

  return (
    <PSelectWrapper
      data-e2e="authorized_dealer"
      style={{
        flexBasis: 'calc(50% - 10px)',
        maxWidth: '300px',
      }}
      label={t('authorized_dealer')}
      dropdownDirection="up"
      filter={true}
    >
      <select
        required
        value={selectedAuthorizedDealer ?? ''}
        onChange={(e) => {
          const selectedElement = filteredDealers.find((m) => m.dealer_number === e.target.value);
          if (selectedElement) {
            handleSelect(selectedElement.dealer_number);
          }
        }}
        style={{
          width: '100%',
          fontSize: '14px',
          padding: '2px 8px',
          height: '15px',
          borderRadius: '4px',
          boxSizing: 'border-box',
        }}
      >
        {/* <option>{t(`${area}_selection`)}</option> */}
        {filteredDealers.map((element) => (
          <option key={element.dealer_number} value={element.dealer_number}>
            {`${element.dealer_number} - ${element.display_name}`}
          </option>
        ))}
      </select>
    </PSelectWrapper>
  );
};
