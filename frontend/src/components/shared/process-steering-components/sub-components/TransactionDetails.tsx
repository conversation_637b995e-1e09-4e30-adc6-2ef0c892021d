import React from 'react';
import { P<PERSON><PERSON>ing, PTag, PText, PButtonPure, PBanner } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { OrderRows } from './OrderRows';
import { CarOrderInList } from '../../../../store/types';
import { InboundApiEventResponse } from '../../../../../../infrastructure/lib/types/process-steering-types';

interface TransactionDetailsProps {
  result: InboundApiEventResponse;
  orders?: CarOrderInList[];
  isMulti: boolean;
  isCopyBannerOpen: boolean;
  visibleRows: string[][];
  orderRows: string[][];
  handleCopyTxId: () => void;
  handleCopyOrderIds: () => void;
  handleToggleOrders: () => void;
  handleTransitionEnd: () => void;
}

export const TransactionDetails: React.FC<TransactionDetailsProps> = ({
  result,
  orders,
  isMulti,
  isCopyBannerOpen,
  visibleRows,
  orderRows,
  handleCopyTxId,
  handleCopyOrderIds,
  handleToggleOrders,
  handleTransitionEnd,
}) => {
  const { t } = useTranslation();
  return (
    <div
      data-e2e="transaction-details"
      style={{
        width: '92%',
        padding: '1.5rem',
        paddingBottom: '1rem',
        border: '1px solid var(--p-chroma-gray-500)',
        borderRadius: '8px',
        boxShadow: '0 4px 4px 2px rgba(0,0,0,0.1)',
      }}
    >
      <PHeading size="small">{t('transaction_details')}</PHeading>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', paddingTop: '0.5rem' }}>
        <PText color="contrast-medium" size="x-small" weight="regular">
          {t('transaction_id')}:
        </PText>
        <PTag>{result.data.transaction_id}</PTag>
        <PButtonPure data-e2e="copy_transaction_id" size="x-small" icon="copy" onClick={handleCopyTxId} />
      </div>
      {orders && orders.length > 0 && (
        <>
          <PBanner
            open={isCopyBannerOpen}
            dismissButton={false}
            description={t('copy_to_clipboard')}
            state="info"
            onTransitionEnd={handleTransitionEnd}
          />
          {!isMulti ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', paddingTop: '0.5rem' }}>
              <PText color="contrast-medium" size="x-small" weight="regular">
                {t('affected_nco_id')}:
              </PText>
              <PTag>{orders[0].pk_new_car_order_id}</PTag>
              <PButtonPure data-e2e="copy_id" size="x-small" icon="copy" onClick={handleCopyOrderIds} />
            </div>
          ) : (
            <div style={{ paddingTop: '0.5rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <PText color="contrast-medium" size="x-small" weight="regular">
                  {t('affected_nco_ids')}:
                </PText>
                <PButtonPure data-e2e="copy_ids" size="x-small" icon="copy" onClick={handleCopyOrderIds} />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', marginTop: '0.5rem' }}>
                <OrderRows rows={visibleRows} />
              </div>
              {orderRows.length > 1 && (
                <div style={{ marginTop: '0.5rem' }}>
                  <PButtonPure
                    data-e2e="toggle_orders"
                    size="x-small"
                    icon={`arrow-head-${orderRows.length > 1 ? (visibleRows.length > 1 ? 'up' : 'down') : 'down'}`}
                    onClick={handleToggleOrders}
                  >
                    {t(visibleRows.length > 1 ? 'show_less' : 'show_all')}
                  </PButtonPure>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};
