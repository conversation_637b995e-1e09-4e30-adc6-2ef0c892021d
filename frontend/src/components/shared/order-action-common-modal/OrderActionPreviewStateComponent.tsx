import React, { useEffect, useState } from 'react';
import { CarOrderInList, OrderActionType } from '../../../store/types';
import { useTranslation } from 'react-i18next';
import { PButton, PHeading, PInlineNotification, PSpinner } from '@porsche-design-system/components-react';
import OrderActionMultiList from '../order-action-multi-lists/OrderActionMultiList';
import OrderActionSingleDetails from '../order-action-single-details/OrderActionSingleDetails';
import { t } from 'i18next';
import { OneVmsEventKey } from '../../../../../infrastructure/lib/types/process-steering-types';

interface PreviewButtonsProps {
  handleSubmit: () => void;
  closeModal: () => void;
  isLoading: boolean;
  isSubmitDisabled: boolean;
  actionType: OrderActionType | OneVmsEventKey;
}

export const PreviewButtons: React.FC<PreviewButtonsProps> = (props: PreviewButtonsProps) => {
  return (
    <div className="order-footer-btn-container">
      <PButton data-e2e="accept" variant="primary" disabled={props.isSubmitDisabled} onClick={props.handleSubmit}>
        {t(`${props.actionType}_order_yes`)}
      </PButton>
      <PButton data-e2e="cancel" variant="tertiary" disabled={props.isLoading} onClick={props.closeModal}>
        {t('submit_order_no')}
      </PButton>
    </div>
  );
};

export interface OrderActionPreviewStateProps {
  ordersToHandle: CarOrderInList[];
  isMulti: boolean;
  handleSubmit: () => void;
  closeModal: () => void;
  CustomComponent?: React.FC;
  isLoading: boolean;
  actionType: OrderActionType | OneVmsEventKey;
  isSubmitDisabled?: boolean;
  subFormError?: string;
}

export const OrderActionPreviewStateComponent: React.FC<OrderActionPreviewStateProps> = (
  props: OrderActionPreviewStateProps,
) => {
  const { t } = useTranslation();
  const [isDisabled, setIsDisabled] = useState(true);
  const modalTitle = t(`${props.actionType}_${props.isMulti ? 'orders' : 'order'}_modal`);

  useEffect(() => {
    if (props.isSubmitDisabled !== undefined) {
      setIsDisabled(props.isSubmitDisabled);
    } else {
      setIsDisabled(props.isLoading);
    }
  }, [props.isSubmitDisabled, props.isLoading]);

  const renderContent = () => {
    if (props.subFormError) {
      return (
        <PInlineNotification
          data-e2e="validation_error_notification"
          style={{ marginBottom: '20px' }}
          state="error"
          dismissButton={false}
        >
          {props.subFormError}
        </PInlineNotification>
      );
    }

    if (props.isMulti) {
      return (
        <OrderActionMultiList
          dataE2e={`${props.actionType}_multi_details`}
          orders={props.ordersToHandle}
          heading={t('selected_orders')}
        />
      );
    } else {
      return <OrderActionSingleDetails order={props.ordersToHandle[0]} />;
    }
  };

  return (
    <>
      <PHeading size="large">{modalTitle}</PHeading>
      {renderContent()}
      {props.CustomComponent && <props.CustomComponent />}
      {props.isLoading && <PSpinner className="centered-foreground-spinner" size="large" />}
      <PreviewButtons
        handleSubmit={props.handleSubmit}
        closeModal={props.closeModal}
        isLoading={props.isLoading}
        isSubmitDisabled={isDisabled}
        actionType={props.actionType}
      />
    </>
  );
};
