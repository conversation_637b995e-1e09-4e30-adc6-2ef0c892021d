import { PModal } from '@porsche-design-system/components-react';
import './OrderActionCommonModal.css';
import { CarOrderInList, ModalState, OrderActionType, OrderCancelReason } from '../../../store/types';
import {
  OrderActionResult,
  OrderActionStatusCodes,
} from '../../../../../infrastructure/lambda/backend/new-car-order/shared-order-action-status-result/types';
import './OrderActionCommonModal.css';
import { OrderActionPreviewStateComponent } from './OrderActionPreviewStateComponent';
import { OrderActionResultStateComponent } from './OrderActionResultStateComponent';
import { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query';
import { SerializedError } from '@reduxjs/toolkit';
import { OneVmsEventKey } from '../../../../../infrastructure/lib/types/process-steering-types';
import { ModelTypeTextModel } from '../../../../../infrastructure/lib/entities/model-type-text-model';
import { useEffect, useState } from 'react';

export interface CommonOrderActionProps {
  orders: CarOrderInList[];
  modelTypeTexts?: ModelTypeTextModel[];
  closeModal: (state: ModalState) => void;
  actionType: OrderActionType | OneVmsEventKey;
}

export interface CommonModalProps {
  orders: CarOrderInList[];
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  error?: FetchBaseQueryError | SerializedError;
  orderResult?: OrderActionResult;
  modelTypeTexts?: ModelTypeTextModel[];
  actionType: OrderActionType;
  closeModal: (state: ModalState) => void;
  handleSubmit: () => void;
  CustomComponent?: React.FC;
  isSubmitDisabled?: boolean;
}

const OrderActionCommonModal: React.FC<CommonModalProps> = (props) => {
  const [modalState, setModalState] = useState<ModalState>('preview');
  const [successfulOrders, setSuccessfulOrders] = useState<CarOrderInList[]>([]);
  const [failedOrders, setFailedOrders] = useState<CarOrderInList[]>([]);
  const [racingCondErrorOrders, setRacingCondErrorOrders] = useState<CarOrderInList[]>([]);
  const isMulti = props.orders.length > 1;
  useEffect(() => {
    const tempSuccessfulOrders: CarOrderInList[] = [];
    const tempFailedOrders: CarOrderInList[] = [];
    const tempRacingCondErrorOrders: CarOrderInList[] = [];

    props.orders.forEach((order) => {
      if (props.orderResult?.[order.pk_new_car_order_id] === OrderActionStatusCodes.SUCCESS) {
        tempSuccessfulOrders.push(order);
      } else if (props.orderResult?.[order.pk_new_car_order_id] === OrderActionStatusCodes.RACING_CONDITION) {
        tempRacingCondErrorOrders.push(order);
      } else {
        tempFailedOrders.push(order);
      }
    });

    setSuccessfulOrders(tempSuccessfulOrders);
    setFailedOrders(tempFailedOrders);
    setRacingCondErrorOrders(tempRacingCondErrorOrders);

    if (props.isSuccess || props.isError) {
      setModalState('result');
    }
  }, [props.orders, props.orderResult, props.isSuccess, props.isError]);
  const handleClose = () => props.closeModal(modalState);

  return (
    <PModal
      disableBackdropClick
      data-e2e={`${props.actionType}_order_modal`}
      open
      backdrop="blur"
      dismissButton={false}
      onDismiss={handleClose}
    >
      {modalState === 'preview' ? (
        <OrderActionPreviewStateComponent
          ordersToHandle={props.orders}
          isMulti={isMulti}
          handleSubmit={props.handleSubmit}
          closeModal={handleClose}
          CustomComponent={props.CustomComponent}
          isLoading={props.isLoading}
          actionType={props.actionType}
          isSubmitDisabled={props.isSubmitDisabled}
        />
      ) : (
        <OrderActionResultStateComponent
          successfulOrders={successfulOrders}
          failedOrders={failedOrders}
          racingCondErrorOrders={racingCondErrorOrders}
          isMulti={isMulti}
          closeModal={handleClose}
          error={props.error}
          actionType={props.actionType}
          orderResult={props.orderResult}
        />
      )}
    </PModal>
  );
};

export default OrderActionCommonModal;
