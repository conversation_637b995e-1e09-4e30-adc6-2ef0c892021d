import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../app/hooks';
import i18n from '../../services/i18n';
import { useReportTotalLossMutation, useRevokeTotalLossMutation } from '../../store/api/NewCarOrderApi';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import TotalLossNotificationComponent from './TotalLossNotificationComponent';
import ProcessSteeringOrderActionModal from '../shared/process-steering-components/order-action-common-modal/ProcessSteeringOrderActionModal';
import { OneVmsEventKey } from '../../../../infrastructure/lib/types/process-steering-types';

const TotalLossModal: React.FC<CommonOrderActionProps> = (props) => {
  const { t } = useTranslation();
  const correlationId = useAppSelector((state) => state.correlation.correlationId);

  const [deadlineDate, setDeadlineDate] = useState<string>('');

  // Update the deadline date based on the current language and time
  useEffect(() => {
    const updateDeadlineDate = () => {
      const now = new Date();
      const deadline = new Date(now);
      deadline.setDate(now.getDate() + 3);

      const formattedDeadline = new Intl.DateTimeFormat(i18n.language, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short',
      }).format(deadline);
      setDeadlineDate(formattedDeadline);
    };

    updateDeadlineDate();

    const interval = setInterval(() => {
      updateDeadlineDate();
    }, 60 * 1000);

    return () => clearInterval(interval);
  }, [i18n.language]);

  const [executeTotalLossAction, totalLossActionResult] =
    props.actionType === OneVmsEventKey.TOTAL_LOSS_REPORT ? useReportTotalLossMutation() : useRevokeTotalLossMutation();

  const handleSubmit = () => {
    executeTotalLossAction({
      nco_ids_with_modified_at: props.orders.map((nco) => ({
        pk_new_car_order_id: nco.pk_new_car_order_id,
        modified_at: nco.modified_at ?? '',
      })),
    });
  };

  return (
    <ProcessSteeringOrderActionModal
      orders={props.orders}
      isLoading={totalLossActionResult.isLoading}
      isError={totalLossActionResult.isError}
      isSuccess={totalLossActionResult.isSuccess}
      error={totalLossActionResult.error}
      result={totalLossActionResult.data}
      actionType={props.actionType}
      closeModal={props.closeModal}
      handleSubmit={handleSubmit}
      CustomComponent={() => {
        return (
          <TotalLossNotificationComponent
            actionType={props.actionType}
            orders={props.orders}
            deadlineDate={deadlineDate}
          />
        );
      }}
    />
  );
};

export default TotalLossModal;
