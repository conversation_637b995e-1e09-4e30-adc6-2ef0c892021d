import './UserMenu.css';
import { JSX, useEffect } from 'react';
import { PText, PButtonPure } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { loadUser, selectUser } from '../../store/slices/UserMenuSlice';
import { useAppSelector, useAppDispatch } from '../../app/hooks';
import { getWebDomain } from '../../services/ServiceUtils';
// import { IFrameRefreshToken } from '@kas-resources/react-components';
import { useGetStageConfigQuery } from '../../store/api/StaticJsonApi';
import { loadUserFromToken } from '../../services/UserService';

export function UserMenu(): JSX.Element {
  const { t, i18n } = useTranslation();
  const user = useAppSelector(selectUser);
  const dispatch = useAppDispatch();
  const { data: stageConfig } = useGetStageConfigQuery(undefined);

  const changeLanguage = async () => {
    if (i18n.language === 'en-GB') {
      await i18n.changeLanguage('de-DE');
    } else {
      await i18n.changeLanguage('en-GB');
    }
  };

  const logout = () => {
    window.location.href = `${getWebDomain()}/logout`;
  };

  useEffect(() => {
    if (stageConfig) {
      dispatch(loadUser(loadUserFromToken(stageConfig)));
    }
  }, [stageConfig]);

  return (
    <>
      <div className={'dropdown'}>
        <PButtonPure data-e2e="userMenu" icon="user">
          {user.firstName + ' ' + user.lastName}{' '}
        </PButtonPure>
        <div className={'dropdown-content'}>
          <div>
            <div>
              <PText>{/* <IFrameRefreshToken /> */}</PText>
            </div>
            <div>
              <PButtonPure
                icon="arrow-head-right"
                // eslint-disable-next-line @typescript-eslint/no-misused-promises
                onClick={async (e): Promise<void> => {
                  e.preventDefault();
                  await changeLanguage();
                }}
              >
                {t('language')}
              </PButtonPure>
            </div>
            <div>
              <PButtonPure icon={'logout'} onClick={logout}>
                {t('logout')}
              </PButtonPure>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
