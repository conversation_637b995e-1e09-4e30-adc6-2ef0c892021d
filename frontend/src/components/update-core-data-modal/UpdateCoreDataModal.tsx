import { useState } from 'react';
import { useUpdateNewCarOrderCoreDataMutation } from '../../store/api/NewCarOrderApi';
import { OrderTypeUpdateForm } from './OrderTypeUpdateForm';
import './UpdateCoreDataModal.css';
import { CommonOrderActionProps } from '../shared/order-action-common-modal/OrderActionCommonModal';
import {
  NcoIdWithModifiedAt,
  UpdateNcoCoreApiRequest,
} from '../../../../infrastructure/lib/types/process-steering-types';
import ProcessSteeringOrderActionModal from '../shared/process-steering-components/order-action-common-modal/ProcessSteeringOrderActionModal';

const UpdateCoreDataModal: React.FC<CommonOrderActionProps> = (props) => {
  const [ordersToUpdate] = useState(props.orders);
  const [errorMessage, setErrorMessage] = useState<string>();
  const [orderTypeSelect, setOrderTypeSelect] = useState<string>('');

  const [
    editOrders,
    {
      isLoading: isEditingOrders,
      isError: isOrdersEditError,
      isSuccess: isOrdersEditSuccess,
      error: orderErrorResult,
      data: orderEditResult,
    },
  ] = useUpdateNewCarOrderCoreDataMutation();

  const handleSubmit = (): void => {
    const nco_ids_with_modified_at: NcoIdWithModifiedAt[] = ordersToUpdate.map((order) => ({
      pk_new_car_order_id: order.pk_new_car_order_id,
      modified_at: order.modified_at!,
    }));

    const apiRequest: UpdateNcoCoreApiRequest = {
      nco_ids_with_modified_at: nco_ids_with_modified_at,
      payload: {
        order_type: orderTypeSelect,
      },
    };
    editOrders({ data: apiRequest });
  };

  const UpdateForm: React.FC = () => (
    <div className="form-container1" style={{ marginBottom: '24px' }}>
      <div className="field-row1">
        <div className="field-col1">
          <OrderTypeUpdateForm
            orders={ordersToUpdate}
            dealer_number={ordersToUpdate[0].dealer_number ?? '-'}
            setOrderTypeSelect={setOrderTypeSelect}
            setSubFormError={setErrorMessage}
            orderTypeSelect={orderTypeSelect}
            isUpdating={isEditingOrders}
          />
        </div>
      </div>
    </div>
  );

  return (
    <ProcessSteeringOrderActionModal
      orders={ordersToUpdate}
      isLoading={isEditingOrders}
      isError={isOrdersEditError}
      isSuccess={isOrdersEditSuccess}
      error={orderErrorResult}
      result={orderEditResult}
      actionType={props.actionType}
      closeModal={props.closeModal}
      handleSubmit={handleSubmit}
      CustomComponent={UpdateForm}
      isSubmitDisabled={!orderTypeSelect || errorMessage !== undefined}
      subFormError={errorMessage}
    />
  );
};

export default UpdateCoreDataModal;
