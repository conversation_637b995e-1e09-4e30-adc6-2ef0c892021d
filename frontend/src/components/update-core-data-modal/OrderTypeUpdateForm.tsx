import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraMdOrderTypeResponseItem } from '../../../../infrastructure/lib/types/masterdata-types';
import { FetchError } from '../../pages/errors/FetchErrors';
import { useGetOrderTypesForDealerQuery } from '../../store/api/MasterdataApi';
import { CarOrderInList } from '../../store/types';

interface OrderTypeUpdateFormProps {
  orders: CarOrderInList[];
  dealer_number: string;
  setOrderTypeSelect: (value: string) => void;
  setSubFormError: (value: string) => void;
  orderTypeSelect: string;
  isUpdating: boolean;
}

export const OrderTypeUpdateForm: React.FC<OrderTypeUpdateFormProps> = ({
  orders,
  dealer_number,
  setOrderTypeSelect,
  setSubFormError,
  orderTypeSelect,
  isUpdating,
}) => {
  const { t, i18n } = useTranslation();
  const [isCustomerRelated, setIsCustomerRelated] = useState<boolean>(false);
  const { data: orderTypes, error, isLoading: isLoadingOrderTypes } = useGetOrderTypesForDealerQuery(dealer_number);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [nonCustomerRelatedOrderTypes, setNonCustomerRelatedOrderTypes] = useState<CoraMdOrderTypeResponseItem[]>([]);

  //disable button
  useEffect(() => {
    if (orders && orderTypes)
      orders.forEach((order) => {
        const orderType = orderTypes?.find((oT) => oT.pk_order_type === order.order_type);
        if (orderType?.is_customer_related) {
          setIsCustomerRelated(true);
        }
      });
  }, [orders, orderTypes]);

  useEffect(() => {
    if (orderTypes)
      setNonCustomerRelatedOrderTypes(
        orderTypes?.filter((orderType) => !orderType.is_customer_related && orderType.editable) || [],
      );
  }, [orderTypes]);
  useEffect(() => {
    if (isCustomerRelated) {
      if (orders.length === 1) setSubFormError(t('update_core_data_customer_related_error_single'));
      else setSubFormError(t('update_core_data_customer_related_error_multi'));
    }
  }, [isCustomerRelated]);

  if (isLoadingOrderTypes) {
    return <PSpinner />;
  }

  if (!orderTypes) {
    return <FetchError custom_error={error} error_area="order_type" />;
  }

  return (
    <PSelectWrapper
      data-e2e="SelectOrderType"
      style={{ flexBasis: 'calc(50% - 10px)' }}
      label={t('order_type_prompt')}
      state={errMessage ? 'error' : 'none'}
      message={errMessage}
    >
      <select
        disabled={isCustomerRelated || isUpdating}
        required
        value={orderTypeSelect ?? ''}
        onChange={(e) => {
          const orderType = orderTypes.find((m) => m.pk_order_type === e.target.value);
          if (orderType) {
            setOrderTypeSelect(orderType.pk_order_type);
          }
        }}
      >
        <option>{t('order_type_selection')}</option>
        {nonCustomerRelatedOrderTypes?.map((orderType) => (
          <option key={orderType.pk_order_type} value={orderType.pk_order_type} disabled={!orderType.editable}>
            {`${orderType.pk_order_type} - ${orderType.description}`}
          </option>
        ))}
      </select>
    </PSelectWrapper>
  );
};
