import { CarOrderInList } from '../store/types';
import { InboundProcessMappingModel } from '../../../infrastructure/lib/entities/inbound-mapping-model';
import {
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
} from '../../../infrastructure/lib/types/process-steering-types';

/**
 * Determines which OneVmsEventKeys are allowed for all provided car orders based on their status and inbound mappings
 * @param inboundMappings List of inbound status mappings defining allowed events for status combinations
 * @param orders One or more ncos to check
 * @returns Array of OneVmsEventKeys that are allowed for all provided orders
 */
export const getAllowedEventKeys = (
  inboundMappings: InboundProcessMappingModel[],
  ncos: CarOrderInList | CarOrderInList[],
): OneVmsEventKey[] => {
  // Convert single order to array for consistent handling
  const ncoArray = Array.isArray(ncos) ? ncos : [ncos];

  // Get allowed events for each order based on its status codes
  const allowedEventsByNco = ncoArray.map((nco) => {
    const events = inboundMappings.filter((mapping) => matchMapping(mapping, nco)).map((mapping) => mapping.event);

    // Remove duplicates for each NCO's events
    return [...new Set(events)];
  });

  // Find intersection of allowed events across all orders
  return allowedEventsByNco.reduce((commonEvents, ncoEvents) => {
    if (commonEvents.length === 0) {
      return ncoEvents;
    }
    return commonEvents.filter((event) => ncoEvents.includes(event));
  }, [] as OneVmsEventKey[]);
};

function matchMapping(mapping: InboundProcessMappingModel, nco: CarOrderInList) {
  return (
    mapping.source_system === OneVmsSourceSystemKey.CORA_USER &&
    matchStatusCode(mapping.order_status_code, nco.order_status_onevms_code) &&
    matchStatusCode(mapping.error_status_code, nco.order_status_onevms_error_code) &&
    matchStatusCode(mapping.invoice_status_code, nco.order_invoice_onevms_code)
  );
}

// Shamelessly stolen from infrastructure/lambda/utils/process-steering-helpers.ts
function matchStatusCode(mappingCode: string, actualCode: string): boolean {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
  if (mappingCode === SpecialStatusCode.ALL) {
    return true;
  } else if (mappingCode.endsWith('*')) {
    const prefix = mappingCode.slice(0, -1);
    return actualCode.startsWith(prefix);
  }
  return mappingCode === actualCode;
}

//gets events keys that have inbound mapping with all status set to null = convention for actions without nco reference
export const getAllowedEventKeysWithoutNcos = (inboundMappings: InboundProcessMappingModel[]): OneVmsEventKey[] => {
  const eventKeys = inboundMappings
    .filter(
      (mapping) =>
        mapping.source_system === OneVmsSourceSystemKey.CORA_USER &&
        mapping.order_status_code === SpecialStatusCode.NONE &&
        mapping.error_status_code === SpecialStatusCode.NONE &&
        mapping.invoice_status_code === SpecialStatusCode.NONE,
    )
    .map((mapping) => mapping.event);

  return [...new Set(eventKeys)];
};
