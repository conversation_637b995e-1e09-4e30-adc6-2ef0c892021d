import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../configureStore';
import { KccSaveConfigWindowMessage } from '../../pages/kcc-component/kccUtils';
import { CoraNCOConfiguration } from '../../../../infrastructure/lib/types/new-car-order-types';

export interface KccConfigurationState {
  config?: CoraNCOConfiguration;
  signature_config?: string;
  config_pvms?: unknown;
  signature_config_pvms?: string;
  traceid: string;
  kccFocused: boolean;
  status: 'idle' | 'loading' | 'failed';
}

const initialState: KccConfigurationState = {
  traceid: '',
  kccFocused: true,
  status: 'idle',
};

export const kccConfigSlice = createSlice({
  name: 'kccConfig',
  initialState,
  reducers: {
    saveConfig: (state, action: PayloadAction<KccSaveConfigWindowMessage>) => {
      state.config = action.payload.payload.kas.configuration as CoraNCOConfiguration;
      state.config_pvms = action.payload.payload.pvms;
      state.signature_config = action.payload.payload.signature;
      state.signature_config_pvms = action.payload.payload.signature;
      state.traceid = 'dummytraceid';
    },
    clearConfig: (state, action: PayloadAction<boolean>) => {
      (state.config = undefined),
        (state.config_pvms = undefined),
        (state.signature_config = undefined),
        (state.signature_config_pvms = undefined),
        (state.traceid = '');
    },
    setKccFocused: (state, action: PayloadAction<boolean>) => {
      state.kccFocused = action.payload;
    },
  },
});

export const { saveConfig, setKccFocused, clearConfig } = kccConfigSlice.actions;

const selectConfigOnly = (state: RootState): CoraNCOConfiguration | undefined => state.kccConfig.config;
const selectConfigPvmsOnly = (state: RootState): unknown | undefined => state.kccConfig.config_pvms;
const selectConfigSignatureOnly = (state: RootState): string | undefined => state.kccConfig.signature_config;
const selectConfigPvmsSignatureOnly = (state: RootState): string | undefined => state.kccConfig.signature_config_pvms;
const selectConfigTraceIdOnly = (state: RootState): string => state.kccConfig.traceid;
export const selectKccConfig = createSelector(
  [
    selectConfigOnly,
    selectConfigPvmsOnly,
    selectConfigSignatureOnly,
    selectConfigPvmsSignatureOnly,
    selectConfigTraceIdOnly,
  ],
  (config, config_pvms, signature_config, signature_config_pvms, traceid) => ({
    config,
    config_pvms,
    signature_config,
    signature_config_pvms,
    traceid,
  }),
);
export const selectKccFocused = (state: RootState): boolean => state.kccConfig.kccFocused;

export default kccConfigSlice.reducer;
