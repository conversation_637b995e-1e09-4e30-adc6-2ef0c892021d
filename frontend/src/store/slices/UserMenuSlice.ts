import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../configureStore';

export interface ActiveUserRoles {
  hasCreateOrder: boolean;
  hasChangeOrder: boolean;
  hasCancelOrder: boolean;
}

export interface User {
  username: string; //'xyzxyz';
  firstName: string; //'Max';
  lastName: string; //'Mustermann';
  porschePartnerNo: string; //'9500090';
  expires: number;
}

export interface UserState {
  user: User;
  status: 'idle' | 'loading' | 'failed';
}

const initialState: UserState = {
  user: {
    username: '',
    firstName: '',
    lastName: '',
    porschePartnerNo: '',
    expires: 0,
  },
  status: 'idle',
};

export const userMenuSlice = createSlice({
  name: 'userMenu',
  initialState,
  reducers: {
    loadUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },
  },
});

export const { loadUser } = userMenuSlice.actions;
export const selectUser = (state: RootState): User => state.user.user;
export const selectUserStatus = (state: RootState): string => state.user.status;

export default userMenuSlice.reducer;
