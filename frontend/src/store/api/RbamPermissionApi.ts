import { baseApi } from './BaseApi';

const apiPrefix = 'rbam-permissions';
const permissionsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPermissionsForUser: builder.query<
      { data: { app_permissions: string[]; applicationName: string; applicationRole: string }[] },
      undefined
    >({
      query: () => ({ url: `${apiPrefix}` }),
    }),
  }),
});

export const { useGetPermissionsForUserQuery } = permissionsApi;
