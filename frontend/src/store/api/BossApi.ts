import { AuthorizedDealer } from '../../../../infrastructure/lib/types/boss-org-types';
import { ModelTypeVisibilityModel } from '../../../../infrastructure/lib/entities/model-type-visibility-model';
import { ModelTypeTextModel } from '../../../../infrastructure/lib/entities/model-type-text-model';
import { baseApi } from './BaseApi';

const apiPrefix = 'boss';
const bossApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAuthorizedDealers: builder.query<AuthorizedDealer[], undefined>({
      query: () => ({ url: `${apiPrefix}/authorized-dealer` }),
    }),
    getModelTypes: builder.query<ModelTypeVisibilityModel[], string>({
      query: (importer_number) => ({ url: `${apiPrefix}/model-type`, params: { importer_number: importer_number } }),
    }),
    getModelTypeTexts: builder.query<ModelTypeTextModel[], string>({
      query: (isoLanguageCode) => ({ url: `${apiPrefix}/model-type/text/${isoLanguageCode}` }),
    }),
  }),
});

export const { useGetAuthorizedDealersQuery, useGetModelTypesQuery, useLazyGetModelTypeTextsQuery } = bossApi;
