import {
  CoraNCOBuySellTransferApiRequest,
  CoraNCOOBuySellTransferApiResponse,
} from '../../../../infrastructure/lib/types/new-car-order-types';
import { baseApi } from './BaseApi';

const apiPrefix = 'special-actions';
const specialActionsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    buySellTransferOrders: builder.mutation<
      CoraNCOOBuySellTransferApiResponse,
      { data: CoraNCOBuySellTransferApiRequest }
    >({
      query: ({ data }) => ({
        url: `${apiPrefix}/buy-sell-transfer`,
        body: data,
        method: 'POST',
      }),
    }),
  }),
});

export const { useBuySellTransferOrdersMutation } = specialActionsApi;
