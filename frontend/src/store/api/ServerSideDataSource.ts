import {
  CoraNCOQueryApiRequestBody,
  CoraNCOQueryApiResponse,
} from '../../../../infrastructure/lib/types/new-car-order-types';
import {
  CoraNCPurchaseIntentionRequestBody,
  CoraPurchaseIntentionQueryApiResponse,
} from '../../../../infrastructure/lib/types/purchase-intention-types';
import { getApiDomain } from '../../services/ServiceUtils';

export const fetchAgGridSsrmNewCarOrder = async (
  body: CoraNCOQueryApiRequestBody,
  correlationId?: string,
): Promise<CoraNCOQueryApiResponse> => {
  return fetchWithHeaders<CoraNCOQueryApiResponse>(
    'lists/new-car-order/get',
    { body: body as unknown as any },
    correlationId,
  );
};
const fetchWithHeaders = async <T>(path: string, options: RequestInit = {}, correlationId?: string): Promise<T> => {
  const headers = new Headers({
    'Content-Type': 'application/json',
    ...options.headers, // Merge with any custom headers
  });

  if (correlationId) {
    headers.set('x-kas-request-id', correlationId);
  }

  const response = await fetch(`${getApiDomain()}/${path}`, {
    ...options,
    headers,
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(options.body),
  });

  if (!response.ok) {
    throw new Error(`${response.status} - ${response.statusText} - ${await response.text()}`);
  }

  return response.json();
};
export const fetchAgGridSsrmPurchaseIntentions = async (
  body: CoraNCPurchaseIntentionRequestBody,
  correlationId?: string,
): Promise<CoraPurchaseIntentionQueryApiResponse> => {
  return fetchWithHeaders<CoraPurchaseIntentionQueryApiResponse>(
    'lists/purchase-intention/get',
    { body: body as unknown as any },
    correlationId,
  );
};
