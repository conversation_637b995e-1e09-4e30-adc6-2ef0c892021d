import { KccProxyInitialParams, getKccCacheHostname } from '../../pages/kcc-component/kccUtils';
import { displayNotification } from '../slices/NotificationSlice';
import { baseApi } from './BaseApi';
import { KccProxyCacheConfigResponse } from '../types';

const kccApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    sendConfigToKcc: builder.mutation<KccProxyCacheConfigResponse, KccProxyInitialParams>({
      query: (body) => ({ url: getKccCacheHostname() + '/', body: body, method: 'POST' }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (err) {
          dispatch(displayNotification({ msg: 'Error fetching post!', state: 'error' }));
        }
      },
    }),
  }),
});

export const { useSendConfigToKccMutation } = kccApi;
