import {
  CoraNCOBaseApiResponse,
  CoraNCOCopyApiRequest,
  CoraNCOCopyApiResponse,
  CoraNCOGetApiResponse,
  CoraNCOIdsApiRequest,
  CoraNCOIdsApiResponse,
  CoraNCOImporterTransferApiRequest,
  CoraNCOImporterTransferApiResponse,
} from '../../../../infrastructure/lib/types/new-car-order-types';
import {
  CancelNcoApiRequest,
  CoraNcoUpdateApiRequest,
  CreateNcoApiRequest,
  CoraNCOTotalLossApiRequest,
  DefaultApiRequest,
  InboundApiEventResponse,
  UpdateNcoCoreApiRequest,
} from '../../../../infrastructure/lib/types/process-steering-types';
import { baseApi } from './BaseApi';
import { createQuotaStoreId } from './QuotaApi';

const apiPrefix = 'new-car-order';
const newCarOrderApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getNewCarOrderById: builder.query<CoraNCOGetApiResponse, string>({
      query: (id) => ({ url: `${apiPrefix}/${id}` }),
      providesTags: (result) => [{ type: 'currentNco', id: result?.pk_new_car_order_id }],
    }),
    createNewCarOrder: builder.mutation<InboundApiEventResponse, CreateNcoApiRequest>({
      query: (order_create_request_obj) => ({
        url: `${apiPrefix}`,
        method: 'POST',
        body: JSON.stringify(order_create_request_obj),
      }),
      invalidatesTags: (_result, _error, arg) => [
        { type: 'ncoList' },
        { type: 'quota', id: createQuotaStoreId(arg.payload) },
      ],
    }),
    editNewCarOrder: builder.mutation<InboundApiEventResponse, CoraNcoUpdateApiRequest>({
      query: (order_edit_request_obj) => ({
        url: `${apiPrefix}/${order_edit_request_obj.nco_ids_with_modified_at[0].pk_new_car_order_id}`,
        method: 'PATCH',
        body: JSON.stringify(order_edit_request_obj),
      }),
      invalidatesTags: (_result, _error, arg) => [
        { type: 'currentNco', id: arg.nco_ids_with_modified_at[0].pk_new_car_order_id },
        { type: 'ncoList' },
        { type: 'quota', id: createQuotaStoreId(arg.payload) },
      ],
    }),
    cancelNewCarOrders: builder.mutation<InboundApiEventResponse, { data: CancelNcoApiRequest }>({
      query: ({ data }) => ({
        url: `${apiPrefix}/cancel`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }, { type: 'quota' }],
    }),
    copyOrders: builder.mutation<
      CoraNCOCopyApiResponse,
      { car_order_id: string; newSelectedDealer?: string; quotas: CoraNCOCopyApiRequest }
    >({
      query: ({ car_order_id, newSelectedDealer, quotas }) => ({
        url: `${apiPrefix}/${car_order_id}/copy`,
        params: newSelectedDealer ? { newSelectedDealer } : undefined,
        body: quotas,
        method: 'POST',
      }),
    }),
    deallocateQuota: builder.mutation<InboundApiEventResponse, { data: DefaultApiRequest }>({
      query: ({ data }) => ({
        url: `${apiPrefix}/deallocate-quota`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }],
    }),
    reportTotalLoss: builder.mutation<InboundApiEventResponse, CoraNCOTotalLossApiRequest>({
      query: (total_loss_request_obj) => ({
        url: `${apiPrefix}/total-loss/report`,
        method: 'POST',
        body: JSON.stringify(total_loss_request_obj),
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }],
    }),
    revokeTotalLoss: builder.mutation<InboundApiEventResponse, CoraNCOTotalLossApiRequest>({
      query: (total_loss_request_obj) => ({
        url: `${apiPrefix}/total-loss/revoke`,
        method: 'POST',
        body: JSON.stringify(total_loss_request_obj),
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }],
    }),
    importerTransferOrders: builder.mutation<
      CoraNCOImporterTransferApiResponse,
      { data: CoraNCOImporterTransferApiRequest }
    >({
      query: ({ data }) => ({
        url: `${apiPrefix}/importer-transfer`,
        body: data,
        method: 'POST',
      }),
    }),
    getNcoIdsList: builder.mutation<CoraNCOIdsApiResponse, CoraNCOIdsApiRequest>({
      query: ({ dealer_number }) => ({
        url: `${apiPrefix}/new-car-order-ids`,
        method: 'GET',
        params: { dealer_number },
      }),
    }),
    moveToDealerInventory: builder.mutation<InboundApiEventResponse, { data: DefaultApiRequest }>({
      query: ({ data }) => ({
        url: `${apiPrefix}/move-to-dealer-inventory`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }],
    }),
    removeFromDealerInventory: builder.mutation<InboundApiEventResponse, { data: DefaultApiRequest }>({
      query: ({ data }) => ({
        url: `${apiPrefix}/remove-from-dealer-inventory`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, arg) => [{ type: 'ncoList' }],
    }),
    updateNewCarOrderCoreData: builder.mutation<InboundApiEventResponse, { data: UpdateNcoCoreApiRequest }>({
      query: ({ data }) => ({
        url: `${apiPrefix}/update-core-data`,
        method: 'PATCH',
        body: data,
      }),
    }),
  }),
});

export const {
  useGetNewCarOrderByIdQuery,
  useCreateNewCarOrderMutation,
  useEditNewCarOrderMutation,
  useCancelNewCarOrdersMutation,
  useCopyOrdersMutation,
  useDeallocateQuotaMutation,
  useReportTotalLossMutation,
  useRevokeTotalLossMutation,
  useImporterTransferOrdersMutation,
  useGetNcoIdsListMutation,
  useMoveToDealerInventoryMutation,
  useRemoveFromDealerInventoryMutation,
  useUpdateNewCarOrderCoreDataMutation,
} = newCarOrderApi;
