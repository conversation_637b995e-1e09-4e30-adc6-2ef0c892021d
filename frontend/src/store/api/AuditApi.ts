import { baseApi } from './BaseApi';
import { NewCarOrderAuditTrailModel } from '../../../../infrastructure/lib/entities/new-car-order-audit-trail-model';

const apiPrefix = 'audit';
const auditApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAuditTrailByNcoId: builder.query<NewCarOrderAuditTrailModel[], string>({
      query: (id) => ({ url: `${apiPrefix}/${id}` }),
      providesTags: (result) => [{ type: 'auditNco', id: result?.[0].pk_new_car_order_id }],
    }),
  }),
});

export const { useGetAuditTrailByNcoIdQuery } = auditApi;
