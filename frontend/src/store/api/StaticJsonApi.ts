import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getWebDomain } from '../../services/ServiceUtils';
import { OrderCancelReason, StageConfig } from '../types';
import { baseApi } from './BaseApi';

const customBaseQuery = fetchBaseQuery({
  baseUrl: `${getWebDomain()}`,
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
});

const staticJsonApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCancelReasons: builder.query<OrderCancelReason[], undefined>({
      queryFn: async (a, baseQueryApi) => {
        const response = await customBaseQuery({ url: 'storno.json' }, baseQueryApi, {});
        if (!response.error) {
          return { data: response.data as OrderCancelReason[] };
        } else {
          console.log('Could not fetch storno reasons, probably because of local test. Mocking data', response.error);
          return {
            data: [
              {
                id: 'A1',
                beschreibung: 'Grund 1: weil er halt nicht will oder kann',
              },
              {
                id: 'A2',
                beschreibung: 'Grund 2: weil er halt nicht will oder kann',
              },
              {
                id: 'A3',
                beschreibung: 'Grund 3: weil er halt nicht will oder kann',
              },
              {
                id: 'A4',
                beschreibung: 'Grund 4: weil er halt nicht will oder kann',
              },
              {
                id: 'A5',
                beschreibung: 'Grund 5: weil er halt nicht will oder kann',
              },
              {
                id: 'A6',
                beschreibung: 'Grund 6: weil er halt nicht will oder kann',
              },
              {
                id: 'A7',
                beschreibung: 'Grund 7: weil er halt nicht will oder kann',
              },
              {
                id: 'A8',
                beschreibung: 'Grund 8: weil er halt nicht will oder kann',
              },
              {
                id: 'A9',
                beschreibung: 'Grund 9: weil er halt nicht will oder kann',
              },
            ],
          };
        }
      },
    }),
    getStageConfig: builder.query<StageConfig, undefined>({
      queryFn: async (a, baseQueryApi) => {
        const response = await customBaseQuery({ url: 'config.json' }, baseQueryApi, {});
        if (!response.error) {
          return { data: response.data as StageConfig };
        } else {
          console.log('Could not fetch stage config, propably because of local test. Mocking data', response.error);
          return {
            data: {
              stage: 'local',
              //change to anything else to test read access
              ppnRolesWrite: ['ppn_approle_boss_dev_basic_application_role'],
              // FOR LOCAL TESTING: Change feature flags while developing, but please abort these changes before commiting.
              featureFlags: {
                copyOrder: false,
                updateNcoCoreData: false,
                reportRevokeTotalLoss: false,
                deallocateQuota: false,
                importerTransfer: false,
                handleDealerInventory: false,
                buySellTransfer: false,
                ncoInvoiceMapping: false,
              },
            },
          };
        }
      },
    }),
  }),
});

export const { useGetCancelReasonsQuery, useGetStageConfigQuery } = staticJsonApi;
