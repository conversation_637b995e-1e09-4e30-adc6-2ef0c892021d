import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { store } from './store/configureStore';
import { PorscheDesignSystemProvider } from '@porsche-design-system/components-react';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { BrowserRouter } from 'react-router-dom';
import AuthContextProvider from './app/AuthContext';
import { LicenseManager } from 'ag-grid-enterprise';
import FeatureFlagContextProvider from './app/FeatureFlagContext';

LicenseManager.setLicenseKey(
  'CompanyName=Dr. Ing. h.c. F. Porsche AG,LicensedGroup=DiTP,LicenseType=MultipleApplications,LicensedConcurrentDeveloperCount=26,LicensedProductionInstancesCount=0,AssetReference=AG-034535,SupportServicesEnd=31_December_2024_[v2]_MTczNTYwMzIwMDAwMA==f044794ae79b36a225ae1f45f6a302bb',
);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <PorscheDesignSystemProvider>
          <FeatureFlagContextProvider>
            <AuthContextProvider>
              <App />
            </AuthContextProvider>
          </FeatureFlagContextProvider>
        </PorscheDesignSystemProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>,
);

reportWebVitals();
