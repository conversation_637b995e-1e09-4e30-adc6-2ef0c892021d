import { useTranslation } from 'react-i18next';
import { useAppDispatch } from '../../app/hooks';
import { displayNotification } from '../../store/slices/NotificationSlice';
import { useEffect } from 'react';
import { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query';
import { SerializedError } from '@reduxjs/toolkit';
import { TFunction } from 'i18next';

interface FetchErrorProps {
  custom_error?: FetchBaseQueryError | SerializedError;
  /**
   * used for i18n (t('api_error') - t('error_area'))
   */
  error_area: string;
}
export const getErrorTextFromFetchError = (
  custom_error: FetchBaseQueryError | SerializedError,
  t: TFunction<'translation', undefined>,
): string => {
  let errorText = '';
  if ('originalStatus' in custom_error) {
    errorText = `${t('fetch_error')}: ${custom_error.status} - ${custom_error.originalStatus} - ${custom_error.data}`;
  } else if ('error' in custom_error) {
    errorText = `${t('fetch_error')}: ${custom_error.status} - ${custom_error.error} - ${custom_error.data}`;
  } else if ('status' in custom_error) {
    errorText = `${t('fetch_error')}: ${custom_error.status} - ${(custom_error.data as any).message}`;
  } else {
    errorText = `${t('serialization_error')}: ${custom_error.code}/${custom_error.name} - ${custom_error.message}`;
  }
  return errorText;
};
export const FetchError: React.FC<FetchErrorProps> = ({ custom_error, error_area }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  let errorText = '';
  if (custom_error) {
    errorText = getErrorTextFromFetchError(custom_error, t);
  }
  useEffect(() => {
    dispatch(
      displayNotification({
        title: `${t('api_error')}: ${t(error_area)}`,
        msg: errorText,
        state: 'error',
      }),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <>Error {`${t('api_error')} - ${t(error_area)}`}</>;
};
