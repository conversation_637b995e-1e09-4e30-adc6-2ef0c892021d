import { PButton } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { AuthorizedDealerForm } from '../../components/authorized-dealer/AuthorizedDealerForm';
import { getWebDomain } from '../../services/ServiceUtils';
import { useLazyGetImporterForDealerQuery } from '../../store/api/MasterdataApi';
import { DealerImporter } from './DealerImporter';
import { ModelTypeForm } from './ModelTypeForm';
import { ModelTypeVisibilityModel } from '../../../../infrastructure/lib/entities/model-type-visibility-model';

export interface KccQueryParams {
  dealer_number: string;
  importer_number: string;
  importer_code: string;
  model_type: string;
  model_year: string;
  cnr: string;
}

export const ParameterSelection: React.FC = () => {
  const [selectedAuthorizedDealer, setSelectedAuthorizedDealer] = useState<string | undefined>();
  const [triggerGetImporterDealer, importerDealerQueryResult] = useLazyGetImporterForDealerQuery();
  const [selectedModelType, setSelectedModelType] = useState<ModelTypeVisibilityModel>();

  const handleAuthorizedDealerSelect = (dealer_number: string) => {
    if (selectedAuthorizedDealer !== dealer_number) {
      setSelectedAuthorizedDealer(dealer_number);
      setSelectedModelType(undefined);
    }
  };

  const createOrder = () => {
    const queryParmas: Partial<KccQueryParams> = {
      dealer_number: importerDealerQueryResult.data?.dealer.sk_dealer_number,
      importer_number: importerDealerQueryResult.data?.importer.pk_importer_number,
      importer_code: importerDealerQueryResult.data?.importer.code,
      model_type: selectedModelType?.model_type,
      model_year: selectedModelType?.my4,
      cnr: selectedModelType?.cnr,
    };
    if (Object.values(queryParmas).some((p) => p === undefined)) {
      console.error('missing values');
      return;
    }
    const searchParams = new URLSearchParams(queryParmas).toString();
    window.location.href = `${getWebDomain()}/orders/create?${searchParams}`;
  };

  useEffect(() => {
    if (selectedAuthorizedDealer) {
      triggerGetImporterDealer(selectedAuthorizedDealer);
    }
  }, [selectedAuthorizedDealer]);

  return (
    <>
      <AuthorizedDealerForm
        handleSelect={handleAuthorizedDealerSelect}
        selectedAuthorizedDealer={selectedAuthorizedDealer}
      />
      {selectedAuthorizedDealer && (
        <DealerImporter
          data={importerDealerQueryResult.data}
          isFetching={importerDealerQueryResult.isFetching}
          error={importerDealerQueryResult.error}
        />
      )}
      {importerDealerQueryResult.data?.importer && (
        <ModelTypeForm
          handleSelect={setSelectedModelType}
          selectedModelType={selectedModelType}
          importer_number={importerDealerQueryResult.data.importer.pk_importer_number}
        />
      )}
      {selectedModelType && (
        <PButton data-e2e="continue_to_kcc" onClick={createOrder}>
          Create Order
        </PButton>
      )}
    </>
  );
};
