import React, { useEffect, useRef, useState } from 'react';
import { PSelect, PSelectOption } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import OrderListGrid from './OrderListGrid';
import { useGetOnevmsStatusQuery } from '../../store/api/MasterdataApi';
import { useInIframe } from '../../utils/useInIframe';

const PreProductionListsPage: React.FC = () => {
  const { t } = useTranslation();
  const [isChangeable, setIsChangeable] = useState<boolean>(true);
  const cancelOrderIdRef = useRef<string | null>(null);
  const [selectedValue, setSelectedValue] = useState<string>('changeable');
  const onevmsStatusRes = useGetOnevmsStatusQuery(undefined);
  const isInIframe = useInIframe();

  // Hook to store Order ID when calling cancel from mini list
  useEffect(() => {
    cancelOrderIdRef.current = localStorage.getItem('cancelOrderId');
    if (cancelOrderIdRef.current) {
      setIsChangeable(true);
    }
  }, [localStorage]);

  const handleDropdownChange = (event: any) => {
    const newSelectedValue = event.target.value;
    setSelectedValue(newSelectedValue);
    if (newSelectedValue === 'changeable') {
      setIsChangeable(true);
    } else {
      setIsChangeable(false);
    }
  };

  const changeableSelect = (
    <div style={{ marginBottom: '2vh', height: '6vh' }}>
      <PSelect name={t('changeable')} value={selectedValue} onUpdate={handleDropdownChange}>
        <PSelectOption value="changeable">{t('changeable_orders')}</PSelectOption>
        <PSelectOption value="non-changeable">{t('non_changeable_orders')}</PSelectOption>
      </PSelect>
    </div>
  );

  return (
    <>
      {isChangeable && (
        <OrderListGrid
          gridId="ChangeablePreProductionListGrid"
          pageName={t('pre_production_orders')}
          actionFilters={{ changeable: true }}
          enableCancelOrder={true}
          enableChangeOrder={true}
          enableDeallocateQuota={true}
          tableHeightInVh={isInIframe ? 80 : 70}
          children={changeableSelect}
        />
      )}
      {!isChangeable && (
        <OrderListGrid
          gridId="NonChangeablePreProductionListGrid"
          pageName={t('pre_production_orders')}
          actionFilters={{ changeable: false }}
          filterModel={{
            order_status_onevms_code: {
              filterType: 'set',
              values: (onevmsStatusRes.data?.map((s) => s.one_vms_status) ?? []).filter((s) => s.startsWith('PP')),
            },
          }}
          tableHeightInVh={isInIframe ? 80 : 70}
          children={changeableSelect}
        />
      )}
    </>
  );
};

export default PreProductionListsPage;
