import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PText } from '@porsche-design-system/components-react';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthContext } from '../../app/AuthContext';
import BuySellTransferModal from '../../components/special-actions-modals/buy-sell-transfer-modal/BuySellTransferModal';
import { useFeatureFlagContext } from '../../app/FeatureFlagContext';

const SystemNavigationPage: React.FC = () => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState<boolean>(false);
  const authContext = useAuthContext();
  const featureFlagContext = useFeatureFlagContext();

  let content: JSX.Element = (
    <PText size="medium" style={{ marginBottom: '16px' }}>
      {t('no_access')}
    </PText>
  );

  console.warn(authContext.permissions, featureFlagContext);

  if (authContext.isLoading || featureFlagContext.isLoading) {
    content = <PSpinner className="centered-foreground-spinner" size="large" />;
  } else if (featureFlagContext.featureFlags.buySellTransfer && authContext.permissions.BUY_SELL_TRANSFER) {
    content = (
      <>
        <PText size="medium" style={{ marginBottom: '16px' }}>
          {t('high_level_actions_description')}
        </PText>
        <PButton
          data-e2e="OpenBuySellModal"
          variant="secondary"
          onClick={() => {
            setShowModal(true);
          }}
        >
          {t('buy_sell_transfer_header')}
        </PButton>
        {showModal && (
          <BuySellTransferModal
            closeModal={() => {
              setShowModal(false);
            }}
          ></BuySellTransferModal>
        )}
      </>
    );
  }

  return (
    <div style={{ height: '100vh' }}>
      <PHeading tag="h3" size="x-large" style={{ marginBottom: '50px' }}>
        {t('high_level_actions_header')}
      </PHeading>
      {content}
    </div>
  );
};
export default SystemNavigationPage;
