import { LinkButtonIconName, PLinkPure } from '@porsche-design-system/components-react';
import { useAuthContext } from '../../app/AuthContext';
import { CarOrderInList, OrderActionType } from '../../store/types';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { useFeatureFlagContext } from '../../app/FeatureFlagContext';
import { useGetInboundStatusMappingQuery } from '../../store/api/MasterdataApi';
import { getAllowedEventKeys } from '../../utils/ncoActionHelper';
import { OneVmsEventKey } from '../../../../infrastructure/lib/types/process-steering-types';

interface MultiActionContainerProps {
  actionProps: {
    disableCopyOrder?: boolean;
    enableCancelOrder?: boolean;
    enableChangeOrder?: boolean;
    disableChangeOrderCoreData?: boolean;
    enableTotalLoss?: boolean;
    enableTotalLossRevoke?: boolean;
    enableDeallocateQuota?: boolean;
    disableDlrInventoryAction?: boolean;
  };
  selectedOrders: CarOrderInList[];
  handleMultiAction: (actionType: OrderActionType | OneVmsEventKey) => void;
}

const MultiActionContainer: React.FC<MultiActionContainerProps> = (props) => {
  const authContext = useAuthContext();
  const featureFlagContext = useFeatureFlagContext();
  const inboundMappings = useGetInboundStatusMappingQuery(undefined);
  const { t } = useTranslation();

  const actionButtons = useMemo(() => {
    //get allowed events for all selected orders (new process steering)
    const allowedEventKeys = getAllowedEventKeys(inboundMappings.data ?? [], props.selectedOrders);
    const _actionButtons: {
      e2e: string;
      icon: LinkButtonIconName;
      actionType: OrderActionType | OneVmsEventKey;
      name: string;
    }[] = [];
    if (
      authContext.permissions.NCO_CANCEL &&
      props.actionProps.enableCancelOrder &&
      allowedEventKeys.includes(OneVmsEventKey.CANCEL)
    ) {
      _actionButtons.push({
        e2e: 'cancel_orders',
        icon: 'delete',
        actionType: OneVmsEventKey.CANCEL,
        name: 'cancel_orders',
      });
    }
    if (
      featureFlagContext.featureFlags.deallocateQuota &&
      authContext.permissions.NCO_DEALLOCATE_QUOTA &&
      props.actionProps.enableDeallocateQuota &&
      allowedEventKeys.includes(OneVmsEventKey.DEALLOCATE_QUOTA)
    ) {
      _actionButtons.push({
        e2e: 'deallocate_quota',
        icon: 'delete',
        actionType: OneVmsEventKey.DEALLOCATE_QUOTA,
        name: 'deallocate_quota',
      });
    }

    if (
      featureFlagContext.featureFlags.reportRevokeTotalLoss &&
      authContext.permissions.NCO_REPORT_TOTAL_LOSS &&
      props.actionProps.enableTotalLoss &&
      allowedEventKeys.includes(OneVmsEventKey.TOTAL_LOSS_REPORT)
    ) {
      if (
        props.selectedOrders.every(
          (order) => order.order_status_onevms_code === 'ID0000' || order.order_status_onevms_code === 'ID5000',
        )
      ) {
        _actionButtons.push({
          e2e: 'report_total_loss',
          icon: 'exclamation',
          actionType: OneVmsEventKey.TOTAL_LOSS_REPORT,
          name: 'report_total_loss',
        });
      }
    }
    if (
      featureFlagContext.featureFlags.reportRevokeTotalLoss &&
      authContext.permissions.NCO_REVOKE_TOTAL_LOSS &&
      props.actionProps.enableTotalLossRevoke &&
      allowedEventKeys.includes(OneVmsEventKey.TOTAL_LOSS_REVOKE)
    ) {
      if (props.selectedOrders.every((order) => order.order_status_onevms_code === '0X1100')) {
        _actionButtons.push({
          e2e: 'revoke_total_loss',
          icon: 'reset',
          actionType: OneVmsEventKey.TOTAL_LOSS_REVOKE,
          name: 'revoke_total_loss',
        });
      }
    }
    if (
      featureFlagContext.featureFlags.updateNcoCoreData &&
      authContext.permissions.NCO_MULTI_EDIT &&
      !props.actionProps.disableChangeOrderCoreData &&
      //check that all selected orders are allowed for event UPDATE_CORE_DATA
      allowedEventKeys.includes(OneVmsEventKey.UPDATE_CORE_DATA)
    ) {
      _actionButtons.push({
        e2e: 'update_orders_core_data',
        icon: 'edit',
        actionType: OneVmsEventKey.UPDATE_CORE_DATA,
        name: 'update_core_data',
      });
    }
    if (
      featureFlagContext.featureFlags.handleDealerInventory &&
      authContext.permissions.NCO_MOVE_TO_DEALER_INVENTORY &&
      !props.actionProps.disableDlrInventoryAction &&
      allowedEventKeys.includes(OneVmsEventKey.MOVE_TO_INVENTORY)
    ) {
      if (props.selectedOrders.every((order) => order.order_invoice_onevms_code === 'PI4000')) {
        _actionButtons.push({
          e2e: 'move_orders_to_dealer_inventory',
          icon: 'arrow-double-right',
          actionType: OneVmsEventKey.MOVE_TO_INVENTORY,
          name: 'move_to_dealer_inventory',
        });
      }
    }
    if (
      featureFlagContext.featureFlags.handleDealerInventory &&
      authContext.permissions.NCO_REMOVE_FROM_DEALER_INVENTORY &&
      !props.actionProps.disableDlrInventoryAction &&
      allowedEventKeys.includes(OneVmsEventKey.REMOVE_FROM_INVENTORY)
    ) {
      if (props.selectedOrders.every((order) => order.order_invoice_onevms_code === 'MI9000')) {
        _actionButtons.push({
          e2e: 'remove_orders_from_dealer_inventory',
          icon: 'arrow-double-left',
          actionType: OneVmsEventKey.REMOVE_FROM_INVENTORY,
          name: 'remove_from_dealer_inventory',
        });
      }
    }
    if (featureFlagContext.featureFlags.importerTransfer && authContext.permissions.NCO_TRANSFER_IMPORTER) {
      _actionButtons.push({
        e2e: 'importer_transfer',
        icon: 'switch',
        actionType: OrderActionType.IMPORTER_TRANSFER,
        name: 'importer_transfer',
      });
    }
    return _actionButtons;
  }, [props.selectedOrders, authContext.permissions, props.actionProps, inboundMappings]);

  return (
    <>
      {actionButtons.length > 0 && props.selectedOrders.length > 1 && (
        <div style={{ display: 'flex', justifyContent: 'flex-start', paddingBottom: '10px' }}>
          {actionButtons.map((a, i) => (
            <PLinkPure
              key={i}
              data-e2e={a.e2e}
              theme="light"
              size="small"
              style={{ marginLeft: '20px' }}
              icon={a.icon}
              underline={false}
              onClick={() => {
                props.handleMultiAction(a.actionType);
              }}
            >
              {t(a.name)}
            </PLinkPure>
          ))}
        </div>
      )}
    </>
  );
};

export default MultiActionContainer;
