import { PButtonPure, PHeading } from '@porsche-design-system/components-react';
import { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetFailedStatusMappingOrdersQuery } from '../../store/api/MonitoringApi';
import { FailedCarOrderInList } from '../../store/types';
import { useTheme } from '../../utils/useTheme';
import { useInIframe } from '../../utils/useInIframe';

const FailedOrderListGrid: React.FC = () => {
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [selectedFailedOrders, setSelectedFailedOrders] = useState<FailedCarOrderInList[]>([]);
  const { t, i18n } = useTranslation();
  const {
    data: failedOrders,
    isLoading: isFailedOrdersLoading,
    refetch,
    isFetching,
  } = useGetFailedStatusMappingOrdersQuery(undefined); //to use it
  const isInIframe = useInIframe();
  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    params.api.setGridOption('loading', isFailedOrdersLoading || isFetching);
    const allColumnIds: string[] = [];
    params.api.getColumns()?.forEach((column) => {
      allColumnIds.push(column.getColId());
    });
    params.api.autoSizeColumns(allColumnIds, false);
  };

  const theme = useTheme();

  useEffect(() => {
    if (gridApi) {
      gridApi.setGridOption('loading', isFailedOrdersLoading || isFetching);
    }
  }, [isFailedOrdersLoading, gridApi, isFetching]);

  useEffect(() => {
    if (failedOrders) {
      const updatedOrders = failedOrders.map((failedOrder) => ({
        ...failedOrder,
      })) as FailedCarOrderInList[];
      setRowData(updatedOrders);
    }
  }, [failedOrders, gridApi, t, i18n.language]);

  useEffect(() => {
    setRowData(failedOrders || []);
  }, [failedOrders]);

  const columns = useMemo<ColDef<FailedCarOrderInList>[]>(
    () => [
      {
        headerName: t('new_car_order_id'),
        headerTooltip: t('new_car_order_id'),
        field: 'value.ids.new_car_order_id',
        tooltipField: 'value.ids.new_car_order_id',
        sort: 'asc',
        initialSortIndex: 1,
        pinned: 'left',
      },
      {
        headerName: t('timestamp'),
        headerTooltip: t('timestamp'),
        field: 'value.order_info.status_info.order_status_pvms_timestamp',
        tooltipField: 'value.order_info.status_info.order_status_pvms_timestamp',
        cellRenderer: (params: any) => {
          const isoString = params.value;
          if (!isoString) return '';
          const date = new Date(isoString);
          const formattedDate = date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
          });
          return formattedDate;
        },
      },
      {
        headerName: t('vehicle_status_pvms_code'),
        headerTooltip: t('vehicle_status_pvms_code'),
        field: 'value.order_info.status_info.vehicle_status_pvms_code',
        tooltipField: 'value.order_info.status_info.vehicle_status_pvms_code',
      },
      {
        headerName: t('sperrgrund'),
        headerTooltip: t('sperrgrund'),
        field: 'value.order_info.planning.blocking_reason_planning',
        tooltipField: 'value.order_info.planning.blocking_reason_planning',
      },
      {
        headerName: t('kate'),
        headerTooltip: t('kate'),
        field: 'kate',
        tooltipField: 'kate',
      },
      {
        headerName: t('qs_sperren'),
        headerTooltip: t('qs_sperren'),
        field: 'qs_sperren',
        tooltipField: 'qs_sperren',
      },
    ],
    [t],
  );

  const [rowData, setRowData] = useState<FailedCarOrderInList[]>([]);

  const onSelectionChanged = useCallback(() => {
    setSelectedFailedOrders(gridApi?.getSelectedRows() ?? []);
  }, [gridApi]);

  const handleRefresh = async () => {
    gridApi?.setGridOption('loading', true);
    await refetch();
  };
  return (
    <div>
      <div
        className={`${theme === 'light' ? 'ag-theme-pds' : 'ag-theme-pds-dark'} compact`}
        style={{ width: '100%', overflow: 'hidden', position: 'relative' }}
      >
        <PHeading size="medium" style={{ paddingBottom: '1vh', height: '3vh' }}>
          Failed Orders
          <PButtonPure
            theme={theme}
            size={'small'}
            icon="reset"
            underline={false}
            style={{ marginLeft: '20px', verticalAlign: 'middle' }}
            onClick={handleRefresh}
          ></PButtonPure>
        </PHeading>
        <div
          style={{
            height: isInIframe ? '93vh' : '81vh',
            overflow: 'hidden',
            flex: '1 1 0px',
            width: '100%',
          }}
        >
          <AgGridReact
            gridId="FailedOrderGrid"
            onGridReady={onGridReady}
            columnDefs={columns}
            floatingFiltersHeight={70}
            enableCellTextSelection={true}
            defaultColDef={{
              filter: 'agTextColumnFilter',
              sortable: true,
              resizable: true,
              editable: false,
              floatingFilter: true,
              flex: 1,
              minWidth: 210,
              maxWidth: 400,
              cellStyle: () => ({
                display: 'flex',
                alignItems: 'center',
              }),
              wrapHeaderText: true,
              autoHeight: true,
              wrapText: true,
            }}
            tooltipShowDelay={1000}
            tooltipHideDelay={4000}
            rowData={rowData}
            rowSelection="multiple"
            domLayout="normal"
            onSelectionChanged={onSelectionChanged}
            suppressRowClickSelection
            alwaysShowHorizontalScroll={false}
            suppressHorizontalScroll={false}
            autoSizeStrategy={{
              type: 'fitCellContents',
            }}
            // Pagination props
            pagination={true}
            paginationPageSize={100}
            paginationPageSizeSelector={[20, 100, 500]}
          />
        </div>
      </div>
    </div>
  );
};
export default FailedOrderListGrid;
