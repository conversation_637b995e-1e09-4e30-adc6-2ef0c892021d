import { PModal } from '@porsche-design-system/components-react';
import React from 'react';
import { NewCarOrderAuditTrailModel } from '../../../../infrastructure/lib/entities/new-car-order-audit-trail-model';
import JsonDiffViewer from './JsonDiffViewer';
import './AuditDetailModal.css';

export interface AuditDetailModalProps {
  auditTrail?: NewCarOrderAuditTrailModel;
  open: boolean;
  closeModal: () => void;
}

const AuditDetailModal: React.FC<AuditDetailModalProps> = (props: AuditDetailModalProps) => {
  return (
    <PModal
      className="audit-detail-modal"
      data-e2e="audit_trail_modal"
      open={props.open}
      dismissButton={true}
      onDismiss={props.closeModal}
    >
      {props.auditTrail && (
        <JsonDiffViewer
          oldJson={props.auditTrail.old_nco ?? {}}
          newJson={props.auditTrail.new_nco ?? {}}
          // oldJson={props.auditTrail.old_nco ?? undefined}
          // newJson={props.auditTrail.new_nco ?? undefined}
        ></JsonDiffViewer>
      )}
    </PModal>
  );
};

export default AuditDetailModal;
