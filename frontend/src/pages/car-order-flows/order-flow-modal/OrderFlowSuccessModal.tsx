import { <PERSON><PERSON><PERSON>, PIcon, PModal, PText } from '@porsche-design-system/components-react';
import { JSX, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { CoraNCOBaseApiResponse } from '../../../../../infrastructure/lib/types/new-car-order-types';
import { useLazyGetModelTypeTextsQuery } from '../../../store/api/BossApi';
import { AppDispatch } from '../../../store/configureStore';
import { clearCorrelationId } from '../../../store/slices/CorrelationSlice';
import { matchMttForModelTypeAndModelYear } from '../../../utils/utils';
import './OrderFlowModal.css';
import './OrderFlowSuccessModal.css';
interface OrderFlowSuccessModalProps {
  open?: boolean;
  heading: string;
  createdOrder: CoraNCOBaseApiResponse;
  navigationButtonGroup: JSX.Element;
  action: 'create' | 'update' | 'convert';
}
export const OrderFlowSuccessModal: React.FC<OrderFlowSuccessModalProps> = ({
  open,
  createdOrder,
  heading,
  navigationButtonGroup,
  action,
}) => {
  const { t, i18n } = useTranslation();

  const dispatch = useDispatch<AppDispatch>();

  const [loadModelTypeTexts, { data: modelTypeTexts, isLoading: isModelTypeTextsLoading }] =
    useLazyGetModelTypeTextsQuery();
  const [modelTypeText, setModelTypeText] = useState<string>(t('not_loaded'));

  //load model type texts if empty or language changed
  useEffect(() => {
    if (!isModelTypeTextsLoading) {
      if (!modelTypeTexts || (modelTypeTexts.length > 0 && modelTypeTexts[0].iso_language_code !== i18n.language)) {
        loadModelTypeTexts(i18n.language);
      }
    }
  }, [i18n.language, dispatch]);

  useEffect(() => {
    if (open) {
      dispatch(clearCorrelationId());
      console.log('The order process is finished and correlationId has been cleared');
    }
  }, [open, dispatch]);

  useEffect(() => {
    if (modelTypeTexts) {
      const modelTypeText = matchMttForModelTypeAndModelYear(
        modelTypeTexts,
        createdOrder.model_type,
        createdOrder.model_year,
        t,
      );
      setModelTypeText(modelTypeText ?? t('missing_translation'));
    }
  }, [modelTypeTexts]);

  //on Click copy order id
  const copyOrderID = () => {
    navigator.clipboard.writeText(createdOrder.pk_new_car_order_id);
  };

  const headerMessagePart = (
    <h2 data-e2e="ShowOrderSuccessHeader">
      <PIcon name="success-filled" color="notification-success" aria={{ 'aria-label': 'Success Filled icon' }} />{' '}
      {heading}
    </h2>
  );

  const newCarOrderIdPart = (
    <div className="new-car-order-id">
      <PText weight={'thin'} size={'large'} data-e2e="pk_new_car_order_id">
        {createdOrder.pk_new_car_order_id}
        <PIcon onClick={copyOrderID} style={{ cursor: 'pointer' }} name="copy" aria={{ 'aria-label': 'Copy icon' }} />
      </PText>
    </div>
  );

  const dealerImporterPart = (
    <div className="order-edit-details-single-container">
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('importer_number')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="importer_number">
            {createdOrder.importer_number}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('dealer_number')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="dealer_number">
            {createdOrder.dealer_number}
          </PText>
        </div>
      </div>
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('importer_code')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="importer_code">
            {createdOrder.importer_code}
          </PText>
        </div>
      </div>
    </div>
  );

  const modelInfoPart = (
    <div className="order-edit-details-single-container">
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('model_type_text')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="model_type_text">
            {modelTypeText}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('model_year')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="model_year">
            {createdOrder.model_year}
          </PText>
        </div>
      </div>
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('model_type')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="model_type">
            {createdOrder.model_type}
          </PText>
        </div>
      </div>
    </div>
  );

  const orderDetailsPart = (
    <div className="order-edit-details-single-container">
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('order_type')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="order_type">
            {createdOrder.order_type}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('shipping_code')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="shipping_code">
            {createdOrder.shipping_code}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('desired_delivery_date')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="requested_dealer_delivery_date">
            {createdOrder.requested_dealer_delivery_date || '-'}
          </PText>
        </div>
      </div>
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('port_name')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="receiving_port_code">
            {createdOrder.receiving_port_code || '-'}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('quota_month')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="quota_month">
            {createdOrder.quota_month}
          </PText>
        </div>
      </div>
    </div>
  );

  const auditDetailsPartEdit = (
    <div className="order-edit-details-single-container">
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('changed_on_date')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="modified_at">
            {createdOrder.modified_at}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('changed_by')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="modified_by">
            {createdOrder.modified_by}
          </PText>
        </div>
      </div>
    </div>
  );

  const auditDetailsPartCreate = (
    <div className="order-edit-details-single-container">
      <div className="field-col">
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('created_at')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="modified_at">
            {createdOrder.created_at}
          </PText>
        </div>
        <div className="field-row">
          <PText color="contrast-medium" size={'xx-small'}>
            {t('created_by')}
          </PText>
          <PText weight={'bold'} size={'x-small'} data-e2e="modified_by">
            {createdOrder.created_by}
          </PText>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <PModal open={!!open} dismissButton={false}>
        <br />
        {headerMessagePart}
        {newCarOrderIdPart}
        <br />
        {dealerImporterPart}
        <PDivider style={{ paddingBottom: '20px' }}></PDivider>
        {modelInfoPart}
        <PDivider style={{ paddingBottom: '20px' }}></PDivider>
        {orderDetailsPart}
        <PDivider style={{ paddingBottom: '20px' }}></PDivider>
        {action === 'update' ? auditDetailsPartEdit : auditDetailsPartCreate}
        <PDivider style={{ paddingBottom: '20px' }}></PDivider>
        {navigationButtonGroup}
      </PModal>
    </div>
  );
};
