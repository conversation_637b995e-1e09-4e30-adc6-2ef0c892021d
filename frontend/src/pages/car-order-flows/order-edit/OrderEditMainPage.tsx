import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PModal, PSpinner } from '@porsche-design-system/components-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  CoraNCOBaseApiResponse,
  CoraNCOEditApiRequest,
} from '../../../../../infrastructure/lib/types/new-car-order-types';
import {
  CoraNcoUpdateApiRequest,
  UpdateNcoPayload,
  OneVmsEventKey,
} from '../../../../../infrastructure/lib/types/process-steering-types';
import { useAppDispatch, useAppSelector } from '../../../app/hooks';
import { ProcessSteeringResultStateComponent } from '../../../components/shared/process-steering-components/order-action-common-modal/ProcessSteeringResultState';
import { routes } from '../../../Constants';
import { useEditNewCarOrderMutation, useGetNewCarOrderByIdQuery } from '../../../store/api/NewCarOrderApi';
import { selectKccConfig, setKccFocused } from '../../../store/slices/KccConfigSlice';
import { displayNotification } from '../../../store/slices/NotificationSlice';
import { selectUser } from '../../../store/slices/UserMenuSlice';
import { CarOrderInList } from '../../../store/types';
import { ObjectValidator } from '../../../utils/object-validation';
import { FetchError, getErrorTextFromFetchError } from '../../errors/FetchErrors';
import KccConfiguration from '../../kcc-component/KccConfiguration';
import { KccComponentInitialRequestParams } from '../../kcc-component/kccUtils';
import { OrderFlowModal } from '../order-flow-modal/OrderFlowModal';
import { OrderEditForm } from './OrderEditForm';

export type CoraNCOEditData = Omit<CoraNCOEditApiRequest, 'quota_month'> & {
  quota_month: string | null;
};

const OrderEditMainPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const { nco_id } = useParams();
  const { data: newCarOrder, error, isLoading } = useGetNewCarOrderByIdQuery(nco_id ?? '');

  const objectValidator = new ObjectValidator<CoraNCOEditApiRequest>('CoraNCOEditApiRequest');
  const [newCarOrderToEdit, setNewCarOrderToEdit] = useState<CoraNCOEditData | undefined>(undefined);
  const [modalError, setModalError] = useState<string | undefined>(undefined);
  const [kccParams, setKccParams] = useState<KccComponentInitialRequestParams>();
  const [formError, setFormError] = useState<string>();
  const kccConfig = useAppSelector(selectKccConfig);
  const correlationId = useAppSelector((state) => state.correlation.correlationId);
  const username = useAppSelector(selectUser)?.username;
  const [
    editOrder,
    {
      isLoading: isEditingOrder,
      isError: isOrderCreateError,
      isSuccess: isOrderEditSuccess,
      data: orderEditData,
      error: orderCreateError,
    },
  ] = useEditNewCarOrderMutation();

  function onFormChange(order: CoraNCOEditData) {
    setNewCarOrderToEdit(order);
  }

  function validateNewCarOrderEditRequest(order: Partial<CoraNCOEditApiRequest>): CoraNCOEditApiRequest | undefined {
    console.error('validate', formError);
    if (formError) {
      setModalError(formError);
      console.error('validation failed', formError);
      return undefined;
    }
    order.configuration_signature = kccConfig.signature_config;
    order.configuration = kccConfig.config;
    order.configuration_expire = kccConfig.config_pvms;
    order.configuration_expire_signature = kccConfig.signature_config_pvms;
    order.modified_by = username;
    const [order_validated, validation_errors] = objectValidator.validate(order);
    if (order_validated === null) {
      console.error('validation failed', JSON.stringify(validation_errors));
      setModalError(validation_errors?.[0].message);
      return undefined;
    }
    setModalError(undefined);
    return order_validated;
  }

  function onEditOrder() {
    if (newCarOrderToEdit) {
      const payload = validateNewCarOrderEditRequest(newCarOrderToEdit as UpdateNcoPayload)!;
      const validated_order: CoraNcoUpdateApiRequest = {
        nco_ids_with_modified_at: [
          {
            pk_new_car_order_id: newCarOrderToEdit.pk_new_car_order_id,
            modified_at: newCarOrderToEdit.modified_at ? newCarOrderToEdit.modified_at : '',
          },
        ],
        payload: payload,
      };
      editOrder(validated_order);
      console.log('Send Order Request', validated_order);
    } else {
      dispatch(
        displayNotification({
          title: t('unknown_error'),
          msg: t('unknown_error', { area: 'order_create' }),
          state: 'error',
        }),
      );
    }
  }

  function restartKccWorkflow() {
    console.log('restart edit flow');
  }

  useEffect(() => {
    if (newCarOrder) {
      setKccParams({ ...newCarOrder, process: 'CO2' });
      setNewCarOrderToEdit({ ...newCarOrder, configuration_signature: 'dummy' });
    } else {
      setNewCarOrderToEdit(newCarOrder);
      setKccParams(undefined);
    }
  }, [newCarOrder]);
  useEffect(() => {
    if (isOrderCreateError && orderCreateError) {
      setModalError(getErrorTextFromFetchError(orderCreateError, t));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOrderCreateError]);
  const convertOrder = (order: CoraNCOBaseApiResponse): CarOrderInList => ({
    ...order,
    business_partner_id: order.business_partner_id ?? '',
    receiving_port_code: order.receiving_port_code ?? undefined,
    requested_dealer_delivery_date: order.requested_dealer_delivery_date ?? '',
  });
  const navigate = useNavigate();
  function goToOrderList() {
    dispatch(setKccFocused(true));
    navigate(routes.lists.preProductionOrders);
  }
  if (!nco_id) {
    return <>{t('no_new_car_order_id_in_params')}</>;
  }
  if (isLoading) {
    return <PSpinner />;
  }
  if (!newCarOrder) {
    return <FetchError custom_error={error} error_area="order_edit" />;
  }

  return (
    <div>
      {isOrderEditSuccess && orderEditData ? (
        <PModal
          disableBackdropClick
          data-e2e={`${OneVmsEventKey.UPDATE_NCO}_order_modal`}
          open
          backdrop="blur"
          dismissButton={false}
          onDismiss={goToOrderList}
        >
          <ProcessSteeringResultStateComponent
            orders={[convertOrder(newCarOrder)]}
            isMulti={false}
            closeModal={goToOrderList}
            error={orderCreateError}
            result={orderEditData}
            actionType={OneVmsEventKey.UPDATE_NCO}
          />
        </PModal>
      ) : (
        <>
          {newCarOrderToEdit && (
            <OrderFlowModal
              e2eId="change_order_modal"
              heading={t('change_order')}
              error={modalError}
              body={
                <OrderEditForm
                  partialNewCarOrder={newCarOrderToEdit}
                  existingNewCarOrder={newCarOrder}
                  onFormChange={onFormChange}
                  setError={setFormError}
                />
              }
              onConfirm={onEditOrder}
              onBackToKcc={restartKccWorkflow}
              confirmButtonText="change_order_button"
              mutationRequestInProgress={isEditingOrder}
            />
          )}
        </>
      )}
      {kccParams && (
        <KccConfiguration
          kccInitRequestParams={kccParams}
          kccCancelDefaultRedirectUrl={`${window.location.origin}${routes.lists.preProductionOrders}`}
        />
      )}
    </div>
  );
};
export default OrderEditMainPage;
