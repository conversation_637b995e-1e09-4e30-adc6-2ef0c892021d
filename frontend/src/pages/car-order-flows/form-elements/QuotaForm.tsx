import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraQuota } from '../../../../../infrastructure/lib/types/quota-api-types';
import { useAppSelector } from '../../../app/hooks';
import { useGetQuotaForDealerQuery } from '../../../store/api/QuotaApi';
import { selectKccConfig } from '../../../store/slices/KccConfigSlice';
import { getValidQuotaMonthForConfig } from '../../../utils/utils';
import { FetchError } from '../../errors/FetchErrors';
import { CoraNCOGetApiResponse } from '../../../../../infrastructure/lib/types/new-car-order-types';

interface QuotaFormProps {
  dealer_number: string;
  importer_number: string;
  model_type: string;
  model_year: string;
  selectedQuotaMonth?: string;
  existingNewCarOrder?: CoraNCOGetApiResponse; // Only for existing NewCarOrder (edit workflow)
  isDisabled?: boolean;
  handleQuotaSelect: (quota_month: string) => void;
  setError: (area: string, err?: string) => void;
}

export const QuotaForm: React.FC<QuotaFormProps> = ({
  dealer_number,
  importer_number,
  model_type,
  model_year,
  selectedQuotaMonth,
  existingNewCarOrder,
  isDisabled,
  handleQuotaSelect,
  setError,
}) => {
  const { t } = useTranslation();
  const dummyQuota: CoraQuota = {
    consumed_new_car_order_ids: [],
    created_at: '',
    created_by: '',
    dealer_number: 0,
    importer_number: 0,
    model_type: '',
    model_year: 0,
    last_modified_at: '',
    quota_consumed: 0,
    quota_count: 0,
    quota_id: '',
    quota_id_without_month: '',
    quota_month: '',
    quota_open: 0,
  };

  const [selectableQuotas, setSelectableQuotas] = useState<CoraQuota[] | undefined>(undefined);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [descriptionMessage, setDescriptionMessage] = useState<string | undefined>(undefined);
  const kccConfig = useAppSelector(selectKccConfig);
  const [firstValidQuotaMonth, setFirstValidQuotaMonth] = useState<string>(new Date().toISOString().slice(0, 7));
  const [lastValidQuotaMonth, setLastValidQuotaMonth] = useState<string>(
    new Date('9999-12-01').toISOString().slice(0, 7),
  );

  const {
    data: quota_months,
    error,
    isLoading,
  } = useGetQuotaForDealerQuery({ dealer_number, model_type, model_year, importer_number });

  useEffect(() => {
    setError('quota_form', errMessage);
  }, [errMessage]);

  useEffect(() => {
    if (kccConfig.config) {
      const { from, until } = getValidQuotaMonthForConfig(kccConfig.config, existingNewCarOrder?.configuration);
      setFirstValidQuotaMonth(from);
      setLastValidQuotaMonth(until);
    }
  }, [kccConfig.config]);

  useEffect(() => {
    if (quota_months) {
      const _selectableQuotas = quota_months.filter(
        (q) => q.quota_open > 0 && q.quota_month >= firstValidQuotaMonth && q.quota_month <= lastValidQuotaMonth,
      );
      // Only for existing NewCarOrders, always add the existing quota
      if (existingNewCarOrder?.quota_month) {
        let _existingQuota = _selectableQuotas.find((q) => q.quota_month === existingNewCarOrder.quota_month);
        if (!_existingQuota) {
          _existingQuota = {
            ...dummyQuota,
            quota_month: existingNewCarOrder.quota_month,
          };
          _selectableQuotas.push(_existingQuota);
        }
      }
      if (selectedQuotaMonth) {
        // Always add the selected Quota
        let _selectedQuota = _selectableQuotas.find((q) => q.quota_month === selectedQuotaMonth);
        if (!_selectedQuota) {
          _selectedQuota = quota_months.find((q) => q.quota_month === selectedQuotaMonth) ?? {
            ...dummyQuota,
            quota_month: selectedQuotaMonth,
          };
          _selectableQuotas.push(_selectedQuota);
        }
        if (selectedQuotaMonth === existingNewCarOrder?.quota_month) {
          setErrMessage(undefined);
          setDescriptionMessage(t('existing_quota'));
          setSelectableQuotas(_selectableQuotas);
          return;
        }
        setDescriptionMessage(undefined);
        if (_selectedQuota.quota_open <= 0) {
          setErrMessage(t('prefilled_quota_not_available'));
          setSelectableQuotas(_selectableQuotas);
          return;
        }
        if (!errMessage && (selectedQuotaMonth < firstValidQuotaMonth || selectedQuotaMonth > lastValidQuotaMonth)) {
          setErrMessage(t('config_validity_excludes_selected_quota'));
          setSelectableQuotas(_selectableQuotas);
          return;
        }
      }
      if (_selectableQuotas.length === 0) {
        setErrMessage(t('selected_quota_has_no_free_quota'));
        setSelectableQuotas(_selectableQuotas);
        return;
      }
      setErrMessage(undefined);
      setSelectableQuotas(_selectableQuotas);
      return;
    }
  }, [quota_months, selectedQuotaMonth, firstValidQuotaMonth, lastValidQuotaMonth]);

  if (isLoading) {
    return <PSpinner />;
  }

  if (!quota_months) {
    return <FetchError custom_error={error} error_area="fetch_quotas" />;
  }

  return (
    <>
      <PSelectWrapper
        data-e2e="SelectQuotaMonth"
        style={{ flexBasis: 'calc(50% - 10px)' }}
        label={t('quota_month_prompt')}
        state={errMessage ? 'error' : 'success'}
        message={errMessage ? errMessage : descriptionMessage}
      >
        <select
          required
          disabled={isDisabled}
          value={selectedQuotaMonth || ''}
          onChange={(e) => {
            const quota = selectableQuotas?.find((q) => q.quota_month === e.target.value);
            if (quota) {
              handleQuotaSelect(quota.quota_month);
            }
          }}
        >
          <option>{t('quota_month_selection')}</option>
          {(selectableQuotas?.sort((a, b) => (a.quota_month > b.quota_month ? 1 : -1)) ?? []).map((q) => (
            <option key={q.quota_month} value={q.quota_month}>
              {`${q.quota_month} (${q.quota_open})`}
            </option>
          ))}
        </select>
      </PSelectWrapper>
    </>
  );
};
