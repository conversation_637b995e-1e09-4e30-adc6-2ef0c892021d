import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CoraMdOrderTypeResponseItem } from '../../../../../infrastructure/lib/types/masterdata-types';
import { useGetOrderTypesForDealerQuery } from '../../../store/api/MasterdataApi';
import { FetchError } from '../../errors/FetchErrors';

/**
 * Shorthands:
 * KF (Customer related)
 * LF (Not customer related)
 *
 * Edit NewCarOrder:
 * KF -> LF yes
 * LF -> KF no
 *
 * Convert:
 * KF only
 *
 * Create:
 * LF only
 */
interface OrderTypePossiblities {
  kf_to_lf?: boolean;
  lf_to_kf?: boolean;
}
interface OrderTypeFormProps {
  dealer_number: string;
  isCustomerRelated: boolean;
  selectedOrderType?: string;
  orderTypePossibilities?: OrderTypePossiblities;
  handleSelect: (orderType: string) => void;
  setError: (area: string, err?: string) => void;
  notFiltered?: boolean | false;
}
export const OrderTypeForm: React.FC<OrderTypeFormProps> = ({
  isCustomerRelated,
  orderTypePossibilities,
  dealer_number,
  selectedOrderType,
  handleSelect,
  setError,
  notFiltered,
}) => {
  const { t } = useTranslation();

  const { data: orderTypes, error, isLoading } = useGetOrderTypesForDealerQuery(dealer_number);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    setError('order_type_form', errMessage);
  }, [errMessage]);

  function filterOrderTypes(
    order_types: CoraMdOrderTypeResponseItem[],
    isCustomerRelated: boolean,
    orderTypePossibilities?: OrderTypePossiblities,
  ) {
    if (isCustomerRelated) {
      if (orderTypePossibilities?.kf_to_lf) {
        return order_types;
      } else {
        return order_types.filter((oT) => oT.is_customer_related);
      }
    } else {
      if (orderTypePossibilities?.lf_to_kf) {
        return order_types;
      } else {
        return order_types.filter((oT) => !oT.is_customer_related);
      }
    }
  }

  if (isLoading) {
    return <PSpinner />;
  }

  if (!orderTypes) {
    return <FetchError custom_error={error} error_area="order_type" />;
  }

  return (
    <PSelectWrapper
      data-e2e="SelectOrderType"
      style={{ flexBasis: 'calc(50% - 10px)' }}
      label={t('order_type_prompt')}
      state={errMessage ? 'error' : 'none'}
      message={errMessage}
    >
      <select
        required
        value={selectedOrderType ?? ''}
        onChange={(e) => {
          const orderType = orderTypes.find((m) => m.pk_order_type === e.target.value);
          if (orderType) {
            handleSelect(orderType.pk_order_type);
          }
        }}
      >
        <option>{t('order_type_selection')}</option>
        {!notFiltered
          ? filterOrderTypes(orderTypes, isCustomerRelated, orderTypePossibilities)
              .filter((ot) => (isCustomerRelated ? ot.is_customer_related : !ot.is_customer_related))
              .filter((ot) => ot.editable || ot.pk_order_type === selectedOrderType)
              .map((orderType) => (
                <option key={orderType.pk_order_type} value={orderType.pk_order_type} disabled={!orderType.editable}>
                  {`${orderType.pk_order_type} - ${orderType.description}`}
                </option>
              ))
          : orderTypes.map((orderType) => (
              <option key={orderType.pk_order_type} value={orderType.pk_order_type} disabled={!orderType.editable}>
                {`${orderType.pk_order_type} - ${orderType.description}`}
              </option>
            ))}
      </select>
    </PSelectWrapper>
  );
};
