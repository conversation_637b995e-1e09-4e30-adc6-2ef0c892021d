import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PCheckboxWrapper, PDivider } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { saveConfig, selectKccConfig, setKccFocused } from '../../store/slices/KccConfigSlice';
import { KccProxyInitialParams, KccSaveConfigWindowMessage, getKccDefaultUrl, getKccSearchParams } from './kccUtils';
import { useAppSelector } from '../../app/hooks';
import { useSearchParams } from 'react-router-dom';
import { useSendConfigToKccMutation } from '../../store/api/KccApi';
import { useDispatch } from 'react-redux';
import { useGetStageConfigQuery } from '../../store/api/StaticJsonApi';

export const kccIFrameId = 'KccConfigurationFrame';

interface KccConfigurationTestProps {
  /**
   * Parameters to start the Kcc Configuration Workflow
   */
  kccInitRequestParams: KccProxyInitialParams;
  /**
   * Default URL to redirect to when Kcc Sends a CANCEL WindowPostMessage
   */
  kccCancelDefaultRedirectUrl: string;
  /**
   * true if KCC should be reopened with the new Configuration after the configuration changed
   * @default false
   */
  kccResendConfigOnConfigChange?: boolean;
}
const KccConfigurationTest: React.FC<KccConfigurationTestProps> = ({
  kccInitRequestParams,
  kccCancelDefaultRedirectUrl,
  kccResendConfigOnConfigChange: kccResendConfigAfterSaveInKcc,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [useKccDummy, setUseKccDummy] = useState<boolean>(false);
  const [searchParams, _] = useSearchParams();
  const [showIframe, setShowIframe] = useState<boolean>(false);
  const [iframeUrl, setIframeUrl] = useState<string | undefined>(getKccDefaultUrl());
  const [kccUrlParams, setKccUrlParams] = useState<string>('');
  const [isKccCancel, setIsKccCancel] = useState<boolean>(false);
  const [triggerSendConfigToKcc, proxyConfigEndpointResponse] = useSendConfigToKccMutation();
  const kccConfig = useAppSelector(selectKccConfig);

  const { data: stageConfig } = useGetStageConfigQuery(undefined);

  useEffect(() => {
    if (proxyConfigEndpointResponse.isUninitialized) {
      return;
    }
    if (proxyConfigEndpointResponse.isLoading) {
      setShowIframe(false);
    } else {
      if (proxyConfigEndpointResponse.isSuccess) {
        setKccUrlParams(getKccSearchParams({ 'session-id': proxyConfigEndpointResponse.data.sessionId }));
        setShowIframe(true);
      } else {
        console.error('Proxy Endpoint done loading but no success', proxyConfigEndpointResponse);
      }
    }
  }, [proxyConfigEndpointResponse.isLoading, proxyConfigEndpointResponse.isSuccess]);

  const style: React.CSSProperties = {
    width: '100%',
    minWidth: '1100px',
    height: '100%',
    minHeight: '80vh',
    border: 0,
    // position: 'fixed',
    // top: `calc(88px + 40px)`,
    left: 0,
    bottom: 0,
    right: 0,
    margin: 0,
    padding: 0,
  };
  const hideKccConfiguration = () => {
    dispatch(setKccFocused(false));
  };

  function triggerNewCallKcc() {
    triggerSendConfigToKcc(kccInitRequestParams);
  }

  useEffect(() => {
    if (kccInitRequestParams) {
      triggerNewCallKcc();
    }
  }, [kccInitRequestParams]);

  useEffect(() => {
    if (kccResendConfigAfterSaveInKcc) {
      triggerNewCallKcc();
    }
  }, [kccConfig]);

  useEffect(() => {
    console.debug('Iframe Full URL', `${iframeUrl}?${kccUrlParams}`);
  }, [iframeUrl, kccUrlParams]);

  useEffect(() => {
    console.debug('UseKccDummyChange', useKccDummy);
    if (useKccDummy) {
      if (window.location.hostname === 'localhost') {
        setIframeUrl('http://localhost:3005');
        return;
      }
      const url = `https://kcc-dummy.${window.location.host}`;
      setIframeUrl(url);
    } else {
      setIframeUrl(getKccDefaultUrl());
    }
  }, [useKccDummy]);

  useEffect(() => {
    if (stageConfig?.stage === 'dev') {
      setUseKccDummy(true);
    }
  }, [stageConfig]);

  useEffect(() => {
    if (isKccCancel) {
      const redirectTo = decodeURI(searchParams.get('origin_url') ?? '') || kccCancelDefaultRedirectUrl;
      window.location.assign(redirectTo);
    }
  }, [isKccCancel]);

  function onWindowMessage(event: MessageEvent) {
    switch (event.data.channel) {
      case 'KASCC_NOTIFICATION':
        switch (event.data.type) {
          case 'CONFIG_SAVED':
          case 'CONFIG_TRANSFER':
            console.debug('CONFIG_SAVED message from KCC', event.data);
            const _configSavedMessage = event.data as KccSaveConfigWindowMessage;
            dispatch(saveConfig(_configSavedMessage));
            dispatch(setKccFocused(false));
            break;
          case 'CANCEL':
          case 'PROCESS_CANCEL':
            console.debug('KCC PROCESS CANCELED');
            // KASHEARTBE-1202
            // Found no other way to make the redirect use the newest value from kccRedirectUrl, probably related to eventlistener bullshit
            setIsKccCancel(true);
            break;
          default:
            console.log('Received Windowmessage (KASCC_NOTIFICATION) with unknown type', event.data);
        }
        break;
    }
  }
  // Create/Destroy window Message hook
  useEffect(() => {
    window.addEventListener('message', onWindowMessage);
    return () => {
      window.removeEventListener('message', onWindowMessage);
    };
  }, []);

  return (
    <>
      {stageConfig?.stage === 'dev' && (
        <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start' }}>
          <PButton
            data-e2e="openCoraModal"
            variant="primary"
            onClick={hideKccConfiguration}
            style={{ paddingRight: '10px' }}
          >
            {t('open_cora_modal')}
          </PButton>
          <PCheckboxWrapper label={t('use_kcc_dummy')}>
            <input
              type="checkbox"
              checked={useKccDummy}
              onChange={(e) => {
                console.debug('Change useKccDummyForm');
                setUseKccDummy(e.target.checked);
              }}
            ></input>
          </PCheckboxWrapper>
          <PDivider />
        </div>
      )}
      {showIframe && (
        <iframe title="KCC-Iframe" src={`${iframeUrl}?${kccUrlParams}`} style={style} id={kccIFrameId}></iframe>
      )}
    </>
  );
};
export default KccConfigurationTest;
