import React, { ChangeEvent, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PTextFieldWrapper, PTextareaWrapper } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { KccProxyInitialParams, getKccDefaultUrl } from './kccUtils';
import KccConfigurationTest from './KccConfigurationTest';

const dummyKccComponentRequestParams: KccProxyInitialParams = {
  meta_data: {
    caller_id: 'CORA',
    process: 'CO1',
    call_type: 'Konfiguration',
    PriceDate: '20241001000000',
    market_date: '20241001000000',
    context_datbase_date: '20241001000000',
    masterdata_environment: 'P',
    enableCustomerSelection: false,
    //  "user_permissions": "D100000001", --> werden im Proxy hart gesetzt, nur erster Buchstabe kommt BOSS
    configurator_language: 'pl',
  },
  order_info: {
    base_info: {
      quota_month: '2024-10',
    },
    trading_partner: {
      importer_number: '9690000',
      importer_code: 'DE',
      dealer_sold_to_number: '0000000',
    },
  },
  model_info: {
    model_type: '95BAN1',
    model_year: 2024,
    country_code: 'C00',
  },
};

export const kccIFrameId = 'KccConfigurationFrame';
const KccTestComponent: React.FC = () => {
  const { t } = useTranslation();

  const [kccUrl, setKccUrl] = useState<string>(getKccDefaultUrl());
  const [kccUrlIFrame, setKccUrlIFrame] = useState<string>();
  const [textFieldValue, setTextFieldValue] = useState<string>(JSON.stringify(dummyKccComponentRequestParams, null, 2));
  const [kccCreateParamsProps, setKccCreateParamsProps] = useState<KccProxyInitialParams>();

  function onTextAreaInput(event: ChangeEvent<HTMLTextAreaElement>): void {
    setTextFieldValue(event.target.value);
  }

  function onStartButtonClick(): void {
    setKccCreateParamsProps(JSON.parse(textFieldValue));
  }

  const customIframeUrlSelection = (
    <>
      <PTextFieldWrapper>
        <input type="text" value={kccUrl} onChange={(e) => setKccUrl(e.target.value)}></input>
      </PTextFieldWrapper>
      <PButton variant="primary" onClick={() => setKccUrlIFrame(kccUrl ? kccUrl : undefined)}>
        {t('Call Iframe with this domain')}
      </PButton>
      <PDivider />
    </>
  );

  return (
    <div>
      <PTextareaWrapper label="Cora Request Body" hideLabel={false}>
        <textarea onChange={onTextAreaInput} value={textFieldValue} name="some-name" />
      </PTextareaWrapper>
      <PButton onClick={onStartButtonClick}>Start KCC Workflow with this CORA Request Body</PButton>
      <PDivider />
      {kccCreateParamsProps && (
        <KccConfigurationTest
          kccInitRequestParams={kccCreateParamsProps}
          kccCancelDefaultRedirectUrl={`${window.location.origin}/test`}
        />
      )}
    </div>
  );
};
export default KccTestComponent;
