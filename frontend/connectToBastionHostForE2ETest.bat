@echo off
setlocal enabledelayedexpansion

rem Set AWS Region
set AWS_REGION=eu-west-1

echo Setting up SSM Port Forwarding for DB Connection...

rem Get Target Instance ID
for /f "delims=" %%i in ('aws ec2 describe-instances --query "Reservations[].Instances[0].InstanceId" --output text --region "!AWS_REGION!"') do set TARGET_INSTANCE_ID=%%i

rem Get RDS Cluster Writer Endpoint
for /f "delims=" %%i in ('aws rds describe-db-cluster-endpoints --query "DBClusterEndpoints[0].Endpoint" --output text --region "!AWS_REGION!"') do set RDS_CLUSTER_WRITER=%%i

rem Start SSM Session for Port Forwarding
start /b aws ssm start-session --target "!TARGET_INSTANCE_ID!" --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters "{\"host\":[\"!RDS_CLUSTER_WRITER!\"],\"portNumber\":[\"5432\"],\"localPortNumber\":[\"5432\"]}" --region "!AWS_REGION!"
