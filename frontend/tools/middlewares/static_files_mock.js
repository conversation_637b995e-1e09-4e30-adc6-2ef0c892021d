const suffixStornoReasons = '/storno.json';
const suffixStageConfig = '/config.json';

module.exports = function (req, res, next) {
  if (req.url.endsWith(suffixStornoReasons) && req.method === 'GET') {
    res.jsonp([
      {
        id: 'A1',
        beschreibung: 'Grund 1: weil er halt nicht will oder kann',
      },
      {
        id: 'A2',
        beschreibung: 'Grund 2: weil er halt nicht will oder kann',
      },
      {
        id: 'A3',
        beschreibung: 'Grund 3: weil er halt nicht will oder kann',
      },
      {
        id: 'A4',
        beschreibung: 'Grund 4: weil er halt nicht will oder kann',
      },
      {
        id: 'A5',
        beschreibung: 'Grund 5: weil er halt nicht will oder kann',
      },
      {
        id: 'A6',
        beschreibung: 'Grund 6: weil er halt nicht will oder kann',
      },
      {
        id: 'A7',
        beschreibung: 'Grund 7: weil er halt nicht will oder kann',
      },
      {
        id: 'A8',
        beschreibung: 'Grund 8: weil er halt nicht will oder kann',
      },
      {
        id: 'A9',
        beschreibung: 'Grund 9: weil er halt nicht will oder kann',
      },
    ]);
  } else if (req.url.endsWith(suffixStageConfig) && req.method === 'GET') {
    res.jsonp({
      stage: 'local',
      //change to anything else to test read access
      ppnRolesWrite: ['ppn_approle_boss_dev_basic_application_role'],
    });
  } else {
    next();
  }
};
