const prefixBuySellTransfer = '/special-actions/buy-sell-transfer';

module.exports = function buySellTransferMiddleware(req, res, next) {
  if (req.url.startsWith(prefixBuySellTransfer) && req.method === 'POST') {
    const { source_dealer_number, target_dealer_number, new_car_order_ids } = req.body;

    // Check if source or target dealer number is missing
    if (!source_dealer_number || !target_dealer_number) {
      return res.status(400).jsonp({
        message: 'Missing source or target dealer number',
      });
    }

    // Check if the number of orders is valid
    if (!Array.isArray(new_car_order_ids) || new_car_order_ids.length === 0) {
      return res.status(400).jsonp({
        message: 'Invalid number of orders',
      });
    }

    const numberOfOrders = new_car_order_ids.length;

    // Generate random IDs for the orders
    const generateRandomId = () =>
      `${source_dealer_number}-${target_dealer_number}-${Math.floor(Math.random() * 100000)}`;

    // Simulate a random failure with a 50% chance
    const isFailure = Math.random() < 0.5; // 50% chance of failure

    if (isFailure) {
      // Random failure: return an error message
      return res.status(500).jsonp({
        message: 'Random failure occurred during order ID generation',
      });
    }

    // If no failure, generate the order IDs
    const orderIds = Array.from({ length: numberOfOrders }, generateRandomId);

    // Return success with the generated order IDs
    return res.status(200).jsonp({
      new_car_order_ids: orderIds,
    });
  }

  // If the request doesn't match, proceed to the next middleware
  next();
};
