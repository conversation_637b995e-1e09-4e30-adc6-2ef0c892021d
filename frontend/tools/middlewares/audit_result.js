const prefixAudit = '/audit';
const fs = require('fs');
const path = require('path');

module.exports = function (req, res, next) {
  if (req.url.startsWith(prefixAudit) && req.method === 'GET') {
    fs.readFile(path.join(__dirname, '../files/auditResponse.json'), 'utf8', (err, data) => {
      // Commented is used for old audittrails in Prod
      // fs.readFile(path.join(__dirname, '../../../infrastructure/tools/old_audit/POP925553.json'), 'utf8', (err, data) => {
      // let d = JSON.parse(data)
      // d = d.map(e => {return {...e, new_nco: e.new_NewCarOrder, old_nco: e.old_NewCarOrder, action_at: e.new_NewCarOrder.modified_at, action_by: e.new_NewCarOrder.modified_by}})
      if (err) {
        return res.send.call(res, JSON.stringify({ message: 'Internal Server Error' }), 500);
      }
      // return res.send.call(res, JSON.stringify(d));
      return res.send.call(res, data);
    });
  } else {
    next();
  }
};
