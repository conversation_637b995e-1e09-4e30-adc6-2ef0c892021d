const fs = require('fs');
const path = require('path');
const prefix = '/new-car-order';
const prefixIds = '/new-car-order/new-car-order-ids';

module.exports = function (req, res, next) {
  if (req.method === 'GET' && req.url.startsWith(prefix)) {
    // Handle the new-car-order-ids specific route
    if (req.url.startsWith(prefixIds) && !!req.query) {
      fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
        if (err) {
          return res.status(500).jsonp({ message: 'Internal Server Error' });
        }

        try {
          let json = JSON.parse(data)['new-car-order'];
          const { dealer_number } = req.query;

          // Filter the data based on importer_number or dealer_number
          const filteredData = json.filter((item) => dealer_number && item.dealer_number === dealer_number);

          console.log(filteredData.map((item) => item.pk_new_car_order_id));
          if (filteredData.length > 0) {
            const newCarOrderIds = filteredData.map((item) => item.pk_new_car_order_id);
            return res.jsonp({ new_car_order_ids: newCarOrderIds }); // Return as an array of IDs
          } else {
            return res.status(404).jsonp([]); // Return an empty array if no records are found
          }
        } catch (e) {
          return res.status(500).jsonp({ message: 'Internal Server Error' });
        }
      });
      return; // Ensure no further processing of the request after response is sent
    }

    // Handle the general /new-car-order route
    fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
      if (err) {
        return res.status(500).jsonp({ message: 'Internal Server Error' });
      }

      try {
        let json = JSON.parse(data)['new-car-order'];
        const filters = req.query;

        const filteredData = json.filter((item) => {
          if (filters.order_status_onevms_code) {
            return item.order_status_onevms_code.includes(filters.order_status_onevms_code);
          } else if (filters.order_changeable) {
            return item.order_changeable === filters.order_changeable;
          } else if (filters.dealer_number) {
            return item.dealer_number === filters.dealer_number;
          }
        });

        if (filteredData.length > 0) {
          return res.jsonp(filteredData);
        } else {
          return res.status(404).jsonp({ message: 'No matching records found' });
        }
      } catch (e) {
        return res.status(500).jsonp({ message: 'Internal Server Error' });
      }
    });
    return; // Ensure no further processing of the request after response is sent
  }

  next(); // Move to the next middleware if this one doesn't handle the request
};
