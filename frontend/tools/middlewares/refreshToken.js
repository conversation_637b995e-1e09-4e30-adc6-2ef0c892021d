const jsonwebtoken = require('jsonwebtoken');
// mock API should set refresh_token
module.exports = function (req, res, next) {
  if (req.url === '/refresh_token') {
    const jwt = jsonwebtoken.sign(
      {
        family_name: '<PERSON><PERSON><PERSON>',
        given_name: '<PERSON>',
        email: '<EMAIL>',
        'cognito:groups': [],
        exp: new Date().getTime() / 1000 + 60 * 5,
      },
      'JWT_SECRET',
    );
    res.cookie('id_token', jwt, { httpOnly: false });
  }
  next();
};
