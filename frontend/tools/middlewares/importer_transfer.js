const prefixCopyOrders = '/new-car-order/importer-transfer';

module.exports = function (req, res, next) {
  if (req.url.startsWith(prefixCopyOrders) && req.method === 'POST') {
    const { new_car_order_ids, to_importer_number, to_dealer_number, new_nco_attributes } = req.body;

    // Mock response structure
    const response = new_car_order_ids.map((orderId) => {
      if (!to_importer_number || !to_dealer_number) {
        return {
          old_new_car_order_id: orderId,
          isSuccess: false,
          error_msg: 'Missing importer or dealer number',
        };
      }

      if (new_nco_attributes && Object.keys(new_nco_attributes).length === 0) {
        return {
          old_new_car_order_id: orderId,
          isSuccess: false,
          error_msg: 'Invalid attributes provided',
        };
      }

      const randomSuccess = () => Math.random() < 0.5; // 70% chance of success
      const isSuccess = randomSuccess();

      if (!isSuccess) {
        return {
          old_new_car_order_id: orderId,
          isSuccess: false,
          error_msg: 'Transfer failed due to system error',
        };
      }

      // Example success scenario
      return {
        old_new_car_order_id: orderId,
        new_new_car_order_id: `${orderId}-new`,
        isSuccess: true,
      };
    });

    res.jsonp(response);
  } else {
    next();
  }
};
