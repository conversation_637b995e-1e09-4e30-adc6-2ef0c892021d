const prefixReportTotalLoss = '/new-car-order/total-loss/report';
const prefixRevokeTotalLoss = '/new-car-order/total-loss/revoke';

module.exports = function (req, res, next) {
  if (
    (req.url.startsWith(prefixReportTotalLoss) || req.url.startsWith(prefixRevokeTotalLoss)) &&
    req.method === 'POST'
  ) {
    const totalLossResult = {};

    req.body.new_car_order_ids.forEach((ncoid) => {
      totalLossResult[ncoid] =
        ncoid === 'WRONGSTATUS1'
          ? 'order_action_wrong_status'
          : ncoid === 'TOOLONGAGO1'
            ? 'total_loss_too_long_ago'
            : ncoid === 'FORBIDDEN1'
              ? 'order_action_forbidden'
              : ncoid === 'NOTFOUND1'
                ? 'order_not_found'
                : ncoid === 'AURORAERROR1'
                  ? 'aurora_error'
                  : 'order_action_success';
    });

    res.jsonp(totalLossResult);
  } else {
    next();
  }
};
