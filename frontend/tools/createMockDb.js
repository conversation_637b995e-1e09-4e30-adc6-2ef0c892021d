/* eslint-disable no-console */
const fs = require('fs');
const path = require('path');
const mockData = require('./MockData.json');

const baseOrder = {
  dealer_number: 'ItNcoDlr',
  pk_new_car_order_id: 'MVAURORAERRO1',
  created_at: '2024-01-17T09:02:59.852Z',
  created_by: 'user_not_found',
  modified_at: '2024-01-17T09:02:59.852Z',
  modified_by: 'user_not_found',
  importer_code: 'PAD',
  importer_number: 'ItNcoImp',
  model_type: 'MT0003',
  model_year: '2030',
  order_status_onevms_code: 'OX1100',
  order_status_onevms_error_code: 'null',
  order_invoice_onevms_code: 'PI4000',
  order_type: 'ItOtBlackAllow',
  quota_month: '2023-11',
  receiving_port_code: 'ItNcoPc1',
  requested_dealer_delivery_date: undefined,
  shipping_code: '1',
  cnr: 'C21',
  business_partner_id: 'SomeOne',
};
// Generate some orders
let batchNr = 1;
for (const status of mockData['onevms-status']) {
  for (let i = 0; i < 40; i++) {
    const batchAsPadded = batchNr.toString().padStart(3, '0');
    mockData['new-car-order'].push({
      ...baseOrder,
      dealer_number: `SomeDlr${batchAsPadded}`,
      pk_new_car_order_id: `PAD${batchAsPadded}${i.toString().padStart(3, '0')}`,
      importer_number: `SomeImp${batchAsPadded}`,
      order_status_onevms_code: status.one_vms_status,
      order_status_onevms_error_code: status.one_vms_error_status,
    });
  }
  batchNr++;
}
const data = JSON.stringify({ ...mockData, refresh_token: [{ id: 1 }] });
const filepath = path.join(__dirname, 'db.json');

fs.writeFile(filepath, data, function (err) {
  err ? console.log(err) : console.log('Mock DB created.');
});
