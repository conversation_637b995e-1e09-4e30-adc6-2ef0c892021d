{"dealer": [{"sk_dealer_number": "9520011", "code": "DLR1", "display_name": "Dealer 1 Imp 1", "pk_importer_number": "9690000", "standard_port_code": "EMD", "alternative_port_codes": []}, {"sk_dealer_number": "9520012", "code": "DLR21", "display_name": "Dealer 2 Imp 1", "pk_importer_number": "9690000", "standard_port_code": "EMD", "alternative_port_codes": ["ABC"]}, {"sk_dealer_number": "9520012", "code": "DLR22", "display_name": "Dealer 2 Imp 2", "pk_importer_number": "9690002", "standard_port_code": "EMD", "alternative_port_codes": ["ABC"]}, {"sk_dealer_number": "9520013", "code": "DLR3", "display_name": "Dealer 3 Imp 4", "pk_importer_number": "9690004", "standard_port_code": "ABC"}, {"sk_dealer_number": "9520014", "code": "DLR4", "display_name": "Dealer 4 Imp 4", "pk_importer_number": "9690004", "standard_port_code": "ABC"}, {"sk_dealer_number": "9520015", "code": "DLR5", "display_name": "Dealer 5 Imp 4", "pk_importer_number": "9690004"}, {"sk_dealer_number": "9520016", "code": "DLR6", "display_name": "Dealer 6 Imp 5", "pk_importer_number": "9690005"}], "order-type": [{"pk_order_type": "LF", "description": "Lagerfahrzeug", "dlr_is_visible": true, "imp_is_blacklist": true, "importers": ["9690003"], "is_deactivated": false, "is_customer_related": false, "created_at": "2023-09-05T12:00:00Z", "created_by": "user123", "modified_at": "2023-09-05T12:30:00Z", "modified_by": "user456", "editable": true}, {"pk_order_type": "KF", "description": "Kundenfahrzeug", "dlr_is_visible": true, "imp_is_blacklist": false, "importers": ["9690003"], "is_deactivated": false, "is_customer_related": true, "created_at": "2023-09-05T13:00:00Z", "created_by": "user789", "modified_at": "2023-09-05T13:20:00Z", "modified_by": "user123", "editable": true}, {"pk_order_type": "MF", "description": "Messefahrzeug", "dlr_is_visible": true, "imp_is_blacklist": false, "importers": ["9690003"], "is_deactivated": false, "created_at": "2023-09-05T13:00:00Z", "created_by": "user987", "modified_at": "2023-09-05T13:20:00Z", "modified_by": "user123", "editable": false}, {"pk_order_type": "VF", "description": "Vorführfahrzeug", "dlr_is_visible": true, "imp_is_blacklist": false, "importers": ["9690003"], "is_deactivated": false, "created_at": "2023-09-05T13:00:00Z", "created_by": "user987", "modified_at": "2023-09-05T13:20:00Z", "modified_by": "user123", "editable": true}], "port-code": [{"pk_port_code": "EMD", "created_at": "2023-08-09T07:25:43.598Z", "is_deactivated": false, "standard": true, "created_by": "schwellnusfr", "modified_by": null, "display_name": "<PERSON><PERSON>", "modified_at": null}, {"pk_port_code": "ABC", "created_at": "2023-08-09T07:26:10.759Z", "is_deactivated": false, "created_by": "schwellnusfr", "modified_by": null, "display_name": "AaBbCc", "modified_at": null}, {"pk_port_code": "DEA", "created_at": "2023-08-09T07:26:10.759Z", "is_deactivated": true, "created_by": "schwellnusfr", "modified_by": null, "display_name": "<PERSON><PERSON><PERSON><PERSON>", "modified_at": null}, {"pk_port_code": "XYZ", "created_at": "2023-08-09T07:26:10.759Z", "is_deactivated": false, "created_by": "schwellnusfr", "modified_by": null, "display_name": "XxYyZz", "modified_at": null}], "shipping-code": [{"pk_shipping_code": "5", "description": "Werksabholung", "dlr_is_blacklist": true, "dealers": ["9520011", "9520012"], "imp_is_blacklist": false, "importers": [], "is_deactivated": false, "created_at": "2020-01-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-27T06:00:00Z", "modified_by": "<PERSON>", "editable": true}, {"pk_shipping_code": "6", "description": "Schiffkaperung", "dlr_is_blacklist": true, "dealers": [], "imp_is_blacklist": false, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>", "editable": true}, {"pk_shipping_code": "7", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": false, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>", "editable": true}, {"pk_shipping_code": "8", "description": "Airfreight", "dlr_is_blacklist": true, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>", "editable": false}, {"pk_shipping_code": "9", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}, {"pk_shipping_code": "10", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}, {"pk_shipping_code": "11", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}, {"pk_shipping_code": "12", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}, {"pk_shipping_code": "13", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}, {"pk_shipping_code": "14", "description": "Schiffkaperung", "dlr_is_blacklist": false, "dealers": [], "imp_is_blacklist": true, "importers": ["9690001", "9690003"], "is_deactivated": false, "created_at": "2020-02-27T06:00:00Z", "created_by": "<PERSON>", "modified_at": "2020-01-22T12:00:00Z", "modified_by": "<PERSON>"}], "importer": [{"id": "9690001", "pk_importer_number": "9690001", "code": "DE", "port_codes": ["DEA"], "modified_by": "user_not_found", "display_name": "Imp 1", "modified_at": "2023-08-08T11:26:04.345Z"}, {"id": "9690002", "pk_importer_number": "9690002", "code": "", "modified_by": "boss_sync", "display_name": "Imp 2", "modified_at": "2023-08-01T12:13:04.434Z"}, {"id": "9690003", "pk_importer_number": "9690003", "code": "", "port_codes": ["EMD", "ABC"], "modified_by": "fzrza9z", "display_name": "Imp 3", "modified_at": "2023-08-10T13:29:25.513Z"}, {"id": "9690004", "pk_importer_number": "9690004", "code": "", "port_codes": ["ABC", "XYZ", "DEA"], "modified_by": "fzrza9z", "display_name": "Imp 4", "modified_at": "2023-08-10T13:29:25.513Z"}, {"id": "9690005", "pk_importer_number": "9690005", "code": "", "modified_by": "fzrza9z", "display_name": "Imp 5", "modified_at": "2023-08-10T13:29:25.513Z"}], "model-type": [{"importer_number_role": "9690003_IMP", "model_type_my4_cnr": "982310_2022_C00", "importer_number": "9690003", "cnr": "C00", "model_type": "982310", "my4": 2022}, {"importer_number_role": "9690001_IMP", "model_type_my4_cnr": "982310_2024_C00", "importer_number": "9690001", "cnr": "C00", "model_type": "982310", "my4": 2024}, {"importer_number_role": "9690002_IMP", "model_type_my4_cnr": "982310_2024_C00", "importer_number": "9690002", "cnr": "C00", "model_type": "982310", "my4": 2024}, {"importer_number_role": "9690004_IMP", "model_type_my4_cnr": "982310_2024_C00", "importer_number": "9690004", "cnr": "C00", "model_type": "982310", "my4": 2024}, {"importer_number_role": "9690005_IMP", "model_type_my4_cnr": "982310_2024_C00", "importer_number": "9690005", "cnr": "C00", "model_type": "982310", "my4": 2024}, {"importer_number_role": "9690003_IMP", "model_type_my4_cnr": "982310_2023_C00", "importer_number": "9690003", "cnr": "C00", "model_type": "982310", "my4": 2023}, {"importer_number_role": "9690003_IMP", "model_type_my4_cnr": "982310_2024_C00", "importer_number": "9690003", "cnr": "C00", "model_type": "982310", "my4": 2024}, {"importer_number_role": "9690003_IMP", "model_type_my4_cnr": "982311_2030_C01", "importer_number": "9690003", "cnr": "C01", "model_type": "982311", "my4": 2030}, {"importer_number_role": "9690003_IMP", "model_type_my4_cnr": "982311_2030_C02", "importer_number": "9690003", "cnr": "C02", "model_type": "982311", "my4": 2030}, {"importer_number_role": "5000000_IMP", "model_type_my4_cnr": "5000000_2030_C02", "importer_number": "5000000", "cnr": "C02", "model_type": "982311", "my4": 2030}], "authorized-dealer": [{"dealer_number": "9520011", "display_name": "Dealer 1 Imp 1", "importer_number": "9690000", "role": "Dealer", "importer_display_name": "Imp 1"}, {"dealer_number": "9520012", "display_name": "Dealer 2 Imp 1", "importer_number": "9690000", "role": "Dealer", "importer_display_name": "Imp 1"}, {"dealer_number": "9520012", "display_name": "Dealer 2 Imp 2", "importer_number": "9690002", "role": "Dealer", "importer_display_name": "Imp 2"}, {"dealer_number": "9520013", "display_name": "Imp 3", "importer_number": "9690003", "role": "Importer", "importer_display_name": "Imp 3"}, {"dealer_number": "9520012", "display_name": "Imp 1", "importer_number": "9690000", "role": "Importer", "importer_display_name": "Imp 1"}, {"dealer_number": "9520012", "display_name": "Imp 1", "importer_number": "9690000", "role": "Importer", "importer_display_name": "Imp 1"}, {"dealer_number": "9520015", "display_name": "Dealer 5 Imp 4", "importer_number": "9690004", "role": "Dealer", "importer_display_name": "Imp 4"}, {"dealer_number": "9520012", "display_name": "Dealer 5 Imp 3", "importer_number": "9690003", "role": "Dealer", "importer_display_name": "Imp 3"}, {"dealer_number": "9520012", "display_name": "Dealer 6 Imp 3", "importer_number": "9690003", "role": "Dealer", "importer_display_name": "Imp 3"}], "oc-dealer-importer": [{"id": "9520011", "dealer_number": "9520011", "dealer": {"id": "9520011", "sk_dealer_number": "9520011", "code": "DLR1", "display_name": "Dealer 1 Imp 1", "pk_importer_number": "9690001", "standard_port_code": "EMD", "alternative_port_codes": []}, "importer": {"id": "9690000", "pk_importer_number": "9690000", "code": "DE", "port_codes": ["DEA"], "modified_by": "user_not_found", "display_name": "Imp 1", "modified_at": "2023-08-08T11:26:04.345Z"}}, {"id": "9520012", "dealer_number": "9520012", "dealer": {"id": "9520012", "sk_dealer_number": "9520012", "code": "DLR12", "display_name": "Dealer 2 Imp 2", "pk_importer_number": "9690002", "standard_port_code": "EMD", "alternative_port_codes": ["ABC"]}, "importer": {"id": "9690002", "pk_importer_number": "9690002", "code": "", "modified_by": "boss_sync", "display_name": "Imp 2", "modified_at": "2023-08-01T12:13:04.434Z"}}, {"id": "9520013", "dealer_number": "9520013", "dealer": {"id": "9520013", "sk_dealer_number": "9520013", "code": "DLR3", "display_name": "Dealer 3 Imp 3", "pk_importer_number": "9690003", "standard_port_code": "ABC"}, "importer": {"id": "9690003", "pk_importer_number": "9690003", "code": "", "port_codes": ["EMD", "ABC"], "modified_by": "fzrza9z", "display_name": "Imp 3", "modified_at": "2023-08-10T13:29:25.513Z"}}, {"id": "9520015", "dealer_number": "9520015", "dealer": {"id": "9520015", "sk_dealer_number": "9520015", "code": "DLR3", "display_name": "Dealer 5 Imp 4", "pk_importer_number": "9690004", "standard_port_code": "ABC"}, "importer": {"id": "9690004", "pk_importer_number": "9690004", "code": "", "port_codes": ["ABC", "XYZ", "DEA"], "modified_by": "fzrza9z", "display_name": "Imp 4", "modified_at": "2023-08-10T13:29:25.513Z"}}], "quota": [{"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690001, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "982310", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "**********-9520011-982310-2024-202306", "quota_month": "2024-08", "quota_open": 2}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "MT0001", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid001", "quota_month": "2024-08", "quota_open": 4}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "9YBAV1", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid001", "quota_month": "2025-02", "quota_open": 4}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "MT0001", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid002", "quota_month": "2025-01", "quota_open": 120}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "MT0001", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid002", "quota_month": "2025-02", "quota_open": 70}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520012, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "MT0001", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid002", "quota_month": "2025-05", "quota_open": 15}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690001, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "982310", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "**********-9520011-982310-2024-202307", "quota_month": "2024-11", "quota_open": 2}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 5000001, "importer_number": 5000000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "992110", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "**********-9520011-982311-2024-202307", "quota_month": "2024-11", "quota_open": 2}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690000, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "MT0001", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "quotaid002", "quota_month": "2024-12", "quota_open": 7}, {"consumed_new_car_order_ids": [], "created_at": "2023-06-06T10:52:21.645203", "created_by": "KAFKA_PRODUCER_QUOTAMODULE", "dealer_number": 9520011, "importer_number": 9690001, "lastModified_at": "2023-06-06T10:52:21.646246", "lastModified_by": "KAFKA_PRODUCER_QUOTAMODULE", "model_type": "982310", "model_year": 2024, "quota_consumed": 0, "quota_count": 2, "quota_id": "**********-9520011-982310-2025-202307", "quota_month": "2025-09", "quota_open": 2}], "new-car-order": [{"dealer_number": "9520011", "pk_new_car_order_id": "IT2JX52B", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "9690000", "model_type": "MT0001", "model_year": "2024", "order_status_onevms_code": "PP1300", "order_status_onevms_error_code": "FBP500", "order_invoice_onevms_code": "PI0000", "order_type": "KF", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "9520011", "pk_new_car_order_id": "IT2JX53C", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "9690000", "model_type": "MT0001", "model_year": "2024", "order_status_onevms_code": "PP2000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "null", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "IT2JX52C", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "false"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MOVETO01", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MOVETO02", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MVWRONG1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MVFORBI1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MVNOTFOUND1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "MVAURORAERRO1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI4000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "REMOVEO1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "MI9000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "REMOVEO2", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "MI9000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "RMAURORAERRO1", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "MI9000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "TTL2024", "dealer_name": "Total Loss Dealer", "pk_new_car_order_id": "TOT19823", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "MT0003", "model_year": "2030", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "", "shipping_code": "8", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "<PERSON><PERSON><PERSON><PERSON>", "pk_new_car_order_id": "DE0ZR0R8", "created_at": "2024-01-19T09:54:34.289Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "MT0001", "model_year": "2024", "order_status_onevms_code": "PP2000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "MF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "7", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "order_changeable": "true", "configuration": {"ordered_options": [{"option_id": "zusatzoption2", "option_type": "sometype", "option_validity": {"added_to_order_timestamp": "2024-07-31", "material_lead_time": "80"}}]}, "business_partner_id": "ItNcoBp1"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "DE1WFA1R", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "IP0000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "WRONGSTATUS1", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "ID0000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "TOOLONGAGO1", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "FORBIDDEN1", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "NOTFOUND1", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_status_onevms_timestamp_last_change": "2024-01-19T12:49:51.239Z", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "9520011", "dealer_name": "", "pk_new_car_order_id": "AURORAERROR1", "created_at": "2024-01-19T12:49:51.239Z", "created_by": "kastechcora2", "importer_code": "DE", "importer_number": "9690000", "model_type": "9YBAV1", "model_year": "2024", "order_status_onevms_code": "OX1100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-01", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "8", "cnr": "C01", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "configuration_expire": {"id": "pvms_dummy_config"}, "order_changeable": "false"}, {"dealer_number": "ItNcoDlr", "pk_new_car_order_id": "ITXXXAAA", "created_at": "2024-01-17T09:02:59.852Z", "created_by": "user_not_found", "importer_code": "IT", "importer_number": "ItNcoImp", "model_type": "9YBCZ1", "model_year": "2030", "order_status_onevms_code": "PPXXXX", "order_status_onevms_error_code": "FBXXXX", "order_invoice_onevms_code": "PI0000", "order_type": "ItOtBlackAllow", "quota_month": "2023-11", "receiving_port_code": "ItNcoPc1", "requested_dealer_delivery_date": "2024-01-01", "shipping_code": "9", "cnr": "C21", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItNcoBp1", "order_changeable": "true"}, {"dealer_number": "ItNewDealer01", "pk_new_car_order_id": "IT2JX53A", "created_at": "2024-02-15T10:12:59.852Z", "created_by": "user_alternate", "importer_code": "IT", "importer_number": "ItNewImp01", "model_type": "MT1234", "model_year": "2025", "order_status_onevms_code": "ID4100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "KF", "quota_month": "2023-12", "receiving_port_code": "ItPort1", "requested_dealer_delivery_date": "2024-02-20", "shipping_code": "5", "cnr": "C22", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItBpNew01", "order_changeable": "false"}, {"dealer_number": "DEDealer001", "dealer_name": "AutoWorld GmbH", "pk_new_car_order_id": "DE0YR2K1", "created_at": "2024-02-05T09:54:34.289Z", "created_by": "auto_admin", "importer_code": "DE", "importer_number": "9699999", "model_type": "MT9999", "model_year": "2023", "order_status_onevms_code": "ID4100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "MF", "quota_month": "2023-08", "requested_dealer_delivery_date": "2024-02-12", "shipping_code": "6", "cnr": "C03", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "order_changeable": "false", "configuration": {"ordered_options": [{"option_id": "uniqueoption1", "option_type": "specialtype", "option_validity": {"added_to_order_timestamp": "2024-08-20", "material_lead_time": "45"}}]}, "business_partner_id": "BpID12345"}, {"dealer_number": "DEDealerNew", "dealer_name": "AutoElite", "pk_new_car_order_id": "DE2XK45J", "created_at": "2024-03-05T12:49:51.239Z", "created_by": "system_user", "importer_code": "DE", "importer_number": "9698888", "model_type": "9ZBAU3", "model_year": "2026", "order_status_onevms_code": "IP0000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-06", "requested_dealer_delivery_date": "2024-03-10", "shipping_code": "9", "cnr": "C10", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ElitePartner01", "order_changeable": "false", "configuration_expire": {"id": "elite_config_expire"}}, {"dealer_number": "ItDealerUpdated", "pk_new_car_order_id": "ITXBB45J", "created_at": "2024-02-25T09:02:59.852Z", "created_by": "admin_user", "importer_code": "IT", "importer_number": "ItImpUpdated", "model_type": "9YBCZ2", "model_year": "2031", "order_status_onevms_code": "ID5000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-09", "receiving_port_code": "ItNcoPc2", "requested_dealer_delivery_date": "2024-02-25", "shipping_code": "3", "cnr": "C15", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "NewItBp02", "order_changeable": "false"}, {"dealer_number": "ItDealer123", "pk_new_car_order_id": "IT678PQR", "created_at": "2024-02-10T11:00:59.852Z", "created_by": "super_admin", "importer_code": "IT", "importer_number": "ItNewImporter", "model_type": "MT4567", "model_year": "2029", "order_status_onevms_code": "ID4100", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-07", "receiving_port_code": "ItPortUpdated", "requested_dealer_delivery_date": "2024-02-10", "shipping_code": "2", "cnr": "C27", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItPartnerNew02", "order_changeable": "true"}, {"dealer_number": "IT2022Dlr", "dealer_name": "Italia Motors", "pk_new_car_order_id": "IT223344", "created_at": "2024-01-19T15:30:00.000Z", "created_by": "staff_member", "importer_code": "IT", "importer_number": "ItImp2233", "model_type": "MT2022", "model_year": "2024", "order_status_onevms_code": "ID5000", "order_status_onevms_error_code": "null", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": "2023-05", "receiving_port_code": "ItPortNew", "requested_dealer_delivery_date": "2024-01-25", "shipping_code": "1", "cnr": "C08", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItBusPartner09", "order_changeable": "false"}, {"dealer_number": "IT2022Dlr", "dealer_name": "Italia Motors", "pk_new_car_order_id": "NO_QUOTA", "created_at": "2024-01-19T15:30:00.000Z", "created_by": "stiflers_mom", "importer_code": "NO", "importer_number": "ItImp2233", "model_type": "MT2022", "model_year": "2024", "order_status_onevms_code": "PP1300", "order_status_onevms_error_code": "FBP500", "order_invoice_onevms_code": "PI0000", "order_type": "LF", "quota_month": null, "receiving_port_code": "ItPortNew", "requested_dealer_delivery_date": "2024-01-25", "shipping_code": "1", "cnr": "C08", "customer_name": "<PERSON><PERSON><PERSON><PERSON>", "business_partner_id": "ItBusPartner09", "order_changeable": "true"}], "fetch-failed-order-data": [{"object_id": "obj_123", "key": "order_001", "numOfTries": 3, "timestamp": "2024-08-09T10:15:30Z", "value": {"ids": {"new_car_order_id": "new_order_123"}, "order_info": {"status_info": {"vehicle_status_pvms_code": "VS001"}}}}, {"object_id": "obj_124", "key": "order_002", "numOfTries": 2, "timestamp": "2024-08-10T12:20:45Z", "value": {"ids": {"new_car_order_id": "new_order_124"}, "order_info": {"status_info": {"vehicle_status_pvms_code": "VS002"}}}}, {"object_id": "obj_125", "key": "order_003", "numOfTries": 4, "timestamp": "2024-08-11T14:35:50Z", "value": {"ids": {"new_car_order_id": "new_order_125"}, "order_info": {"status_info": {"vehicle_status_pvms_code": "VS003"}}}}, {"object_id": "obj_126", "key": "order_004", "numOfTries": 1, "timestamp": "2024-08-12T16:40:55Z", "value": {"ids": {"new_car_order_id": "new_order_126"}, "order_info": {"status_info": {"vehicle_status_pvms_code": "VS004"}}}}, {"object_id": "obj_127", "key": "order_005", "numOfTries": 5, "timestamp": "2024-08-13T18:45:00Z", "value": {"ids": {"new_car_order_id": "new_order_127"}, "order_info": {"status_info": {"vehicle_status_pvms_code": "VS005"}}}}], "purchase-intention": [{"purchase_intention_id": "CH755142", "order_type": "KF", "shipping_code": "5", "model_type": "992110", "model_year": "2024", "quota_month": "2024-02", "dealer_number": "5000001", "importer_number": "5000000", "seller": "300822", "business_partner_id": "0*********", "receiving_port_code": "", "vehicle_status_code": "V070", "vehicle_configuration_pvmsnext": {"PriceDate": "20240227120000", "Cnr": "C10", "XTraceId": "", "CommissionNumber": "755142", "Importerid": "CH", "VehicleTag": {"results": [{"__metadata": {"type": "YISA8034_CONFIG_SRV.tag", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')"}, "Value": "PCCDP", "Key": "KB"}]}, "VehicleWLTPHeader": {"results": [{"error_message": "", "response_date": "/Date(1709036362000)/", "check_date": "/Date(1708992000000)/", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltp_header", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5',DataSource='P')"}, "uuid": "85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5", "return_code": "200", "DataSource": "P"}]}, "InteriorColor": "AE", "ConfigName": "240215_R992110_ITALIANOTEST", "PpnUserId": "", "Currency": "CHF", "TopColor": "0Q", "LeadId": "0000858791", "__metadata": {"type": "YISA8034_CONFIG_SRV.Vehicle", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwpWMZin1jRO0')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwpWMZin1jRO0')"}, "IsDealerConfig": "", "CurrentMarketDate": "20240514175543", "ExteriorColor": "0Q", "SapUserId": "", "TotalPriceGross": "137600.00", "VehicleWLTPBody": {"results": [{"EngineType": "ICE", "WLTPBodyWLTPBodyRecord": {"results": [{"DataType": "GENERAL_DATA", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "0.603", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')"}, "Unit": "m2", "Key": "AERODYNAMIC_DRAG", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG_H',DataSource='P')"}, "Unit": "m2", "Key": "AERODYNAMIC_DRAG_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT',DataSource='P')"}, "Unit": "", "Key": "DRAG_COEFFICIENT", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT_H',DataSource='P')"}, "Unit": "", "Key": "DRAG_COEFFICIENT_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1735.0", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')"}, "Unit": "kg", "Key": "EU_LEER_MAX", "DataSource": "P"}, {"ValueFrom": "", "Value": "1580.0", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')"}, "Unit": "kg", "Key": "EU_LEER_MIN", "DataSource": "P"}, {"ValueFrom": "", "Value": "2.07", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')"}, "Unit": "m2", "Key": "FRONT_SURFACE_A", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A_H',DataSource='P')"}, "Unit": "m2", "Key": "FRONT_SURFACE_A_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH_TYPING',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH_TYPING',DataSource='P')"}, "Unit": "", "Key": "HIGH_TYPING", "DataSource": "P"}, {"ValueFrom": "", "Value": "1590", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')"}, "Unit": "kg", "Key": "MASS_ACTUAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "1515", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "555", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "960", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "1645", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')"}, "Unit": "kg", "Key": "TEST_MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE_H',DataSource='P')"}, "Unit": "kg", "Key": "TEST_MASS_VEHICLE_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_TOTAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "186.2", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')"}, "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0_H',DataSource='P')"}, "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1.85", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')"}, "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1_H',DataSource='P')"}, "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "0.02185", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')"}, "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2_H',DataSource='P')"}, "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2_H", "DataSource": "P"}]}, "EnergyManagementType": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')"}, "ValueType": "", "FuelType": "", "DataSource": "P"}]}, "DataSource": "P"}, {"DataType": "INTERPOLATIONS", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "232", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')"}, "Unit": "g/km", "Key": "COMBINED", "DataSource": "P"}, {"ValueFrom": "", "Value": "200", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')"}, "Unit": "g/km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "195", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')"}, "Unit": "g/km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "417", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')"}, "Unit": "g/km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "225", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')"}, "Unit": "g/km", "Key": "MEDIUM", "DataSource": "P"}]}, "EnergyManagementType": "PURE", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')"}, "ValueType": "CO2", "FuelType": "PETROL_E10", "DataSource": "P"}, {"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "10.2", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')"}, "Unit": "l/100km", "Key": "COMBINED", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')"}, "Unit": "l/100km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.6", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')"}, "Unit": "l/100km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "18.4", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')"}, "Unit": "l/100km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.9", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')"}, "Unit": "l/100km", "Key": "MEDIUM", "DataSource": "P"}]}, "EnergyManagementType": "PURE", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')"}, "ValueType": "CONSUMPTION", "FuelType": "PETROL_E10", "DataSource": "P"}]}, "DataSource": "P"}, {"DataType": "IP_FAMILY", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "IP-AP992002B00AT00-WP0-1", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')"}, "Unit": "0/00", "Key": "NAME", "DataSource": "P"}]}, "EnergyManagementType": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')"}, "ValueType": "", "FuelType": "", "DataSource": "P"}]}, "DataSource": "P"}]}, "Typification": "WLTP_EU", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltp_body", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')"}, "DataSource": "P"}]}, "ConfigModified": "", "CofferId": "aa36ae93-1053-465d-85be-79dcf211fef5", "Modeltype": "992110", "Modelyear": "R", "PorscheCode": "PRP1SJT0", "CommentsModified": "", "Importernr": "5000000", "Dealernr": "5000001", "ProspectKit": true, "Vguid": "051Mkmoc7jwpWMZin1jRO0", "Taxes": "10310.45", "TeqId": "0", "BpId": "*********", "FreeZOffer": "", "Vin": "", "Id": "", "ProductionDate": "0000-00-00", "VehicleOption": {"OptionExclusive": {"results": []}, "OptionPackage": {"results": []}, "OptionIndividual": {"results": [{"ImporterComment": "", "PagComment": "", "SortOrder": "", "Id": "EB1", "UpgradeTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.Individual", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('EB1')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('EB1')"}, "DealerComment": "", "ContentOf": ""}, {"ImporterComment": "", "PagComment": "", "SortOrder": "", "Id": "SZ8", "UpgradeTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.Individual", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('SZ8')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('SZ8')"}, "DealerComment": "", "ContentOf": ""}]}, "OptionLocal": {"results": []}, "Id": "1", "__metadata": {"type": "YISA8034_CONFIG_SRV.Option", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')"}, "OptionCustomTailoring": {"results": []}, "OptionZoffer": {"results": []}}, "VehicleStatus": "V100", "LastUpdateDate": "20240227121952"}, "vehicle_configuration_onevms": null, "requested_dealer_delivery_date": null, "cnr": "C10", "importer_code": "CH", "dealer_name": "Porsche Zentrum Schinznach-Bad", "modified_at": "2020-01-22T12:00:00Z", "created_by": "pvms"}, {"purchase_intention_id": "TEST01234", "order_type": "KF", "shipping_code": "6", "model_type": "992110", "model_year": "2024", "quota_month": "2024-02", "dealer_number": "5000001", "importer_number": "5000000", "seller": "300822", "business_partner_id": "0*********", "receiving_port_code": "", "vehicle_status_code": "V070", "vehicle_configuration_pvmsnext": {"PriceDate": "20240227120000", "Cnr": "C10", "XTraceId": "", "CommissionNumber": "755142", "Importerid": "CH", "VehicleTag": {"results": [{"__metadata": {"type": "YISA8034_CONFIG_SRV.tag", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/tagSet('KB')"}, "Value": "PCCDP", "Key": "KB"}]}, "VehicleWLTPHeader": {"results": [{"error_message": "", "response_date": "/Date(1709036362000)/", "check_date": "/Date(1708992000000)/", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltp_header", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_headerSet(uuid='85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5',DataSource='P')"}, "uuid": "85ba2517-3d00-4638-b5e8-b9e5fdc9aeb5", "return_code": "200", "DataSource": "P"}]}, "InteriorColor": "AE", "ConfigName": "240215_R992110_ITALIANOTEST", "PpnUserId": "", "Currency": "CHF", "TopColor": "0Q", "LeadId": "0000858791", "__metadata": {"type": "YISA8034_CONFIG_SRV.Vehicle", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwpWMZin1jRO0')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/VehicleSet('051Mkmoc7jwpWMZin1jRO0')"}, "IsDealerConfig": "", "CurrentMarketDate": "20240514175543", "ExteriorColor": "0Q", "SapUserId": "", "TotalPriceGross": "137600.00", "VehicleWLTPBody": {"results": [{"EngineType": "ICE", "WLTPBodyWLTPBodyRecord": {"results": [{"DataType": "GENERAL_DATA", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='GENERAL_DATA',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "0.603", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG',DataSource='P')"}, "Unit": "m2", "Key": "AERODYNAMIC_DRAG", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='AERODYNAMIC_DRAG_H',DataSource='P')"}, "Unit": "m2", "Key": "AERODYNAMIC_DRAG_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT',DataSource='P')"}, "Unit": "", "Key": "DRAG_COEFFICIENT", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='DRAG_COEFFICIENT_H',DataSource='P')"}, "Unit": "", "Key": "DRAG_COEFFICIENT_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1735.0", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MAX',DataSource='P')"}, "Unit": "kg", "Key": "EU_LEER_MAX", "DataSource": "P"}, {"ValueFrom": "", "Value": "1580.0", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EU_LEER_MIN',DataSource='P')"}, "Unit": "kg", "Key": "EU_LEER_MIN", "DataSource": "P"}, {"ValueFrom": "", "Value": "2.07", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A',DataSource='P')"}, "Unit": "m2", "Key": "FRONT_SURFACE_A", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='FRONT_SURFACE_A_H',DataSource='P')"}, "Unit": "m2", "Key": "FRONT_SURFACE_A_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH_TYPING',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH_TYPING',DataSource='P')"}, "Unit": "", "Key": "HIGH_TYPING", "DataSource": "P"}, {"ValueFrom": "", "Value": "1590", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_ACTUAL',DataSource='P')"}, "Unit": "kg", "Key": "MASS_ACTUAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "1515", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "555", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_FRONT',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "960", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MASS_VEHICLE_REAR',DataSource='P')"}, "Unit": "kg", "Key": "MASS_VEHICLE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "1645", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE',DataSource='P')"}, "Unit": "kg", "Key": "TEST_MASS_VEHICLE", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TEST_MASS_VEHICLE_H',DataSource='P')"}, "Unit": "kg", "Key": "TEST_MASS_VEHICLE_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_FRONT',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_FRONT", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_REAR',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_REAR", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='TIRE_ROLLING_RESISTANCE_TOTAL',DataSource='P')"}, "Unit": "0/00", "Key": "TIRE_ROLLING_RESISTANCE_TOTAL", "DataSource": "P"}, {"ValueFrom": "", "Value": "186.2", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0',DataSource='P')"}, "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F0_H',DataSource='P')"}, "Unit": "N", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F0_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "1.85", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1',DataSource='P')"}, "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F1_H',DataSource='P')"}, "Unit": "N/(km/h)", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F1_H", "DataSource": "P"}, {"ValueFrom": "", "Value": "0.02185", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2',DataSource='P')"}, "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2", "DataSource": "P"}, {"ValueFrom": "", "Value": "", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2_H',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='VEHICLE_RESISTANCE_COEFFICENT_F2_H',DataSource='P')"}, "Unit": "N/(km/h)2", "Key": "VEHICLE_RESISTANCE_COEFFICENT_F2_H", "DataSource": "P"}]}, "EnergyManagementType": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')"}, "ValueType": "", "FuelType": "", "DataSource": "P"}]}, "DataSource": "P"}, {"DataType": "INTERPOLATIONS", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='INTERPOLATIONS',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "232", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')"}, "Unit": "g/km", "Key": "COMBINED", "DataSource": "P"}, {"ValueFrom": "", "Value": "200", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')"}, "Unit": "g/km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "195", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')"}, "Unit": "g/km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "417", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')"}, "Unit": "g/km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "225", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')"}, "Unit": "g/km", "Key": "MEDIUM", "DataSource": "P"}]}, "EnergyManagementType": "PURE", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CO2',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')"}, "ValueType": "CO2", "FuelType": "PETROL_E10", "DataSource": "P"}, {"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "10.2", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='COMBINED',DataSource='P')"}, "Unit": "l/100km", "Key": "COMBINED", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.8", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='EXTRA_HIGH',DataSource='P')"}, "Unit": "l/100km", "Key": "EXTRA_HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "8.6", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='HIGH',DataSource='P')"}, "Unit": "l/100km", "Key": "HIGH", "DataSource": "P"}, {"ValueFrom": "", "Value": "18.4", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='LOW',DataSource='P')"}, "Unit": "l/100km", "Key": "LOW", "DataSource": "P"}, {"ValueFrom": "", "Value": "9.9", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='MEDIUM',DataSource='P')"}, "Unit": "l/100km", "Key": "MEDIUM", "DataSource": "P"}]}, "EnergyManagementType": "PURE", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='CONSUMPTION',FuelType='PETROL_E10',EnergyManagementType='PURE',DataSource='P')"}, "ValueType": "CONSUMPTION", "FuelType": "PETROL_E10", "DataSource": "P"}]}, "DataSource": "P"}, {"DataType": "IP_FAMILY", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpBodyRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpBodyRecordSet(DataType='IP_FAMILY',DataSource='P')"}, "WLTPBodyRecordWLTPDataRecord": {"results": [{"WLTPDataRecordWLTPValue": {"results": [{"ValueFrom": "", "Value": "IP-AP992002B00AT00-WP0-1", "ValueTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpValue", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpValueSet(Key='NAME',DataSource='P')"}, "Unit": "0/00", "Key": "NAME", "DataSource": "P"}]}, "EnergyManagementType": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltpDataRecord", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltpDataRecordSet(ValueType='',FuelType='',EnergyManagementType='',DataSource='P')"}, "ValueType": "", "FuelType": "", "DataSource": "P"}]}, "DataSource": "P"}]}, "Typification": "WLTP_EU", "__metadata": {"type": "YISA8034_CONFIG_SRV.wltp_body", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/wltp_bodySet(Typification='WLTP_EU',DataSource='P')"}, "DataSource": "P"}]}, "ConfigModified": "", "CofferId": "aa36ae93-1053-465d-85be-79dcf211fef5", "Modeltype": "992110", "Modelyear": "R", "PorscheCode": "PRP1SJT0", "CommentsModified": "", "Importernr": "5000000", "Dealernr": "5000001", "ProspectKit": true, "Vguid": "051Mkmoc7jwpWMZin1jRO0", "Taxes": "10310.45", "TeqId": "0", "BpId": "*********", "FreeZOffer": "", "Vin": "", "Id": "", "ProductionDate": "0000-00-00", "VehicleOption": {"OptionExclusive": {"results": []}, "OptionPackage": {"results": []}, "OptionIndividual": {"results": [{"ImporterComment": "", "PagComment": "", "SortOrder": "", "Id": "EB1", "UpgradeTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.Individual", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('EB1')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('EB1')"}, "DealerComment": "", "ContentOf": ""}, {"ImporterComment": "", "PagComment": "", "SortOrder": "", "Id": "SZ8", "UpgradeTo": "", "__metadata": {"type": "YISA8034_CONFIG_SRV.Individual", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('SZ8')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/IndividualSet('SZ8')"}, "DealerComment": "", "ContentOf": ""}]}, "OptionLocal": {"results": []}, "Id": "1", "__metadata": {"type": "YISA8034_CONFIG_SRV.Option", "uri": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')", "id": "https://sapk18-lb.porsche.org:7212/order-mgmt-imp/saml/vehicle/v1/configurations/OptionSet('1')"}, "OptionCustomTailoring": {"results": []}, "OptionZoffer": {"results": []}}, "VehicleStatus": "V100", "LastUpdateDate": "20240227121952"}, "vehicle_configuration_onevms": null, "requested_dealer_delivery_date": "", "cnr": "C10", "importer_code": "CH", "dealer_name": "Porsche Zentrum Schinznach-Bad", "modified_at": "2020-01-22T12:00:00Z", "created_by": "pvms"}, {"purchase_intention_id": "TEST01235", "order_type": "KF", "shipping_code": null, "model_type": "992110", "model_year": "2024", "quota_month": "2024-02", "dealer_number": "5000001", "importer_number": "5000000", "seller": "300822", "business_partner_id": "0*********", "receiving_port_code": null, "vehicle_status_code": "V070", "vehicle_configuration_pvmsnext": null, "vehicle_configuration_onevms": {"ordered_options": [{"option_id": "option1"}]}, "requested_dealer_delivery_date": null, "cnr": "C10", "importer_code": "CH", "dealer_name": "Porsche Zentrum Schinznach-Bad", "modified_at": "2020-01-22T12:00:00Z", "created_by": "css"}, {"purchase_intention_id": "TEST01236", "order_type": "KY", "shipping_code": null, "model_type": "992110", "model_year": "2024", "quota_month": "2024-02", "dealer_number": "5000001", "importer_number": "5000000", "seller": "300822", "business_partner_id": "0*********", "receiving_port_code": null, "vehicle_status_code": "V070", "vehicle_configuration_pvmsnext": null, "vehicle_configuration_onevms": {"ordered_options": [{"option_id": "option1"}, {"option_id": "option2"}]}, "requested_dealer_delivery_date": null, "cnr": "C10", "importer_code": "CH", "dealer_name": "Porsche Zentrum Schinznach-Bad", "modified_at": "2020-01-22T12:00:00Z", "created_by": "css"}], "model-type-text-de": [{"iso_language_code": "de-DE", "ggid_year_model_type": "1_2024-982310", "language_code": "DE", "model_type": "982310", "model_text": "OD TEST12345 Deutsch", "model_year": 2024, "importer_number": "9690001", "cnr": "C21"}, {"iso_language_code": "de-DE", "ggid_year_model_type": "1_2024-MT00012", "language_code": "DE", "model_type": "MT00012", "model_text": "OD TEST12346 Deutsch", "model_year": 2024, "importer_number": "9690002", "cnr": "C21"}, {"iso_language_code": "de-DE", "ggid_year_model_type": "1_2030-MT0003", "language_code": "DE", "model_type": "MT0003", "model_text": "NCO IT2JX52B Deutsch", "model_year": 2030, "importer_number": "ItNcoImp", "cnr": "C21"}, {"iso_language_code": "de-DE", "ggid_year_model_type": "1_2024-MT0001", "language_code": "DE", "model_type": "MT0001", "model_text": "NCO DE0ZR0R8 Deutsch", "model_year": 2024, "importer_number": "9690000", "cnr": "C21"}], "model-type-text-en": [{"iso_language_code": "en-GB", "ggid_year_model_type": "1_2024-982310", "language_code": "DE", "model_type": "982310", "model_text": "OD TEST12345 English", "model_year": 2024, "importer_number": "9690001", "cnr": "C21"}, {"iso_language_code": "en-GB", "ggid_year_model_type": "1_2024-MT00012", "language_code": "DE", "model_type": "MT00012", "model_text": "OD TEST12346 English", "model_year": 2024, "importer_number": "9690002", "cnr": "C21"}, {"iso_language_code": "en-GB", "ggid_year_model_type": "1_2030-MT0003", "language_code": "EN", "model_type": "MT0003", "model_text": "NCO IT2JX52B English", "model_year": 2030, "importer_number": "ItNcoImp", "cnr": "C21"}, {"iso_language_code": "en-GB", "ggid_year_model_type": "1_2024-9YBAV1", "language_code": "EN", "model_type": "9YBAV1", "model_text": "Cayenne Turbo E-Hybrid Coupé with GT Package", "model_year": 2024, "importer_number": "9690000", "cnr": "C21"}], "onevms-status": [{"one_vms_status": "PP1300", "one_vms_error_status": "FBP500", "description_de": "Auftragsprüfungen durchgeführt 1 - Keine PAG Quote", "description_en": "Order Checks executed 1 - No PAG Quota", "order_changeable": true}, {"one_vms_status": "PP2000", "one_vms_error_status": "null", "description_de": "Auftragsprüfungen durchgeführt 2 - Keine PAG Quote", "description_en": "Order Checks executed 2 - No PAG Quota", "order_changeable": false}, {"one_vms_status": "OX1100", "one_vms_error_status": "null", "description_de": "Totalverlust gemeldet", "description_en": "Reported as total loss", "order_changeable": false}, {"one_vms_status": "IP0000", "one_vms_error_status": null, "description_de": "Fahrzeug in Produktion", "description_en": "Vehicle in Production", "order_changeable": false}, {"one_vms_status": "ID0000", "one_vms_error_status": null, "description_de": "Distribution iwas", "description_en": "Something distribution", "order_changeable": false}, {"one_vms_status": "ID5000", "one_vms_error_status": null, "description_de": "Fahrzeug an Händler übergeben", "description_en": "Vehicle handed over to Dealer", "order_changeable": false}, {"one_vms_status": "OC9000", "one_vms_error_status": null, "description_de": "Fahrzeug an Kunden ausgeliefert", "description_en": "Vehicle delivered to Customer", "order_changeable": false}, {"one_vms_status": "OC9000", "one_vms_error_status": null, "description_de": "Fahrzeug an Kunden ausgeliefert", "description_en": "Vehicle delivered to Customer", "order_changeable": false}, {"one_vms_status": "OX0000", "one_vms_error_status": null, "description_de": "Auftrag storniert", "description_en": "Order Cancelled", "order_changeable": false}], "rbam-permissions": {"data": [{"applicationRole": "ppn_approle_kas_importer_dev2", "applicationName": "cora", "app_permissions": ["create-new-car-order", "update-new-car-order", "multi-update-new-car-orders", "cancel-new-car-orders", "list-new-car-orders", "copy-new-car-order", "list-deals", "order-status-monitoring", "audit-trail", "deallocate-quota-new-car-orders", "report-total-loss", "revoke-total-loss", "nco-importer-transfer", "move-to-dealer-inventory", "remove-from-dealer-inventory", "nco-importer-transfer", "buy-sell-transfer"]}]}, "inbound-status-mapping": [{"id": "uuid1", "order_status_code": "PP1300", "error_status_code": "FBP500", "invoice_status_code": "PI0000", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"id": "uuid2", "order_status_code": "*", "error_status_code": "null", "invoice_status_code": "MI9000", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"id": "uuid3", "order_status_code": "OX1100", "error_status_code": "*", "invoice_status_code": "*", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"id": "uuid4", "order_status_code": "OX1100", "error_status_code": "null", "invoice_status_code": "MI9000", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"id": "uuid5", "order_status_code": "PP1300", "error_status_code": "*", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"id": "uuid6", "order_status_code": "null", "error_status_code": "null", "invoice_status_code": "null", "event": "convert-pi", "source_system": "cora_user", "target_event_handler": "convert-pi"}, {"id": "uuid7", "order_status_code": "*", "error_status_code": "*", "invoice_status_code": "*", "event": "move-to-inventory", "source_system": "cora_user", "target_event_handler": "move-to-dealer-inventory"}, {"id": "uuid8", "order_status_code": "*", "error_status_code": "*", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}]}