import { defineConfig } from 'vite';
import { configDefaults } from 'vitest/config';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
import analyze from 'rollup-plugin-analyzer';
import fs from 'node:fs';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    viteTsconfigPaths(),
    // {
    //   ...analyze({
    //     summaryOnly: false, // Set false to get full breakdown
    //     hideDeps: false,
    //     writeTo: (analysis) => {
    //       fs.writeFileSync('dist/bundle-analysis.txt', analysis);
    //     },
    //   }),
    //   enforce: 'post',
    //   apply: 'build',
    // },
    svgr({
      include: '**/*.svg?react',
    }),
  ],
  test: {
    environment: 'jsdom',
    include: ['**/*.test.ts', '**/*.test.tsx'],
    exclude: [...configDefaults.exclude],
    globals: true,
  },
});
