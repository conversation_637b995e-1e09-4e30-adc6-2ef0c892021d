@echo off

setlocal

rem Define platform_agnostic_sed based on the OS type
set platform_agnostic_sed=sed -i
if "%OS%" == "Windows_NT" (
  rem Assuming Windows has sed installed and behaves like GNU sed
  set platform_agnostic_sed=sed -i
)

rem Replace <!--PLACEHOLDER_FONT_LINKS--> in index.html
set placeholder=<!--PLACEHOLDER_FONT_LINKS-->
for /f "delims=" %%i in ('node -e "console.log(require('@porsche-design-system/components-js/partials').getFontLinks())"') do set partial=%%i
set partial=%placeholder%%partial%
set regex=%placeholder%.*

%platform_agnostic_sed% -E -e "s^%regex%^%partial%^" index.html

rem Replace <!--PLACEHOLDER_INITIAL_STYLES--> in index.html
set placeholder=<!--PLACEHOLDER_INITIAL_STYLES-->
for /f "delims=" %%i in ('node -e "console.log(require('@porsche-design-system/components-js/partials').getInitialStyles())"') do set partial=%%i
set partial=%placeholder%%partial%
set regex=%placeholder%.*

%platform_agnostic_sed% -E -e "s^%regex%^%partial%^" index.html

endlocal
