# Notes

https://designsystem.porsche.com/latest/start-coding/react/getting-started

# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

http://localhost:4000/orders/create?model_type=MT0001&model_year=2024&importer_number=9690000&dealer_number=9520011&cnr=C00&importer_code=9690001&quota_month=2024-08

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles <PERSON>act in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `source e2e.env && npm run e2e`

Or for single Testfile: `source e2e.env && npm run e2e -- --spec ./cypress/e2e/order-list.cy.ts`

This will execute the Cypress e2e tests. Before you can execute them, make sure you to create the 897501187430_PowerUser
Profile in your ~/.aws/credentials file (get credentials from Cloudcity AWS Access Portal):

export CYPRESS_TOKEN_SECRET_READ='get_this_from_secrets_manager'
export CYPRESS_TOKEN_SECRET_WRITE='get_this_from_secrets_manager'
export CYPRESS_USER_PASSWORD_READ='get_this_from_secrets_manager'
export CYPRESS_USER_PASSWORD_WRITE='get_this_from_secrets_manager'
export AWS_PROFILE=897501187430_PowerUser

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
