# CORA

## Description

The CORA Order Creation App provides a streamlined platform for order creation, leveraging the power of the AWS Cloud Development Kit (CDK) and is crafted with TypeScript. With a serverless backend powered by AWS Lambda and a frontend developed with React.js, it offers an all-inclusive solution for creating and deploying production-level order systems seamlessly.

## Deployed Instance

Access the dev instance of the application (https://cora-oc-dev.dpp.porsche.com). Please note that this link is limited to intranet access only.

## Features

- Example React application hosted on Amazon S3
- Amazon API Gateway for serving the application after authentication
- Lambda Authorizer for API Gateway with PPN
- Lambda function for handling code exchange in OAuth 2.0 with PPN
- Lambda function for handling user logout in OAuth 2.0 with PPN
- CDK Pipeline deployment for staging with CI/CD process
- Linter, prettier and husky integration for quality insurance

## Getting Started

Before you begin, make sure that you have the following prerequisites installed:

- Node.js v18.x or later
- npm v9.x or later
- AWS CLI v2.x or later
- AWS CDK v2.x or later
- TypeScript v5.x or later

### Clone the Repository

To get started with the CORA Order Creation, clone this repository to your local machine.

```bash
git clone https://cicd.skyway.porsche.com/DiTP/agile-release-trains/kas/onevms/cora/cora.git
cd cora-order-creation/infrastructure
```

## Installation

After cloning the repository, install the dependencies by running the following commands. Make sure you have AWS CLI Credentials configured on your local machine.

```bash
cd cora-order-creation/infrastructure
npm run co:login
npm install
```

This will install all necessary dependencies for the CDK and the React application.

## Usage

This project can be deployed as a developer playground or as a full functional cdk pipeline with different stages and the productuion environment

### Deploy the Application as Playground

#### Building Frontend

Before building the frontend, we first need to retrieve secrets from the `AWS RES ACCOUNT` for the Artifactory. These secrets are essential for the build process. Follow the steps below:

1. **Retrieve the Secrets (Workaround):**

   - Navigate to AWS Secrets Manager in the AWS console.
   - Look for the secrets labeled as `pipeline-secret`. The values are a json with the keys `USER_CERT` and `USER_CERT_KEY`.
   - Note down the values for both these secrets.

   Alternative: Consider employing the Porsche Highway Artifactory's formalized process for obtaining necessary credentials.

2. **Set Up Frontend Variables:**

   - Once you have the secrets, set them as parameters in your local system. To do so, navigate to `.npmrc.example` and copy it as `.npmrc` and add your credentials as described in the example:

     ```
     cert="-----BEGIN CERTIFICATE-----\nXXXX\nXXXX\n-----END CERTIFICATE-----"
     key="-----BEGIN PRIVATE KEY-----\nXXXX\nXXXX\n-----END PRIVATE KEY-----"
     ```

     Note: Make sure that you replace all `\\n` in your secrets with a single `\n`. They exist because of formatting reasons.

3. **Build the Frontend:**

   - With the `.npmrc` variables set, navigate to the Infrastructure repository on your local system.
   - Run the frontend build script by typing the following command into your terminal:
     ```
     npm run build:frontend
     ```
   - This script will build the frontend for you. Alternativly you can also run `npm run build` inside the frontend directory.

   Remember to keep your secrets safe and do not share them publicly.

#### Deploying the CORA Order Creation Application

1. **Prepare for deployment:**

   - Before deploying a feature stack for trial purposes, ensure that your stack is non-existent. Update the feature's name by modifying the `stage` variable found in `infrastructure/lib/context/config.feature.ts`.
   - The `infrastructure/lib/context/config.feature.ts` file contains all nessecary configurations for your environment. Make sure to update the `hostedZoneName` param in the file and configure it in the management console.

2. **Get AWS account credentials:**

   - Check if you have the AWS access credentials (Access Key, Secret Key) for all accounts related to the deployment.
   - Configure an AWS profile locally using the AWS Command Line Interface (CLI) or the AWS configuration file. Add the access credentials and assign them to a profile name.

3. **Deploy the Infrastructure:**

   - To deploy the infrastructure, navigate to the `infrastructure` directory on your local system.
   - Run the following commands to deploy the global, backend, and frontend stacks:

     ```
     cd infrastructure
     cdk deploy cora-oc-feature-name-placeholder/global
     cdk deploy cora-oc-feature-name-placeholder/backend
     cdk deploy cora-oc-feature-name-placeholder/frontend
     ```

     Note: When you rename your application, the names from the commands will change. You can list all available cdk stacks with the command `cdk ls`.

### Clean Up

To destroy all resources created by the CORA Order Creation Appplication, follow the steps below:

1. **Destroy the Deployed Resources:**

   - Navigate back to the `infrastructure` directory on your local system.
   - Run the following commands to destroy the global, backend, and frontend stacks:
     ```
     cdk destroy cora-oc-feature-name-placeholder/global
     cdk destroy cora-oc-feature-name-placeholder/backend
     cdk destroy cora-oc-feature-name-placeholder/frontend
     ```

2. **Create Hosted Zones:**

   - Login to AWS Console and navigate to Route 53.
   - Click on "Hosted zones", then "Create Hosted Zone".
   - Enter your Domain Name, select "Public Hosted Zone", and click "Create".
   - You'll receive four nameservers. Replace your current domain registrar's nameservers with these.
   - Allow up to 48 hours for DNS changes to propagate.

3. **Create Secrets in Resource Account:**

   - **Log in** to AWS Console, go to **AWS Secrets Manager**.
   - Click **"Store a new secret"**, select **"Other type of secrets"**.
   - Switch to the **"Plaintext"** tab, enter your JSON, e.g.:
     ```
     {
        "USER_CERT": "<your_certificate>",
        "USER_CERT_KEY": "<your_certificate_key>"
     }
     ```
   - Click **"Next"**, name the secret `cora-oc/pipeline-secret`, add an optional description.
   - Review settings, click **"Store"**. Your JSON secret is now stored.

   Note: Make sure that you replace all `\n` in your secrets with a double `\\n`. This is needed because of formatting reasons.

4. **Adjust Project Structure:**

   - Set the constants in `./infrastructure/lib/utils/constants.ts` according to the comments.
   - Set the stage constants in the folder `./infrastructure/lib/context/<your_stages>` for the stages according to the comments.
   - Adjust the bootstrap configuration in `./infrastructure/bootstrap`.
   - Ensure that the application qualifier for the bootstrap configuration in `./infrastructure/cdk.json` matches the value defined in your bootstrap script as **"@aws-cdk/core:bootstrapQualifier": "<YOUR_QUALIFIER>"**.

5. **Customize Your Project:**

   - Rename the file `./infrastructure/bin/cora-order-creation.ts` to the name of your app. Change the name of your project folder accordingly.

6. **Configure AWS Profiles:**

   - Check if you have the AWS access credentials (Access Key, Secret Key) for all accounts related to the deployment.
   - Configure an AWS profile locally using the AWS Command Line Interface (CLI) or the AWS configuration file. Add the access credentials and assign them to a profile name.

7. **GitLab and CodeCommit Sync:**

   - Make the initial commit and create a new repository in GitLab.
   - Configure the synchronization to CodeCommit.

8. **Execute AWS-CDK Commands:**
   - Prepare for deployment by running `sh bootstrap/bootstrap_res_account.sh`.
   - Prepare for deployment by running `sh bootstrap/bootstrap_stage_account.sh`.
   - Review the CloudFormation template with `cdk synth`.
   - Review the planned changes with `cdk diff`.
   - Carry out the deployment with `cdk deploy YOUR_REPOSITORY_NAME`.

## Run tests

### Unit tests

npm run test:unit

### Integration tests

Please only run integration tests against the development stage:
export AWS_PROFILE=274282531523_PA_DEVELOPER && npm run test:int

## Contribute

If you want to contribute to this project, please make sure to review the [Contributing Guidelines](CONTRIBUTING.md) and [Code of Conduct](CODE_OF_CONDUCT.md). (TODO)

## Contact & Support

For more help, please open an issue or submit a pull request.

## Acknowledgments

This project is created and maintained by Team Heartbeat.
